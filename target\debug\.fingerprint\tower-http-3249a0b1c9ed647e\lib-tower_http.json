{"rustc": 3062648155896360161, "features": "[\"cors\", \"default\", \"set-header\", \"tower\", \"trace\", \"tracing\", \"util\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 14850331575045365232, "profile": 15657897354478470176, "path": 17662294744169080437, "deps": [[784494742817713399, "tower_service", false, 15712931872650505409], [1906322745568073236, "pin_project_lite", false, 6270632195488232726], [3601586811267292532, "tower", false, 5325913171506022586], [4405182208873388884, "http", false, 10314525203348988149], [7620660491849607393, "futures_core", false, 17862481366668668731], [7712452662827335977, "tower_layer", false, 525772972084167676], [8606274917505247608, "tracing", false, 6846794494185651921], [8915503303801890683, "http_body", false, 3165026523775083827], [10629569228670356391, "futures_util", false, 6524679691479467602], [12848154260885479101, "bitflags", false, 1986056572718609536], [16066129441945555748, "bytes", false, 8148076815200774068], [18403471711646444616, "http_range_header", false, 917546238483728225]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-3249a0b1c9ed647e\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}