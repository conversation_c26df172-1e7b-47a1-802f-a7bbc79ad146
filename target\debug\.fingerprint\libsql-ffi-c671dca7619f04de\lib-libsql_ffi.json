{"rustc": 3062648155896360161, "features": "[]", "declared_features": "[\"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"libsql-checkpoint-only-full\", \"libsql-disable-checkpoint-downgrade\", \"multiple-ciphers\", \"preupdate_hook\", \"session\", \"sqlcipher\", \"sqlean-extension-crypto\", \"sqlean-extension-fuzzy\", \"sqlean-extension-math\", \"sqlean-extension-regexp\", \"sqlean-extension-stats\", \"sqlean-extension-text\", \"sqlean-extension-uuid\", \"sqlean-extensions\", \"unlock_notify\", \"wasm32-wasi-vfs\", \"wasmtime-bindings\", \"with-asan\"]", "target": 10574041547002569476, "profile": 15657897354478470176, "path": 12672567550075621324, "deps": [[6201363611984926128, "build_script_build", false, 11121314227009253767]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\libsql-ffi-c671dca7619f04de\\dep-lib-libsql_ffi", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}