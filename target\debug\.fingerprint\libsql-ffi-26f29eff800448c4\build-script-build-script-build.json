{"rustc": 3062648155896360161, "features": "[]", "declared_features": "[\"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"libsql-checkpoint-only-full\", \"libsql-disable-checkpoint-downgrade\", \"multiple-ciphers\", \"preupdate_hook\", \"session\", \"sqlcipher\", \"sqlean-extension-crypto\", \"sqlean-extension-fuzzy\", \"sqlean-extension-math\", \"sqlean-extension-regexp\", \"sqlean-extension-stats\", \"sqlean-extension-text\", \"sqlean-extension-uuid\", \"sqlean-extensions\", \"unlock_notify\", \"wasm32-wasi-vfs\", \"wasmtime-bindings\", \"with-asan\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 6321620778638456905, "deps": [[5359720273228040123, "bindgen", false, 15001691084049955751], [7499741813737603141, "cmake", false, 931528025746819981], [9293239362693504808, "glob", false, 7101696737909720479], [10268808379857177999, "cc", false, 6444211335541611221]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\libsql-ffi-26f29eff800448c4\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}