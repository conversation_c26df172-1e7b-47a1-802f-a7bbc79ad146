static KEYWORDS: ::phf::Map<&'static UncasedStr, TokenType> = 
::phf::Map {
    key: 7485420634051515786,
    disps: &[
        (0, 0),
        (0, 6),
        (0, 24),
        (7, 50),
        (1, 11),
        (0, 0),
        (2, 76),
        (3, 0),
        (0, 39),
        (0, 84),
        (0, 68),
        (0, 41),
        (0, 1),
        (0, 14),
        (1, 2),
        (0, 42),
        (2, 134),
        (20, 9),
        (0, 12),
        (0, 0),
        (96, 87),
        (3, 86),
        (1, 91),
        (0, 32),
        (0, 11),
        (9, 92),
        (2, 120),
        (0, 15),
        (0, 0),
        (1, 22),
    ],
    entries: &[
        (UncasedStr::new("DEFERRED"), TokenType::TK_DEFERRED),
        (UncasedStr::new("SET"), TokenType::TK_SET),
        (UncasedStr::new("INSERT"), TokenType::TK_INSERT),
        (UncasedStr::new("WINDOW"), TokenType::TK_WINDOW),
        (UncasedStr::new("GENERATED"), TokenType::TK_GENERATED),
        (UncasedStr::new("WHERE"), TokenType::TK_WHERE),
        (UncasedStr::new("RETURNING"), TokenType::TK_RETURNING),
        (UncasedStr::new("USING"), TokenType::TK_USING),
        (UncasedStr::new("PRECEDING"), TokenType::TK_PRECEDING),
        (UncasedStr::new("NATURAL"), TokenType::TK_JOIN_KW),
        (UncasedStr::new("GROUP"), TokenType::TK_GROUP),
        (UncasedStr::new("HAVING"), TokenType::TK_HAVING),
        (UncasedStr::new("INTERSECT"), TokenType::TK_INTERSECT),
        (UncasedStr::new("RECURSIVE"), TokenType::TK_RECURSIVE),
        (UncasedStr::new("THEN"), TokenType::TK_THEN),
        (UncasedStr::new("ASC"), TokenType::TK_ASC),
        (UncasedStr::new("WITHOUT"), TokenType::TK_WITHOUT),
        (UncasedStr::new("AUTOINCREMENT"), TokenType::TK_AUTOINCR),
        (UncasedStr::new("OR"), TokenType::TK_OR),
        (UncasedStr::new("DEFAULT"), TokenType::TK_DEFAULT),
        (UncasedStr::new("VALUES"), TokenType::TK_VALUES),
        (UncasedStr::new("QUERY"), TokenType::TK_QUERY),
        (UncasedStr::new("FOLLOWING"), TokenType::TK_FOLLOWING),
        (UncasedStr::new("DESC"), TokenType::TK_DESC),
        (UncasedStr::new("DEFERRABLE"), TokenType::TK_DEFERRABLE),
        (UncasedStr::new("NO"), TokenType::TK_NO),
        (UncasedStr::new("PLAN"), TokenType::TK_PLAN),
        (UncasedStr::new("COMMIT"), TokenType::TK_COMMIT),
        (UncasedStr::new("UNBOUNDED"), TokenType::TK_UNBOUNDED),
        (UncasedStr::new("REINDEX"), TokenType::TK_REINDEX),
        (UncasedStr::new("TO"), TokenType::TK_TO),
        (UncasedStr::new("GLOB"), TokenType::TK_LIKE_KW),
        (UncasedStr::new("MATERIALIZED"), TokenType::TK_MATERIALIZED),
        (UncasedStr::new("LAST"), TokenType::TK_LAST),
        (UncasedStr::new("NULLS"), TokenType::TK_NULLS),
        (UncasedStr::new("VIEW"), TokenType::TK_VIEW),
        (UncasedStr::new("CASE"), TokenType::TK_CASE),
        (UncasedStr::new("ORDER"), TokenType::TK_ORDER),
        (UncasedStr::new("MATCH"), TokenType::TK_MATCH),
        (UncasedStr::new("PRIMARY"), TokenType::TK_PRIMARY),
        (UncasedStr::new("ALL"), TokenType::TK_ALL),
        (UncasedStr::new("FIRST"), TokenType::TK_FIRST),
        (UncasedStr::new("ROW"), TokenType::TK_ROW),
        (UncasedStr::new("RELEASE"), TokenType::TK_RELEASE),
        (UncasedStr::new("CHECK"), TokenType::TK_CHECK),
        (UncasedStr::new("RESTRICT"), TokenType::TK_RESTRICT),
        (UncasedStr::new("REPLACE"), TokenType::TK_REPLACE),
        (UncasedStr::new("NULL"), TokenType::TK_NULL),
        (UncasedStr::new("CURRENT"), TokenType::TK_CURRENT),
        (UncasedStr::new("REFERENCES"), TokenType::TK_REFERENCES),
        (UncasedStr::new("CROSS"), TokenType::TK_JOIN_KW),
        (UncasedStr::new("FILTER"), TokenType::TK_FILTER),
        (UncasedStr::new("IN"), TokenType::TK_IN),
        (UncasedStr::new("RENAME"), TokenType::TK_RENAME),
        (UncasedStr::new("OVER"), TokenType::TK_OVER),
        (UncasedStr::new("REGEXP"), TokenType::TK_LIKE_KW),
        (UncasedStr::new("EACH"), TokenType::TK_EACH),
        (UncasedStr::new("ON"), TokenType::TK_ON),
        (UncasedStr::new("EXISTS"), TokenType::TK_EXISTS),
        (UncasedStr::new("NOT"), TokenType::TK_NOT),
        (UncasedStr::new("CAST"), TokenType::TK_CAST),
        (UncasedStr::new("RIGHT"), TokenType::TK_JOIN_KW),
        (UncasedStr::new("TEMPORARY"), TokenType::TK_TEMP),
        (UncasedStr::new("BEFORE"), TokenType::TK_BEFORE),
        (UncasedStr::new("ALTER"), TokenType::TK_ALTER),
        (UncasedStr::new("ROWS"), TokenType::TK_ROWS),
        (UncasedStr::new("INDEX"), TokenType::TK_INDEX),
        (UncasedStr::new("RANGE"), TokenType::TK_RANGE),
        (UncasedStr::new("TRANSACTION"), TokenType::TK_TRANSACTION),
        (UncasedStr::new("CREATE"), TokenType::TK_CREATE),
        (UncasedStr::new("BETWEEN"), TokenType::TK_BETWEEN),
        (UncasedStr::new("UNION"), TokenType::TK_UNION),
        (UncasedStr::new("DROP"), TokenType::TK_DROP),
        (UncasedStr::new("TIES"), TokenType::TK_TIES),
        (UncasedStr::new("INITIALLY"), TokenType::TK_INITIALLY),
        (UncasedStr::new("INTO"), TokenType::TK_INTO),
        (UncasedStr::new("CURRENT_DATE"), TokenType::TK_CTIME_KW),
        (UncasedStr::new("UNIQUE"), TokenType::TK_UNIQUE),
        (UncasedStr::new("SAVEPOINT"), TokenType::TK_SAVEPOINT),
        (UncasedStr::new("GROUPS"), TokenType::TK_GROUPS),
        (UncasedStr::new("SELECT"), TokenType::TK_SELECT),
        (UncasedStr::new("END"), TokenType::TK_END),
        (UncasedStr::new("AND"), TokenType::TK_AND),
        (UncasedStr::new("DISTINCT"), TokenType::TK_DISTINCT),
        (UncasedStr::new("ALWAYS"), TokenType::TK_ALWAYS),
        (UncasedStr::new("BEGIN"), TokenType::TK_BEGIN),
        (UncasedStr::new("FOREIGN"), TokenType::TK_FOREIGN),
        (UncasedStr::new("ESCAPE"), TokenType::TK_ESCAPE),
        (UncasedStr::new("WHEN"), TokenType::TK_WHEN),
        (UncasedStr::new("FULL"), TokenType::TK_JOIN_KW),
        (UncasedStr::new("OF"), TokenType::TK_OF),
        (UncasedStr::new("ISNULL"), TokenType::TK_ISNULL),
        (UncasedStr::new("IS"), TokenType::TK_IS),
        (UncasedStr::new("CONFLICT"), TokenType::TK_CONFLICT),
        (UncasedStr::new("ROLLBACK"), TokenType::TK_ROLLBACK),
        (UncasedStr::new("DO"), TokenType::TK_DO),
        (UncasedStr::new("READONLY"), TokenType::TK_READONLY),
        (UncasedStr::new("CURRENT_TIMESTAMP"), TokenType::TK_CTIME_KW),
        (UncasedStr::new("TEMP"), TokenType::TK_TEMP),
        (UncasedStr::new("DATABASE"), TokenType::TK_DATABASE),
        (UncasedStr::new("OFFSET"), TokenType::TK_OFFSET),
        (UncasedStr::new("CONSTRAINT"), TokenType::TK_CONSTRAINT),
        (UncasedStr::new("PARTITION"), TokenType::TK_PARTITION),
        (UncasedStr::new("INSTEAD"), TokenType::TK_INSTEAD),
        (UncasedStr::new("LEFT"), TokenType::TK_JOIN_KW),
        (UncasedStr::new("EXCLUDE"), TokenType::TK_EXCLUDE),
        (UncasedStr::new("PRAGMA"), TokenType::TK_PRAGMA),
        (UncasedStr::new("JOIN"), TokenType::TK_JOIN),
        (UncasedStr::new("DETACH"), TokenType::TK_DETACH),
        (UncasedStr::new("LIKE"), TokenType::TK_LIKE_KW),
        (UncasedStr::new("CASCADE"), TokenType::TK_CASCADE),
        (UncasedStr::new("OUTER"), TokenType::TK_JOIN_KW),
        (UncasedStr::new("TABLE"), TokenType::TK_TABLE),
        (UncasedStr::new("ELSE"), TokenType::TK_ELSE),
        (UncasedStr::new("ABORT"), TokenType::TK_ABORT),
        (UncasedStr::new("WITH"), TokenType::TK_WITH),
        (UncasedStr::new("AFTER"), TokenType::TK_AFTER),
        (UncasedStr::new("EXPLAIN"), TokenType::TK_EXPLAIN),
        (UncasedStr::new("COLLATE"), TokenType::TK_COLLATE),
        (UncasedStr::new("IF"), TokenType::TK_IF),
        (UncasedStr::new("INDEXED"), TokenType::TK_INDEXED),
        (UncasedStr::new("VACUUM"), TokenType::TK_VACUUM),
        (UncasedStr::new("OTHERS"), TokenType::TK_OTHERS),
        (UncasedStr::new("COLUMN"), TokenType::TK_COLUMNKW),
        (UncasedStr::new("LIMIT"), TokenType::TK_LIMIT),
        (UncasedStr::new("CURRENT_TIME"), TokenType::TK_CTIME_KW),
        (UncasedStr::new("EXCLUSIVE"), TokenType::TK_EXCLUSIVE),
        (UncasedStr::new("IGNORE"), TokenType::TK_IGNORE),
        (UncasedStr::new("RAISE"), TokenType::TK_RAISE),
        (UncasedStr::new("FAIL"), TokenType::TK_FAIL),
        (UncasedStr::new("ADD"), TokenType::TK_ADD),
        (UncasedStr::new("KEY"), TokenType::TK_KEY),
        (UncasedStr::new("INNER"), TokenType::TK_JOIN_KW),
        (UncasedStr::new("EXCEPT"), TokenType::TK_EXCEPT),
        (UncasedStr::new("ATTACH"), TokenType::TK_ATTACH),
        (UncasedStr::new("UPDATE"), TokenType::TK_UPDATE),
        (UncasedStr::new("NOTHING"), TokenType::TK_NOTHING),
        (UncasedStr::new("NOTNULL"), TokenType::TK_NOTNULL),
        (UncasedStr::new("AS"), TokenType::TK_AS),
        (UncasedStr::new("VIRTUAL"), TokenType::TK_VIRTUAL),
        (UncasedStr::new("IMMEDIATE"), TokenType::TK_IMMEDIATE),
        (UncasedStr::new("TRIGGER"), TokenType::TK_TRIGGER),
        (UncasedStr::new("ANALYZE"), TokenType::TK_ANALYZE),
        (UncasedStr::new("ACTION"), TokenType::TK_ACTION),
        (UncasedStr::new("BY"), TokenType::TK_BY),
        (UncasedStr::new("FROM"), TokenType::TK_FROM),
        (UncasedStr::new("FOR"), TokenType::TK_FOR),
        (UncasedStr::new("DELETE"), TokenType::TK_DELETE),
    ],
};