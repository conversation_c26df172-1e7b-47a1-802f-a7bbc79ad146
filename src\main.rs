use anyhow::Result;
use crossbeam_channel::unbounded;
use headless_chrome::{<PERSON><PERSON><PERSON>, LaunchOptions, protocol::cdp::Page::CaptureScreenshotFormatOption};
use libsql::{<PERSON><PERSON><PERSON>, params};
use std::{collections::HashSet, fs};
use url::Url;

static NUM_THREADS: usize = 2;

fn query(browser: Browser, url: String) -> Result<Vec<String>> {
    // Open the requested url
    let tab = browser.new_tab()?;

    tab.navigate_to(url.as_str())?;
    tab.wait_until_navigated()?;

    // Take a screenshot
    let img_data =
        tab.capture_screenshot(CaptureScreenshotFormatOption::Webp, Some(75), None, true)?;

    // Save the screenshot
    let parsed_url = Url::parse(&url)?;
    let domain = parsed_url.domain().unwrap_or("unknown");

    let path = urlencoding::encode(parsed_url.path()).to_string();
    let screenshot_dir = format!("screenshots/{}", domain);
    fs::create_dir_all(&screenshot_dir)?;
    let screenshot_path = format!("{}/{}.webp", screenshot_dir, path);
    fs::write(screenshot_path, &img_data)?;

    // Extract links
    let element = tab.wait_for_element("html")?;

    let remote_object = element.call_js_fn(
        r#"
        function(...args) {
            const linkElements = Array.from(document.body.querySelectorAll("[href]"));
            return JSON.stringify(linkElements.map((el) => el.href));
        }
        "#,
        vec![],
        false,
    )?;

    let links: Vec<String> = serde_json::from_str(remote_object.value.unwrap().as_str().unwrap())?;

    // Close the tab
    tab.close_target()?;

    // Return the links
    Ok(links)
}

#[tokio::main]
async fn main() -> Result<()> {
    // Create a local database
    let db = Builder::new_local("local.db").build().await.unwrap();
    let conn = db.connect()?;

    // Create a table to store the visited links
    conn.execute(
        "CREATE TABLE IF NOT EXISTS visited (url TEXT PRIMARY KEY)",
        (),
    )
    .await
    .unwrap();

    // Create channels for communication between threads
    let (work_tx, work_rx) = unbounded::<String>();
    let (result_tx, result_rx) = unbounded::<Vec<String>>();
    let mut children = Vec::new();

    for _ in 0..NUM_THREADS {
        let work_rx = work_rx.clone();
        let result_tx = result_tx.clone();

        let child = std::thread::spawn(move || {
            let browser = Browser::new(LaunchOptions {
                headless: false,

                ..Default::default()
            })
            .expect("Failed to launch browser");

            while let Ok(url) = work_rx.recv() {
                if let Ok(links) = query(browser.clone(), url) {
                    result_tx.send(links).unwrap();
                }
            }
        });

        children.push(child);
    }

    let start_url = "https://example.com".to_string();
    work_tx.send(start_url.clone())?;

    // Get visited links from the database
    let mut visited = HashSet::new();
    visited.insert(start_url.clone());

    let mut rows = conn.query("SELECT url FROM visited", ()).await.unwrap();

    while let Some(row) = rows.next().await.unwrap() {
        visited.insert(row.get::<String>(0).unwrap());
    }

    while let Ok(links) = result_rx.recv() {
        for link in links {
            if !visited.contains(&link) {
                visited.insert(link.clone());

                conn.execute(
                    "INSERT INTO visited (url) VALUES (?)",
                    params![link.clone()],
                )
                .await
                .unwrap();

                work_tx.send(link)?;
            }
        }
    }

    // Join the threads
    drop(work_tx);
    drop(result_tx);

    for child in children {
        child.join().unwrap();
    }

    Ok(())
}
