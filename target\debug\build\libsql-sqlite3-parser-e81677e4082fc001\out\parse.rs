/* This file is automatically generated by <PERSON> from input grammar
** source file "src/parser/parse.y". */
/*
** 2001-09-15
**
** The author disclaims copyright to this source code.  In place of
** a legal notice, here is a blessing:
**
**    May you do good and not evil.
**    May you find forgiveness for yourself and forgive others.
**    May you share freely, never taking more than you give.
**
*************************************************************************
** This file contains SQLite's SQL parser.
**
** The canonical source code to this file ("parse.y") is a Lemon grammar 
** file that specifies the input grammar and actions to take while parsing.
** That input file is processed by <PERSON> to generate a C-language 
** implementation of a parser for the given grammer.  You might be reading
** this comment as part of the translated C-code.  Edits should be made
** to the original parse.y sources.
*/
//line 63 "src/parser/parse.y"

use crate::parser::ast::*;
use crate::parser::{Context, ParserError};
use crate::dialect::{from_token, Token, TokenType};
use log::{trace, error, log_enabled};

#[allow(non_camel_case_types)]
type sqlite3ParserError = crate::parser::ParserError;
//line 515 "src/parser/parse.y"

//line 907 "src/parser/parse.y"

//line 990 "src/parser/parse.y"

//line 1120 "src/parser/parse.y"

/**************** End of %include directives **********************************/
/* These constants specify the various numeric values for terminal symbols.
***************** Begin token definitions *************************************/
/**************** End token definitions ***************************************/

/* The next sections is a series of control #defines.
** various aspects of the generated parser.
**    YYCODETYPE         is the data type used to store the integer codes
**                       that represent terminal and non-terminal symbols.
**                       "unsigned char" is used if there are fewer than
**                       256 symbols.  Larger types otherwise.
**    YYNOCODE           is a number of type YYCODETYPE that is not used for
**                       any terminal or nonterminal symbol.
**    YYFALLBACK         If defined, this indicates that one or more tokens
**                       (also known as: "terminal symbols") have fall-back
**                       values which should be used if the original symbol
**                       would not parse.  This permits keywords to sometimes
**                       be used as identifiers, for example.
**    YYACTIONTYPE       is the data type used for "action codes" - numbers
**                       that indicate what to do in response to the next
**                       token.
**    sqlite3ParserTOKENTYPE     is the data type used for minor type for terminal
**                       symbols.  Background: A "minor type" is a semantic
**                       value associated with a terminal or non-terminal
**                       symbols.  For example, for an "ID" terminal symbol,
**                       the minor type might be the name of the identifier.
**                       Each non-terminal can have a different minor type.
**                       Terminal symbols all have the same minor type, though.
**                       This macros defines the minor type for terminal
**                       symbols.
**    YYMINORTYPE        is the data type used for all minor types.
**                       This is typically a union of many types, one of
**                       which is sqlite3ParserTOKENTYPE.  The entry in the union
**                       for terminal symbols is called "yy0".
**    YYSTACKDEPTH       is the maximum depth of the parser's stack.  If
**                       zero the stack is dynamically sized using realloc()
**    YYERRORSYMBOL      is the code number of the error symbol.  If not
**                       defined, then do no error processing.
**    YYNSTATE           the combined number of states.
**    YYNRULE            the number of rules in the grammar
**    YYNTOKEN           Number of terminal symbols
**    YY_MAX_SHIFT       Maximum value for shift actions
**    YY_MIN_SHIFTREDUCE Minimum value for shift-reduce actions
**    YY_MAX_SHIFTREDUCE Maximum value for shift-reduce actions
**    YY_ERROR_ACTION    The yy_action[] code for syntax error
**    YY_ACCEPT_ACTION   The yy_action[] code for accept
**    YY_NO_ACTION       The yy_action[] code for no-op
**    YY_MIN_REDUCE      Minimum value for reduce actions
**    YY_MAX_REDUCE      Maximum value for reduce actions
*/
/************* Begin control #defines *****************************************/
#[allow(non_camel_case_types)]
pub type YYCODETYPE = u16; // unsigned
const YYNOCODE: YYCODETYPE = 294;
#[allow(non_camel_case_types)]
type YYACTIONTYPE = u16; // unsigned
const YYWILDCARD: YYCODETYPE = 102;
#[allow(non_camel_case_types)]
type sqlite3ParserTOKENTYPE = Token;
#[allow(non_camel_case_types)]
enum YYMINORTYPE {
    yyinit(),
    yy0(sqlite3ParserTOKENTYPE),
    yy4(Option<FrameClause>),
    yy13(Window),
    yy19(Option<Vec<SortedColumn>>),
    yy25(Option<Vec<Name>>),
    yy33(Vec<TriggerCmd>),
    yy47(TriggerEvent),
    yy59(Option<TriggerTime>),
    yy68(Option<GroupBy>),
    yy77(Stmt),
    yy84(ColumnConstraint),
    yy85(Option<DeferSubclause>),
    yy97(Vec<CommonTableExpr>),
    yy105(Option<FrameExclude>),
    yy106(Vec<ResultColumn>),
    yy113(Over),
    yy143(Option<Vec<IndexedColumn>>),
    yy162(Vec<Name>),
    yy166(CompoundOperator),
    yy173(bool),
    yy181(Option<Indexed>),
    yy182(Option<Vec<NamedTableConstraint>>),
    yy201(TableOptions),
    yy203(CreateTableBody),
    yy207(ResolveType),
    yy208(Vec<ColumnDefinition>),
    yy213(Select),
    yy214(FrameExclude),
    yy216(FrameBound),
    yy220(String),
    yy221(FrameMode),
    yy222(Name),
    yy238(RefAct),
    yy254(Option<ResolveType>),
    yy256(WindowDef),
    yy265(Option<Vec<ResultColumn>>),
    yy276(Option<TransactionType>),
    yy277(NamedColumnConstraint),
    yy280(Option<With>),
    yy296(Option<SortOrder>),
    yy300(Option<InitDeferredPred>),
    yy305(Vec<NamedColumnConstraint>),
    yy312(Vec<Expr>),
    yy314(FromClause),
    yy322(Option<NullsOrder>),
    yy329(Option<Name>),
    yy331(Option<Limit>),
    yy343((bool, LikeOperator)),
    yy353(FunctionTail),
    yy360(Vec<IndexedColumn>),
    yy362(Option<Type>),
    yy364(Option<As>),
    yy394(DeferSubclause),
    yy404(Vec<RefArg>),
    yy415(Option<Distinctness>),
    yy419(Option<Vec<Expr>>),
    yy432(RefArg),
    yy439(NamedTableConstraint),
    yy444((Name, Option<Type>)),
    yy463(OneSelect),
    yy468(Vec<Vec<Expr>>),
    yy472(Vec<WindowDef>),
    yy473(Vec<(Expr, Expr)>),
    yy474(QualifiedName),
    yy479(Option<Expr>),
    yy480(Expr),
    yy485(Option<FromClause>),
    yy496((Option<Expr>, Option<Vec<ResultColumn>>)),
    yy503(SelectBody),
    yy504((Option<Upsert>, Option<Vec<ResultColumn>>)),
    yy517(Vec<Set>),
    yy520(Materialized),
    yy536(Vec<SortedColumn>),
    yy557(CommonTableExpr),
    yy559(Vec<NamedTableConstraint>),
    yy569(TriggerCmd),
    yy577(JoinOperator),
    yy578(Option<JoinConstraint>),
}
impl Default for YYMINORTYPE {
    fn default() -> YYMINORTYPE {
        YYMINORTYPE::yyinit()
    }
}
impl yyStackEntry {
    fn yy0(self) -> sqlite3ParserTOKENTYPE {
        if let YYMINORTYPE::yy0(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy4(self) -> Option<FrameClause> {
        if let YYMINORTYPE::yy4(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy4_ref(&mut self) -> &mut Option<FrameClause> {
        if let YYMINORTYPE::yy4(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy13(self) -> Window {
        if let YYMINORTYPE::yy13(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy13_ref(&mut self) -> &mut Window {
        if let YYMINORTYPE::yy13(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy19(self) -> Option<Vec<SortedColumn>> {
        if let YYMINORTYPE::yy19(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy19_ref(&mut self) -> &mut Option<Vec<SortedColumn>> {
        if let YYMINORTYPE::yy19(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy25(self) -> Option<Vec<Name>> {
        if let YYMINORTYPE::yy25(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy25_ref(&mut self) -> &mut Option<Vec<Name>> {
        if let YYMINORTYPE::yy25(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy33(self) -> Vec<TriggerCmd> {
        if let YYMINORTYPE::yy33(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy33_ref(&mut self) -> &mut Vec<TriggerCmd> {
        if let YYMINORTYPE::yy33(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy47(self) -> TriggerEvent {
        if let YYMINORTYPE::yy47(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy47_ref(&mut self) -> &mut TriggerEvent {
        if let YYMINORTYPE::yy47(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy59(self) -> Option<TriggerTime> {
        if let YYMINORTYPE::yy59(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy59_ref(&mut self) -> &mut Option<TriggerTime> {
        if let YYMINORTYPE::yy59(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy68(self) -> Option<GroupBy> {
        if let YYMINORTYPE::yy68(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy68_ref(&mut self) -> &mut Option<GroupBy> {
        if let YYMINORTYPE::yy68(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy77(self) -> Stmt {
        if let YYMINORTYPE::yy77(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy77_ref(&mut self) -> &mut Stmt {
        if let YYMINORTYPE::yy77(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy84(self) -> ColumnConstraint {
        if let YYMINORTYPE::yy84(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy84_ref(&mut self) -> &mut ColumnConstraint {
        if let YYMINORTYPE::yy84(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy85(self) -> Option<DeferSubclause> {
        if let YYMINORTYPE::yy85(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy85_ref(&mut self) -> &mut Option<DeferSubclause> {
        if let YYMINORTYPE::yy85(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy97(self) -> Vec<CommonTableExpr> {
        if let YYMINORTYPE::yy97(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy97_ref(&mut self) -> &mut Vec<CommonTableExpr> {
        if let YYMINORTYPE::yy97(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy105(self) -> Option<FrameExclude> {
        if let YYMINORTYPE::yy105(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy105_ref(&mut self) -> &mut Option<FrameExclude> {
        if let YYMINORTYPE::yy105(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy106(self) -> Vec<ResultColumn> {
        if let YYMINORTYPE::yy106(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy106_ref(&mut self) -> &mut Vec<ResultColumn> {
        if let YYMINORTYPE::yy106(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy113(self) -> Over {
        if let YYMINORTYPE::yy113(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy113_ref(&mut self) -> &mut Over {
        if let YYMINORTYPE::yy113(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy143(self) -> Option<Vec<IndexedColumn>> {
        if let YYMINORTYPE::yy143(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy143_ref(&mut self) -> &mut Option<Vec<IndexedColumn>> {
        if let YYMINORTYPE::yy143(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy162(self) -> Vec<Name> {
        if let YYMINORTYPE::yy162(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy162_ref(&mut self) -> &mut Vec<Name> {
        if let YYMINORTYPE::yy162(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy166(self) -> CompoundOperator {
        if let YYMINORTYPE::yy166(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy166_ref(&mut self) -> &mut CompoundOperator {
        if let YYMINORTYPE::yy166(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy173(self) -> bool {
        if let YYMINORTYPE::yy173(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy173_ref(&mut self) -> &mut bool {
        if let YYMINORTYPE::yy173(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy181(self) -> Option<Indexed> {
        if let YYMINORTYPE::yy181(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy181_ref(&mut self) -> &mut Option<Indexed> {
        if let YYMINORTYPE::yy181(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy182(self) -> Option<Vec<NamedTableConstraint>> {
        if let YYMINORTYPE::yy182(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy182_ref(&mut self) -> &mut Option<Vec<NamedTableConstraint>> {
        if let YYMINORTYPE::yy182(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy201(self) -> TableOptions {
        if let YYMINORTYPE::yy201(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy201_ref(&mut self) -> &mut TableOptions {
        if let YYMINORTYPE::yy201(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy203(self) -> CreateTableBody {
        if let YYMINORTYPE::yy203(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy203_ref(&mut self) -> &mut CreateTableBody {
        if let YYMINORTYPE::yy203(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy207(self) -> ResolveType {
        if let YYMINORTYPE::yy207(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy207_ref(&mut self) -> &mut ResolveType {
        if let YYMINORTYPE::yy207(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy208(self) -> Vec<ColumnDefinition> {
        if let YYMINORTYPE::yy208(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy208_ref(&mut self) -> &mut Vec<ColumnDefinition> {
        if let YYMINORTYPE::yy208(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy213(self) -> Select {
        if let YYMINORTYPE::yy213(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy213_ref(&mut self) -> &mut Select {
        if let YYMINORTYPE::yy213(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy214(self) -> FrameExclude {
        if let YYMINORTYPE::yy214(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy214_ref(&mut self) -> &mut FrameExclude {
        if let YYMINORTYPE::yy214(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy216(self) -> FrameBound {
        if let YYMINORTYPE::yy216(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy216_ref(&mut self) -> &mut FrameBound {
        if let YYMINORTYPE::yy216(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy220(self) -> String {
        if let YYMINORTYPE::yy220(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy220_ref(&mut self) -> &mut String {
        if let YYMINORTYPE::yy220(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy221(self) -> FrameMode {
        if let YYMINORTYPE::yy221(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy221_ref(&mut self) -> &mut FrameMode {
        if let YYMINORTYPE::yy221(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy222(self) -> Name {
        if let YYMINORTYPE::yy222(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy222_ref(&mut self) -> &mut Name {
        if let YYMINORTYPE::yy222(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy238(self) -> RefAct {
        if let YYMINORTYPE::yy238(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy238_ref(&mut self) -> &mut RefAct {
        if let YYMINORTYPE::yy238(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy254(self) -> Option<ResolveType> {
        if let YYMINORTYPE::yy254(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy254_ref(&mut self) -> &mut Option<ResolveType> {
        if let YYMINORTYPE::yy254(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy256(self) -> WindowDef {
        if let YYMINORTYPE::yy256(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy256_ref(&mut self) -> &mut WindowDef {
        if let YYMINORTYPE::yy256(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy265(self) -> Option<Vec<ResultColumn>> {
        if let YYMINORTYPE::yy265(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy265_ref(&mut self) -> &mut Option<Vec<ResultColumn>> {
        if let YYMINORTYPE::yy265(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy276(self) -> Option<TransactionType> {
        if let YYMINORTYPE::yy276(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy276_ref(&mut self) -> &mut Option<TransactionType> {
        if let YYMINORTYPE::yy276(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy277(self) -> NamedColumnConstraint {
        if let YYMINORTYPE::yy277(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy277_ref(&mut self) -> &mut NamedColumnConstraint {
        if let YYMINORTYPE::yy277(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy280(self) -> Option<With> {
        if let YYMINORTYPE::yy280(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy280_ref(&mut self) -> &mut Option<With> {
        if let YYMINORTYPE::yy280(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy296(self) -> Option<SortOrder> {
        if let YYMINORTYPE::yy296(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy296_ref(&mut self) -> &mut Option<SortOrder> {
        if let YYMINORTYPE::yy296(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy300(self) -> Option<InitDeferredPred> {
        if let YYMINORTYPE::yy300(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy300_ref(&mut self) -> &mut Option<InitDeferredPred> {
        if let YYMINORTYPE::yy300(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy305(self) -> Vec<NamedColumnConstraint> {
        if let YYMINORTYPE::yy305(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy305_ref(&mut self) -> &mut Vec<NamedColumnConstraint> {
        if let YYMINORTYPE::yy305(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy312(self) -> Vec<Expr> {
        if let YYMINORTYPE::yy312(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy312_ref(&mut self) -> &mut Vec<Expr> {
        if let YYMINORTYPE::yy312(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy314(self) -> FromClause {
        if let YYMINORTYPE::yy314(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy314_ref(&mut self) -> &mut FromClause {
        if let YYMINORTYPE::yy314(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy322(self) -> Option<NullsOrder> {
        if let YYMINORTYPE::yy322(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy322_ref(&mut self) -> &mut Option<NullsOrder> {
        if let YYMINORTYPE::yy322(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy329(self) -> Option<Name> {
        if let YYMINORTYPE::yy329(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy329_ref(&mut self) -> &mut Option<Name> {
        if let YYMINORTYPE::yy329(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy331(self) -> Option<Limit> {
        if let YYMINORTYPE::yy331(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy331_ref(&mut self) -> &mut Option<Limit> {
        if let YYMINORTYPE::yy331(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy343(self) -> (bool, LikeOperator) {
        if let YYMINORTYPE::yy343(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy343_ref(&mut self) -> &mut (bool, LikeOperator) {
        if let YYMINORTYPE::yy343(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy353(self) -> FunctionTail {
        if let YYMINORTYPE::yy353(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy353_ref(&mut self) -> &mut FunctionTail {
        if let YYMINORTYPE::yy353(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy360(self) -> Vec<IndexedColumn> {
        if let YYMINORTYPE::yy360(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy360_ref(&mut self) -> &mut Vec<IndexedColumn> {
        if let YYMINORTYPE::yy360(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy362(self) -> Option<Type> {
        if let YYMINORTYPE::yy362(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy362_ref(&mut self) -> &mut Option<Type> {
        if let YYMINORTYPE::yy362(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy364(self) -> Option<As> {
        if let YYMINORTYPE::yy364(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy364_ref(&mut self) -> &mut Option<As> {
        if let YYMINORTYPE::yy364(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy394(self) -> DeferSubclause {
        if let YYMINORTYPE::yy394(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy394_ref(&mut self) -> &mut DeferSubclause {
        if let YYMINORTYPE::yy394(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy404(self) -> Vec<RefArg> {
        if let YYMINORTYPE::yy404(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy404_ref(&mut self) -> &mut Vec<RefArg> {
        if let YYMINORTYPE::yy404(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy415(self) -> Option<Distinctness> {
        if let YYMINORTYPE::yy415(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy415_ref(&mut self) -> &mut Option<Distinctness> {
        if let YYMINORTYPE::yy415(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy419(self) -> Option<Vec<Expr>> {
        if let YYMINORTYPE::yy419(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy419_ref(&mut self) -> &mut Option<Vec<Expr>> {
        if let YYMINORTYPE::yy419(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy432(self) -> RefArg {
        if let YYMINORTYPE::yy432(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy432_ref(&mut self) -> &mut RefArg {
        if let YYMINORTYPE::yy432(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy439(self) -> NamedTableConstraint {
        if let YYMINORTYPE::yy439(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy439_ref(&mut self) -> &mut NamedTableConstraint {
        if let YYMINORTYPE::yy439(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy444(self) -> (Name, Option<Type>) {
        if let YYMINORTYPE::yy444(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy444_ref(&mut self) -> &mut (Name, Option<Type>) {
        if let YYMINORTYPE::yy444(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy463(self) -> OneSelect {
        if let YYMINORTYPE::yy463(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy463_ref(&mut self) -> &mut OneSelect {
        if let YYMINORTYPE::yy463(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy468(self) -> Vec<Vec<Expr>> {
        if let YYMINORTYPE::yy468(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy468_ref(&mut self) -> &mut Vec<Vec<Expr>> {
        if let YYMINORTYPE::yy468(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy472(self) -> Vec<WindowDef> {
        if let YYMINORTYPE::yy472(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy472_ref(&mut self) -> &mut Vec<WindowDef> {
        if let YYMINORTYPE::yy472(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy473(self) -> Vec<(Expr, Expr)> {
        if let YYMINORTYPE::yy473(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy473_ref(&mut self) -> &mut Vec<(Expr, Expr)> {
        if let YYMINORTYPE::yy473(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy474(self) -> QualifiedName {
        if let YYMINORTYPE::yy474(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy474_ref(&mut self) -> &mut QualifiedName {
        if let YYMINORTYPE::yy474(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy479(self) -> Option<Expr> {
        if let YYMINORTYPE::yy479(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy479_ref(&mut self) -> &mut Option<Expr> {
        if let YYMINORTYPE::yy479(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy480(self) -> Expr {
        if let YYMINORTYPE::yy480(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy480_ref(&mut self) -> &mut Expr {
        if let YYMINORTYPE::yy480(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy485(self) -> Option<FromClause> {
        if let YYMINORTYPE::yy485(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy485_ref(&mut self) -> &mut Option<FromClause> {
        if let YYMINORTYPE::yy485(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy496(self) -> (Option<Expr>, Option<Vec<ResultColumn>>) {
        if let YYMINORTYPE::yy496(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy496_ref(&mut self) -> &mut (Option<Expr>, Option<Vec<ResultColumn>>) {
        if let YYMINORTYPE::yy496(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy503(self) -> SelectBody {
        if let YYMINORTYPE::yy503(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy503_ref(&mut self) -> &mut SelectBody {
        if let YYMINORTYPE::yy503(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy504(self) -> (Option<Upsert>, Option<Vec<ResultColumn>>) {
        if let YYMINORTYPE::yy504(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy504_ref(&mut self) -> &mut (Option<Upsert>, Option<Vec<ResultColumn>>) {
        if let YYMINORTYPE::yy504(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy517(self) -> Vec<Set> {
        if let YYMINORTYPE::yy517(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy517_ref(&mut self) -> &mut Vec<Set> {
        if let YYMINORTYPE::yy517(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy520(self) -> Materialized {
        if let YYMINORTYPE::yy520(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy520_ref(&mut self) -> &mut Materialized {
        if let YYMINORTYPE::yy520(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy536(self) -> Vec<SortedColumn> {
        if let YYMINORTYPE::yy536(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy536_ref(&mut self) -> &mut Vec<SortedColumn> {
        if let YYMINORTYPE::yy536(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy557(self) -> CommonTableExpr {
        if let YYMINORTYPE::yy557(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy557_ref(&mut self) -> &mut CommonTableExpr {
        if let YYMINORTYPE::yy557(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy559(self) -> Vec<NamedTableConstraint> {
        if let YYMINORTYPE::yy559(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy559_ref(&mut self) -> &mut Vec<NamedTableConstraint> {
        if let YYMINORTYPE::yy559(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy569(self) -> TriggerCmd {
        if let YYMINORTYPE::yy569(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy569_ref(&mut self) -> &mut TriggerCmd {
        if let YYMINORTYPE::yy569(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy577(self) -> JoinOperator {
        if let YYMINORTYPE::yy577(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy577_ref(&mut self) -> &mut JoinOperator {
        if let YYMINORTYPE::yy577(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    fn yy578(self) -> Option<JoinConstraint> {
        if let YYMINORTYPE::yy578(v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
    #[allow(dead_code)]
    fn yy578_ref(&mut self) -> &mut Option<JoinConstraint> {
        if let YYMINORTYPE::yy578(ref mut v) = self.minor {
            v
        } else {
            unreachable!()
        }
    }
}
const YYSTACKDEPTH: usize = 128;
const YYERRORSYMBOL: YYCODETYPE = 0; // No error symbol
const YYFALLBACK: bool = true;
const YYNSTATE: YYACTIONTYPE =             563;
const YYNRULE: usize =             406;
const YYNRULE_WITH_ACTION: YYACTIONTYPE =  377;
const YYNTOKEN: YYACTIONTYPE =             168;
const YY_MAX_SHIFT: YYACTIONTYPE =         562;
const YY_MIN_SHIFTREDUCE: YYACTIONTYPE =   829;
const YY_MAX_SHIFTREDUCE: YYACTIONTYPE =  1234;
const YY_ERROR_ACTION: YYACTIONTYPE =      1235;
const YY_ACCEPT_ACTION: YYACTIONTYPE =     1236;
//const YY_NO_ACTION: YYACTIONTYPE =         1237;
const YY_MIN_REDUCE: YYACTIONTYPE =       1238;
//const YY_MAX_REDUCE: YYACTIONTYPE =        1643;
/************* End control #defines *******************************************/

/* Next are the tables used to determine what action to take based on the
** current state and lookahead token.  These tables are used to implement
** functions that take a state number and lookahead value and return an
** action integer.
**
** Suppose the action integer is N.  Then the action is determined as
** follows
**
**   0 <= N <= YY_MAX_SHIFT             Shift N.  That is, push the lookahead
**                                      token onto the stack and goto state N.
**
**   N between YY_MIN_SHIFTREDUCE       Shift to an arbitrary state then
**     and YY_MAX_SHIFTREDUCE           reduce by rule N-YY_MIN_SHIFTREDUCE.
**
**   N == YY_ERROR_ACTION               A syntax error has occurred.
**
**   N == YY_ACCEPT_ACTION              The parser accepts its input.
**
**   N == YY_NO_ACTION                  No such action.  Denotes unused
**                                      slots in the yy_action[] table.
**
**   N between YY_MIN_REDUCE            Reduce by rule N-YY_MIN_REDUCE
**     and YY_MAX_REDUCE
**
** The action table is constructed as a single large table named yy_action[].
** Given state S and lookahead X, the action is computed as either:
**
**    (A)   N = yy_action[ yy_shift_ofst[S] + X ]
**    (B)   N = yy_default[S]
**
** The (A) formula is preferred.  The B formula is used instead if
** yy_lookahead[yy_shift_ofst[S]+X] is not equal to X.
**
** The formulas above are for computing the action when the lookahead is
** a terminal symbol.  If the lookahead is a non-terminal (as occurs after
** a reduce action) then the yy_reduce_ofst[] array is used in place of
** the yy_shift_ofst[] array.
**
** The following are the tables generated in this section:
**
**  yy_action[]        A single table containing all actions.
**  yy_lookahead[]     A table containing the lookahead for each entry in
**                     yy_action.  Used to detect hash collisions.
**  yy_shift_ofst[]    For each state, the offset into yy_action for
**                     shifting terminals.
**  yy_reduce_ofst[]   For each state, the offset into yy_action for
**                     shifting non-terminals after a reduce.
**  yy_default[]       Default action for each state.
**
*********** Begin parsing tables **********************************************/
macro_rules! YY_ACTTAB_COUNT {() => {2145}}
#[rustfmt::skip]
#[allow(non_upper_case_globals)]
static yy_action: [YYACTIONTYPE; 2145] = [
 /*     0 */  1516, 1331, 1331,  119,  116,  176, 1281,  556,  119,  116,
 /*    10 */   176, 1236,    1,    1,  562,    3, 1240,  442,  441, 1344,
 /*    20 */   397,  217,  556, 1551,  561,  262, 1240,   42,   42, 1339,
 /*    30 */   544,  217,  513,  262, 1544,  373, 1284, 1544,  374, 1339,
 /*    40 */  1329, 1329,   73,   73,  126,  127,   81, 1051, 1051, 1065,
 /*    50 */  1068, 1055, 1055,  124,  124,  125,  125,  125,  125, 1283,
 /*    60 */   953,  214, 1343,  220,  214, 1343,  214, 1343,  953,  214,
 /*    70 */  1343,  214, 1343,  544,  861,  391,  544,  518,  544,  322,
 /*    80 */  1358,  544,  365,  544,  222,  514,  442,  246,  119,  116,
 /*    90 */   176,  119,  116,  176, 1278,  469,  286,  246,  469,  119,
 /*   100 */   116,  176, 1292,  123,  123,  123,  123,  122,  122,  121,
 /*   110 */   121,  121,  120,  117,  432,    6,  461,  436,  953,  478,
 /*   120 */  1595,  372,  556,  220,  397,  556,  953,  436,  214, 1343,
 /*   130 */   143,  861,  862,  861,  252,  437,  984,  492,  489,  488,
 /*   140 */   544,  432,   42,   42,  985,   42,   42,  487,  126,  127,
 /*   150 */    81, 1051, 1051, 1065, 1068, 1055, 1055,  124,  124,  125,
 /*   160 */   125,  125,  125, 1114, 1507,  397,  123,  123,  123,  123,
 /*   170 */   122,  122,  121,  121,  121,  120,  117,  432,  369,  122,
 /*   180 */   122,  121,  121,  121,  120,  117,  432,  316,  537,  126,
 /*   190 */   127,   81, 1051, 1051, 1065, 1068, 1055, 1055,  124,  124,
 /*   200 */   125,  125,  125,  125,  547,  547,  547,  123,  123,  123,
 /*   210 */   123,  122,  122,  121,  121,  121,  120,  117,  432,  471,
 /*   220 */   556, 1327, 1021, 1301,  444, 1021,  837,  838,  839,  840,
 /*   230 */   143, 1325, 1192,  143, 1190, 1597,  397,  371, 1595,  531,
 /*   240 */    73,   73,   98, 1052, 1052, 1066, 1069,  552,  123,  123,
 /*   250 */   123,  123,  122,  122,  121,  121,  121,  120,  117,  432,
 /*   260 */   126,  127,   81, 1051, 1051, 1065, 1068, 1055, 1055,  124,
 /*   270 */   124,  125,  125,  125,  125,  518,  344,  462,  343,  344,
 /*   280 */   462,  343, 1579,  512,  235,  502,  128,  316,  537,  288,
 /*   290 */   316,  537,  121,  121,  121,  120,  117,  432,  984, 1516,
 /*   300 */   252,  430,  429,  492,  489,  488,  985,  397,  125,  125,
 /*   310 */   125,  125,  118,  487,  473,  272, 1512, 1514, 1056,  123,
 /*   320 */   123,  123,  123,  122,  122,  121,  121,  121,  120,  117,
 /*   330 */   432,  126,  127,   81, 1051, 1051, 1065, 1068, 1055, 1055,
 /*   340 */   124,  124,  125,  125,  125,  125,  397,  382,  861, 1112,
 /*   350 */  1112,   84,  285,  553, 1580,   83,  123,  123,  123,  123,
 /*   360 */   122,  122,  121,  121,  121,  120,  117,  432,  556,  861,
 /*   370 */   126,  127,   81, 1051, 1051, 1065, 1068, 1055, 1055,  124,
 /*   380 */   124,  125,  125,  125,  125, 1512,  318,  175,   15,   15,
 /*   390 */   123,  123,  123,  123,  122,  122,  121,  121,  121,  120,
 /*   400 */   117,  432, 1613,  408,  471,  861,  862,  861,  347, 1184,
 /*   410 */   349,  125,  125,  125,  125,  438,  319,  397,  421,  143,
 /*   420 */   527,  104, 1183,  355,  861, 1185,  861,  862,  861,  123,
 /*   430 */   123,  123,  123,  122,  122,  121,  121,  121,  120,  117,
 /*   440 */   432,  126,  127,   81, 1051, 1051, 1065, 1068, 1055, 1055,
 /*   450 */   124,  124,  125,  125,  125,  125,  861, 1318,  443,  123,
 /*   460 */   123,  123,  123,  122,  122,  121,  121,  121,  120,  117,
 /*   470 */   432,  556, 1184, 1184,  293,  946,  316,  537,  526,  861,
 /*   480 */   945,  861,  862,  861,  549, 1183, 1183,  369, 1185, 1185,
 /*   490 */   130,   73,   73,  554,  495,  503, 1587,  555,  263,    8,
 /*   500 */   123,  123,  123,  123,  122,  122,  121,  121,  121,  120,
 /*   510 */   117,  432,  418,  861,  862,  861,   86,  233,  270,  230,
 /*   520 */   363,  498,  358,  497,  257,  397,  518,  876,  951,  351,
 /*   530 */   354,  153,  517,   12,  519,  262,  861,  862,  861,  431,
 /*   540 */   431,  431,  525,  120,  117,  432,  297,  523,  225,  126,
 /*   550 */   127,   81, 1051, 1051, 1065, 1068, 1055, 1055,  124,  124,
 /*   560 */   125,  125,  125,  125,  214, 1343,  556,  209, 1041,  397,
 /*   570 */   953, 1041, 1305,  493,  232,  231,  544,  554,  214, 1343,
 /*   580 */   259,  174,  370, 1021,  876, 1032,   71,   71, 1032, 1030,
 /*   590 */   544, 1578, 1030,  126,  127,   81, 1051, 1051, 1065, 1068,
 /*   600 */  1055, 1055,  124,  124,  125,  125,  125,  125,  123,  123,
 /*   610 */   123,  123,  122,  122,  121,  121,  121,  120,  117,  432,
 /*   620 */   237, 1031, 1033, 1033, 1031, 1033, 1033,  556,  953,  861,
 /*   630 */   556,  175,  515,  220,  938,  939,  861,  344,  449,  327,
 /*   640 */   397,  102,  145,  535,  881,  392,  375,   73,   73,  147,
 /*   650 */    73,   73,  123,  123,  123,  123,  122,  122,  121,  121,
 /*   660 */   121,  120,  117,  432,  126,  127,   81, 1051, 1051, 1065,
 /*   670 */  1068, 1055, 1055,  124,  124,  125,  125,  125,  125,  397,
 /*   680 */   316,  537,  518,  873,  220,  311,  861,  862,  861,  849,
 /*   690 */   504,  381,  554,  861,  862,  861,  393,  398,  556, 1588,
 /*   700 */   200,  556,    8,  126,  127,   81, 1051, 1051, 1065, 1068,
 /*   710 */  1055, 1055,  124,  124,  125,  125,  125,  125,   73,   73,
 /*   720 */   471,   15,   15,  123,  123,  123,  123,  122,  122,  121,
 /*   730 */   121,  121,  120,  117,  432, 1434,  411,  861,  556,  550,
 /*   740 */  1587, 1562,  471,    8,  861,  207, 1343,  861,  452,  446,
 /*   750 */   397,  388, 1233,  312, 1205,    7,   80,  544,  134,  134,
 /*   760 */  1561,    2,  123,  123,  123,  123,  122,  122,  121,  121,
 /*   770 */   121,  120,  117,  432,  126,  127,   81, 1051, 1051, 1065,
 /*   780 */  1068, 1055, 1055,  124,  124,  125,  125,  125,  125,  397,
 /*   790 */   296, 1089,  861, 1042,  861,  862,  861,  439,  861,  271,
 /*   800 */   457,  861,  862,  861,  861,  862,  861,  379,  201,  222,
 /*   810 */  1263,   33,  298,  126,  127,   81, 1051, 1051, 1065, 1068,
 /*   820 */  1055, 1055,  124,  124,  125,  125,  125,  125,  972, 1499,
 /*   830 */  1234,  971,  556,  123,  123,  123,  123,  122,  122,  121,
 /*   840 */   121,  121,  120,  117,  432,  861,  215, 1343,  861,  861,
 /*   850 */   862,  861,   15,   15, 1434,  861,  862,  861,  544,  271,
 /*   860 */   397, 1120, 1119,  458, 1026,  369,  554,  417,  558,  552,
 /*   870 */   558, 1558,  123,  123,  123,  123,  122,  122,  121,  121,
 /*   880 */   121,  120,  117,  432,  126,  127,   81, 1051, 1051, 1065,
 /*   890 */  1068, 1055, 1055,  124,  124,  125,  125,  125,  125,  397,
 /*   900 */   388, 1160,  861,  862,  861,  861,  862,  861, 1304,  156,
 /*   910 */   255,  254,  253,  110,  281,  554,  969, 1640,  389,  413,
 /*   920 */  1556,  556, 1559,  126,  127,   81, 1051, 1051, 1065, 1068,
 /*   930 */  1055, 1055,  124,  124,  125,  125,  125,  125,  556,  467,
 /*   940 */   556,   15,   15,  123,  123,  123,  123,  122,  122,  121,
 /*   950 */   121,  121,  120,  117,  432, 1184,  300,  516,   15,   15,
 /*   960 */    15,   15,  407,  407, 1616,  562,    3, 1240, 1183, 1578,
 /*   970 */   397, 1185,  217,  211,  227,  212, 1581,   14,   14, 1234,
 /*   980 */  1339,  149,  123,  123,  123,  123,  122,  122,  121,  121,
 /*   990 */   121,  120,  117,  432,  126,  127,   81, 1051, 1051, 1065,
 /*  1000 */  1068, 1055, 1055,  124,  124,  125,  125,  125,  125,  238,
 /*  1010 */   362,  556,  214, 1343,  397,  556, 1585,  554,  868,    8,
 /*  1020 */   361,  407,  406,  554,  544,  861,  324,  289, 1550, 1377,
 /*  1030 */   333,   73,   73,  535,  970,   73,   73,  243,  246,  105,
 /*  1040 */    81, 1051, 1051, 1065, 1068, 1055, 1055,  124,  124,  125,
 /*  1050 */   125,  125,  125,  123,  123,  123,  123,  122,  122,  121,
 /*  1060 */   121,  121,  120,  117,  432, 1138,  427,  556,  436,  556,
 /*  1070 */   428, 1376,  430,  429, 1161, 1638,  868, 1638,  477,  505,
 /*  1080 */  1139, 1108,  861,  862,  861,  291, 1110,   73,   73,   52,
 /*  1090 */    52,  330,  867,  332, 1109, 1140,  899,  123,  123,  123,
 /*  1100 */   123,  122,  122,  121,  121,  121,  120,  117,  432,  556,
 /*  1110 */   108,  214, 1343,  220, 1161, 1639,  923, 1639,  556, 1138,
 /*  1120 */  1112, 1112,  313,  544,  506,  396,  924,  496,  369,   15,
 /*  1130 */    15,  397,  554,  448, 1139,  900,  221, 1506,  136,  136,
 /*  1140 */   477, 1269,  403,  554,  534,  301,  554, 1319, 1338, 1140,
 /*  1150 */   867, 1334,  397, 1159,  245,  126,  127,   81, 1051, 1051,
 /*  1160 */  1065, 1068, 1055, 1055,  124,  124,  125,  125,  125,  125,
 /*  1170 */   540,  292,  152,  397,  556,  331,  126,  127,   81, 1051,
 /*  1180 */  1051, 1065, 1068, 1055, 1055,  124,  124,  125,  125,  125,
 /*  1190 */   125,  419,  556, 1159,   56,   56, 1566,  126,  115,   81,
 /*  1200 */  1051, 1051, 1065, 1068, 1055, 1055,  124,  124,  125,  125,
 /*  1210 */   125,  125,   57,   57,  123,  123,  123,  123,  122,  122,
 /*  1220 */   121,  121,  121,  120,  117,  432,  554,  484,  380,  554,
 /*  1230 */   222,  451, 1578,  341,  454,  123,  123,  123,  123,  122,
 /*  1240 */   122,  121,  121,  121,  120,  117,  432, 1337,  453, 1261,
 /*  1250 */   533,  507,  556,  536,  256,  477,  123,  123,  123,  123,
 /*  1260 */   122,  122,  121,  121,  121,  120,  117,  432,  556,  532,
 /*  1270 */   284, 1586,   17,   17,    8, 1434, 1434,  397, 1198,  214,
 /*  1280 */  1343,  214, 1343,  214, 1343,  214, 1343, 1584,   44,   44,
 /*  1290 */     8,  544, 1449,  544,  554,  544,  556,  544,  556,  346,
 /*  1300 */   556,  367,  127,   81, 1051, 1051, 1065, 1068, 1055, 1055,
 /*  1310 */   124,  124,  125,  125,  125,  125,   58,   58,   45,   45,
 /*  1320 */    59,   59,  556,  402,  556, 1539,  556,  364, 1610,  109,
 /*  1330 */  1197,  107,  113,  545,  556,    4,  556,  416,  556,  354,
 /*  1340 */   414,  415,   60,   60,   61,   61,   62,   62,  299,  548,
 /*  1350 */   101, 1434,  367,  556,   63,   63,   46,   46,   47,   47,
 /*  1360 */   123,  123,  123,  123,  122,  122,  121,  121,  121,  120,
 /*  1370 */   117,  432,  433,   48,   48,  404,  556, 1538,  556,  522,
 /*  1380 */   556,  554,  556, 1366,  541,  556,  279,  556,  424, 1270,
 /*  1390 */   556,  969,  113,  545,  143,    4,   50,   50,   51,   51,
 /*  1400 */    64,   64,  132,  132,  556,  133,  133,   65,   65,  548,
 /*  1410 */    66,   66, 1317, 1041,  911,  556,  425,  556,  500,  111,
 /*  1420 */   111,   10,  556, 1303,   67,   67,  556,  112,  556,  433,
 /*  1430 */   557,  433,  433,  447, 1030,   16,   16,   68,   68,  556,
 /*  1440 */    80,  556,   53,   53,  541,  946,   69,   69,   70,   70,
 /*  1450 */   945,  316,  537,  242,  556,  455,  556,  464,  529,   54,
 /*  1460 */    54,   72,   72,  528,   80,  556, 1031, 1033, 1033, 1034,
 /*  1470 */    28,  350,  556, 1041,  161,  161,  162,  162, 1116,  111,
 /*  1480 */   111,  556, 1115,  556,  238,   77,   77,  112, 1448,  433,
 /*  1490 */   557,  433,   55,   55, 1030,  556, 1321,  556, 1228,  556,
 /*  1500 */   554,  135,  135,   74,   74,  348,  520,  369, 1583,  970,
 /*  1510 */   233,    8,  546,  403,  456,  159,  159,  137,  137,  131,
 /*  1520 */   131,  316,  537,  364, 1610,  104, 1031, 1033, 1033, 1034,
 /*  1530 */    28, 1615, 1209,  435,  110,  554,  277,  556,  475,  556,
 /*  1540 */   317, 1220,  386,  386,  385,  274,  383,  405,  178, 1214,
 /*  1550 */   556,  154,  113,  545,  468,    4,  256,  160,  160,  155,
 /*  1560 */   155,   80,  184,  509,  321,  113,  545,  556,    4,  548,
 /*  1570 */   141,  141,  320,  556,  151,  556,   39,  556,  315,  556,
 /*  1580 */   283,  174,  548,   87,  224,   80,  556,  140,  140, 1156,
 /*  1590 */   884,  390,  433,  138,  138,  139,  139,   76,   76,   78,
 /*  1600 */    78,  556,  186,  521,  541,  433,   75,   75,  556,  463,
 /*  1610 */   167, 1578,   31,  142,  335,  465,  104,  541,  529, 1302,
 /*  1620 */    19,   43,   43,  530,  539,  402,  890,  228,   49,   49,
 /*  1630 */   185,  529,  339, 1041, 1096,  476,  528,  357,  294,  111,
 /*  1640 */   111, 1166,  340,   32,  104,  368, 1041,  112,  884,  433,
 /*  1650 */   557,  433,  111,  111, 1030, 1023,  103,  261,  228,  409,
 /*  1660 */   112, 1035,  433,  557,  433,  399, 1387, 1030,  898,  897,
 /*  1670 */   316,  537,  470, 1433,  261,  472,  485,  261,  258,  352,
 /*  1680 */  1361,  104,  905,  906,  987,  988, 1031, 1033, 1033, 1034,
 /*  1690 */    28,  310, 1096, 1092,  440,  258,  975, 1603,  261, 1031,
 /*  1700 */  1033, 1033, 1034,   28, 1209,  435,  480,  943,  277,  110,
 /*  1710 */   113,  545,  538,    4,  386,  386,  385,  274,  383, 1035,
 /*  1720 */   944, 1214,  110, 1113, 1113, 1111, 1111,  548,  865, 1373,
 /*  1730 */   148, 1438, 1280, 1265,  184, 1264,  321,  113,  545, 1253,
 /*  1740 */     4,  278, 1252, 1254,  320, 1244,  387,  306,   13,  307,
 /*  1750 */   433, 1419,  308,  241,  548,  326,  329,  445,  290,  287,
 /*  1760 */   342,  337,  541,  474,  338,  295, 1424,  234, 1400, 1301,
 /*  1770 */   360, 1423, 1542,  490,  186, 1503, 1502,  433, 1370, 1371,
 /*  1780 */  1369,  309,  167,  218, 1368,  142,  542,  378, 1255,  541,
 /*  1790 */  1228, 1041, 1225,   86,  226,  198,  219,  111,  111,  239,
 /*  1800 */   182,  325,  185,  229, 1414,  112,   82,  433,  557,  433,
 /*  1810 */    79,  545, 1030,    4,  129, 1508,  535,  172, 1041,   85,
 /*  1820 */  1407,  188,  450,  328,  111,  111,  950,  548, 1420,   36,
 /*  1830 */   190,  511,  112,  191,  433,  557,  433,  399,  192, 1030,
 /*  1840 */   193,  483,  316,  537, 1031, 1033, 1033, 1034,   28,  102,
 /*  1850 */   433,  194, 1497,  195, 1543, 1426,  466,   37, 1425, 1541,
 /*  1860 */   202, 1429,  541,  460,   88,  479,  440,  204,  345,  208,
 /*  1870 */   499, 1031, 1033, 1033, 1034,   28,  420,  481,  205,  394,
 /*  1880 */  1324, 1312, 1323,  113,  545,   94,    4, 1322,  890, 1295,
 /*  1890 */  1294, 1041,  395, 1289, 1288,  359,  235,  111,  111, 1311,
 /*  1900 */   548, 1609, 1341,  422, 1287,  112, 1286,  433,  557,  433,
 /*  1910 */   423,  508, 1030,  304,  305,  366,   11, 1482,  264,  426,
 /*  1920 */  1591, 1392,  106,  433,  314, 1590,  524,  150,  377, 1350,
 /*  1930 */  1391,  510, 1342, 1340,   35,  541,  559,  376,  216, 1212,
 /*  1940 */   276,  273,  275,  560, 1031, 1033, 1033, 1034,   28, 1250,
 /*  1950 */  1241,  146,  302,  830,  434,  163,  400,  180,  164,  165,
 /*  1960 */   401,  223,  181,  240, 1041,  166, 1106,  183, 1104,  179,
 /*  1970 */   111,  111,  323,  187,  168,  926,  189,  244,  112, 1220,
 /*  1980 */   433,  557,  433,  247,  334, 1030,  277,   18,  248,  336,
 /*  1990 */  1117,  261,  386,  386,  385,  274,  383,  196, 1124, 1214,
 /*  2000 */   459, 1128,  197,  199,  169,  170,  412,  410,  171,   89,
 /*  2010 */    90,   91,  184,   92,  321, 1131, 1127, 1031, 1033, 1033,
 /*  2020 */  1034,   28,  320,  249,  250,    5,  203,   38,  848,  482,
 /*  2030 */   251,  361,  353,  206,  486,   93,   20,   21,  491,  888,
 /*  2040 */   494,  356,   95,  303,  173,  157,  901,   96,  501,   97,
 /*  2050 */  1203,  158,  186, 1071, 1168,   40,   99,  100, 1167, 1087,
 /*  2060 */   167, 1072, 1070,  142,  280,  937,  236,  110,  282,  260,
 /*  2070 */   210,  973, 1187, 1191,   22, 1196, 1189,   23,  979, 1195,
 /*  2080 */   185,   24,  104,   25,   26, 1074,   27, 1075,    9, 1137,
 /*  2090 */   265, 1136,  266,   29,  543,   41,  551, 1036,  866,  114,
 /*  2100 */    34,   30, 1173,  384,  267,  213,  177,  144, 1211, 1210,
 /*  2110 */  1237,  268, 1237,  269, 1271,  399, 1237, 1237, 1237, 1237,
 /*  2120 */   316,  537, 1237, 1237, 1237, 1237, 1237, 1237, 1237, 1237,
 /*  2130 */  1237, 1237, 1237, 1237, 1237, 1237, 1237, 1237, 1237, 1237,
 /*  2140 */  1237, 1237, 1237, 1237,  440,
];
#[rustfmt::skip]
#[allow(non_upper_case_globals)]
static yy_lookahead: [YYCODETYPE; 2313] = [
 /*     0 */   176,  215,  216,  252,  253,  254,  196,  176,  252,  253,
 /*    10 */   254,  168,  169,  170,  171,  172,  173,  193,  194,  219,
 /*    20 */    20,  178,  176,  272,  171,   25,  173,  196,  197,  186,
 /*    30 */   230,  178,  186,   25,  186,  199,  196,  186,  199,  186,
 /*    40 */   215,  216,  196,  197,   44,   45,   46,   47,   48,   49,
 /*    50 */    50,   51,   52,   53,   54,   55,   56,   57,   58,  196,
 /*    60 */    60,  218,  219,  176,  218,  219,  218,  219,   60,  218,
 /*    70 */   219,  218,  219,  230,   60,  188,  230,  231,  230,  176,
 /*    80 */   233,  230,  221,  230,  223,  239,  262,  244,  252,  253,
 /*    90 */   254,  252,  253,  254,  195,  247,  186,  244,  247,  252,
 /*   100 */   253,  254,  203,  103,  104,  105,  106,  107,  108,  109,
 /*   110 */   110,  111,  112,  113,  114,  267,  268,  274,  118,  268,
 /*   120 */   289,  290,  176,  176,   20,  176,  118,  274,  218,  219,
 /*   130 */    82,  117,  118,  119,  120,  188,   32,  123,  124,  125,
 /*   140 */   230,  114,  196,  197,   40,  196,  197,  133,   44,   45,
 /*   150 */    46,   47,   48,   49,   50,   51,   52,   53,   54,   55,
 /*   160 */    56,   57,   58,   12,  261,   20,  103,  104,  105,  106,
 /*   170 */   107,  108,  109,  110,  111,  112,  113,  114,  176,  107,
 /*   180 */   108,  109,  110,  111,  112,  113,  114,  139,  140,   44,
 /*   190 */    45,   46,   47,   48,   49,   50,   51,   52,   53,   54,
 /*   200 */    55,   56,   57,   58,  192,  193,  194,  103,  104,  105,
 /*   210 */   106,  107,  108,  109,  110,  111,  112,  113,  114,  176,
 /*   220 */   176,  203,   74,  205,  247,   74,    7,    8,    9,   10,
 /*   230 */    82,  213,   87,   82,   89,  289,   20,  291,  289,  290,
 /*   240 */   196,  197,   26,   47,   48,   49,   50,  176,  103,  104,
 /*   250 */   105,  106,  107,  108,  109,  110,  111,  112,  113,  114,
 /*   260 */    44,   45,   46,   47,   48,   49,   50,   51,   52,   53,
 /*   270 */    54,   55,   56,   57,   58,  231,  128,  129,  130,  128,
 /*   280 */   129,  130,  280,  239,  166,  167,   70,  139,  140,  246,
 /*   290 */   139,  140,  109,  110,  111,  112,  113,  114,   32,  176,
 /*   300 */   120,  107,  108,  123,  124,  125,   40,   20,   55,   56,
 /*   310 */    57,   58,   59,  133,  176,   27,  193,  194,  122,  103,
 /*   320 */   104,  105,  106,  107,  108,  109,  110,  111,  112,  113,
 /*   330 */   114,   44,   45,   46,   47,   48,   49,   50,   51,   52,
 /*   340 */    53,   54,   55,   56,   57,   58,   20,   17,   60,  155,
 /*   350 */   156,   25,  100,  282,  283,   68,  103,  104,  105,  106,
 /*   360 */   107,  108,  109,  110,  111,  112,  113,  114,  176,   60,
 /*   370 */    44,   45,   46,   47,   48,   49,   50,   51,   52,   53,
 /*   380 */    54,   55,   56,   57,   58,  262,  134,  176,  196,  197,
 /*   390 */   103,  104,  105,  106,  107,  108,  109,  110,  111,  112,
 /*   400 */   113,  114,  176,  211,  176,  117,  118,  119,   78,   77,
 /*   410 */    80,   55,   56,   57,   58,  163,  164,   20,   20,   82,
 /*   420 */    88,   26,   90,   25,   60,   93,  117,  118,  119,  103,
 /*   430 */   104,  105,  106,  107,  108,  109,  110,  111,  112,  113,
 /*   440 */   114,   44,   45,   46,   47,   48,   49,   50,   51,   52,
 /*   450 */    53,   54,   55,   56,   57,   58,   60,  210,  121,  103,
 /*   460 */   104,  105,  106,  107,  108,  109,  110,  111,  112,  113,
 /*   470 */   114,  176,   77,   77,  246,  136,  139,  140,  146,   60,
 /*   480 */   141,  117,  118,  119,   88,   90,   90,  176,   93,   93,
 /*   490 */    23,  196,  197,  176,   96,  284,  285,  176,  181,  288,
 /*   500 */   103,  104,  105,  106,  107,  108,  109,  110,  111,  112,
 /*   510 */   113,  114,  114,  117,  118,  119,  152,   26,  120,  121,
 /*   520 */   122,  123,  124,  125,  126,   20,  231,   60,  109,   24,
 /*   530 */   132,  220,  186,   23,  239,   25,  117,  118,  119,  192,
 /*   540 */   193,  194,  146,  112,  113,  114,  186,  176,  151,   44,
 /*   550 */    45,   46,   47,   48,   49,   50,   51,   52,   53,   54,
 /*   560 */    55,   56,   57,   58,  218,  219,  176,   26,  101,   20,
 /*   570 */    60,  101,  206,   24,  107,  108,  230,  176,  218,  219,
 /*   580 */   234,  235,  181,   74,  117,  118,  196,  197,  118,  122,
 /*   590 */   230,  280,  122,   44,   45,   46,   47,   48,   49,   50,
 /*   600 */    51,   52,   53,   54,   55,   56,   57,   58,  103,  104,
 /*   610 */   105,  106,  107,  108,  109,  110,  111,  112,  113,  114,
 /*   620 */   176,  154,  155,  156,  154,  155,  156,  176,  118,   60,
 /*   630 */   176,  176,  135,  176,  137,  138,   60,  128,  129,  130,
 /*   640 */    20,  150,   73,  146,   24,  188,  256,  196,  197,   73,
 /*   650 */   196,  197,  103,  104,  105,  106,  107,  108,  109,  110,
 /*   660 */   111,  112,  113,  114,   44,   45,   46,   47,   48,   49,
 /*   670 */    50,   51,   52,   53,   54,   55,   56,   57,   58,   20,
 /*   680 */   139,  140,  231,   24,  176,  231,  117,  118,  119,   22,
 /*   690 */   239,  183,  176,  117,  118,  119,  188,  181,  176,  285,
 /*   700 */    23,  176,  288,   44,   45,   46,   47,   48,   49,   50,
 /*   710 */    51,   52,   53,   54,   55,   56,   57,   58,  196,  197,
 /*   720 */   176,  196,  197,  103,  104,  105,  106,  107,  108,  109,
 /*   730 */   110,  111,  112,  113,  114,  176,  211,   60,  176,  284,
 /*   740 */   285,  176,  176,  288,   60,  218,  219,   60,   81,  225,
 /*   750 */    20,   23,   24,  231,   24,   23,  232,  230,  196,  197,
 /*   760 */   176,   23,  103,  104,  105,  106,  107,  108,  109,  110,
 /*   770 */   111,  112,  113,  114,   44,   45,   46,   47,   48,   49,
 /*   780 */    50,   51,   52,   53,   54,   55,   56,   57,   58,   20,
 /*   790 */   246,  124,   60,   24,  117,  118,  119,  176,   60,  176,
 /*   800 */   241,  117,  118,  119,  117,  118,  119,  221,   23,  223,
 /*   810 */   187,   23,  246,   44,   45,   46,   47,   48,   49,   50,
 /*   820 */    51,   52,   53,   54,   55,   56,   57,   58,  144,  162,
 /*   830 */   102,  144,  176,  103,  104,  105,  106,  107,  108,  109,
 /*   840 */   110,  111,  112,  113,  114,   60,  218,  219,   60,  117,
 /*   850 */   118,  119,  196,  197,  176,  117,  118,  119,  230,  176,
 /*   860 */    20,  128,  129,  130,   24,  176,  176,  211,  185,  176,
 /*   870 */   187,  181,  103,  104,  105,  106,  107,  108,  109,  110,
 /*   880 */   111,  112,  113,  114,   44,   45,   46,   47,   48,   49,
 /*   890 */    50,   51,   52,   53,   54,   55,   56,   57,   58,   20,
 /*   900 */    23,   24,  117,  118,  119,  117,  118,  119,  206,  220,
 /*   910 */   128,  129,  130,   26,   24,  176,   26,  277,  278,  241,
 /*   920 */   181,  176,  176,   44,   45,   46,   47,   48,   49,   50,
 /*   930 */    51,   52,   53,   54,   55,   56,   57,   58,  176,  116,
 /*   940 */   176,  196,  197,  103,  104,  105,  106,  107,  108,  109,
 /*   950 */   110,  111,  112,  113,  114,   77,  211,  176,  196,  197,
 /*   960 */   196,  197,  176,  176,  170,  171,  172,  173,   90,  280,
 /*   970 */    20,   93,  178,  211,  151,  211,  283,  196,  197,  102,
 /*   980 */   186,   23,  103,  104,  105,  106,  107,  108,  109,  110,
 /*   990 */   111,  112,  113,  114,   44,   45,   46,   47,   48,   49,
 /*  1000 */    50,   51,   52,   53,   54,   55,   56,   57,   58,  119,
 /*  1010 */   122,  176,  218,  219,   20,  176,  285,  176,   60,  288,
 /*  1020 */   132,  176,  181,  176,  230,   60,  240,  240,  181,  176,
 /*  1030 */    17,  196,  197,  146,  144,  196,  197,   25,  244,  160,
 /*  1040 */    46,   47,   48,   49,   50,   51,   52,   53,   54,   55,
 /*  1050 */    56,   57,   58,  103,  104,  105,  106,  107,  108,  109,
 /*  1060 */   110,  111,  112,  113,  114,   13,  231,  176,  274,  176,
 /*  1070 */   231,  176,  107,  108,   23,   24,  118,   26,  176,  186,
 /*  1080 */    28,  116,  117,  118,  119,  240,  121,  196,  197,  196,
 /*  1090 */   197,   78,   60,   80,  129,   43,   36,  103,  104,  105,
 /*  1100 */   106,  107,  108,  109,  110,  111,  112,  113,  114,  176,
 /*  1110 */   160,  218,  219,  176,   23,   24,   64,   26,  176,   13,
 /*  1120 */   155,  156,  231,  230,  231,  188,   74,   67,  176,  196,
 /*  1130 */   197,   20,  176,  176,   28,   75,   25,  181,  196,  197,
 /*  1140 */   176,  190,  191,  176,  211,  208,  176,  210,  181,   43,
 /*  1150 */   118,  181,   20,  102,  142,   44,   45,   46,   47,   48,
 /*  1160 */    49,   50,   51,   52,   53,   54,   55,   56,   57,   58,
 /*  1170 */    64,  269,  220,   20,  176,  162,   44,   45,   46,   47,
 /*  1180 */    48,   49,   50,   51,   52,   53,   54,   55,   56,   57,
 /*  1190 */    58,  131,  176,  102,  196,  197,  176,   44,   45,   46,
 /*  1200 */    47,   48,   49,   50,   51,   52,   53,   54,   55,   56,
 /*  1210 */    57,   58,  196,  197,  103,  104,  105,  106,  107,  108,
 /*  1220 */   109,  110,  111,  112,  113,  114,  176,   20,  221,  176,
 /*  1230 */   223,  181,  280,  269,  181,  103,  104,  105,  106,  107,
 /*  1240 */   108,  109,  110,  111,  112,  113,  114,  186,  176,  186,
 /*  1250 */    67,  186,  176,  186,   47,  176,  103,  104,  105,  106,
 /*  1260 */   107,  108,  109,  110,  111,  112,  113,  114,  176,   86,
 /*  1270 */    23,  285,  196,  197,  288,  176,  176,   20,   95,  218,
 /*  1280 */   219,  218,  219,  218,  219,  218,  219,  285,  196,  197,
 /*  1290 */   288,  230,  251,  230,  176,  230,  176,  230,  176,  181,
 /*  1300 */   176,  176,   45,   46,   47,   48,   49,   50,   51,   52,
 /*  1310 */    53,   54,   55,   56,   57,   58,  196,  197,  196,  197,
 /*  1320 */   196,  197,  176,  116,  176,  176,  176,  286,  287,  159,
 /*  1330 */   147,  161,   20,   21,  176,   23,  176,  212,  176,  132,
 /*  1340 */   241,  241,  196,  197,  196,  197,  196,  197,  269,   37,
 /*  1350 */   116,  176,  176,  176,  196,  197,  196,  197,  196,  197,
 /*  1360 */   103,  104,  105,  106,  107,  108,  109,  110,  111,  112,
 /*  1370 */   113,  114,   60,  196,  197,  179,  176,  176,  176,  145,
 /*  1380 */   176,  176,  176,  236,   72,  176,  181,  176,  212,  142,
 /*  1390 */   176,   26,   20,   21,   82,   23,  196,  197,  196,  197,
 /*  1400 */   196,  197,  196,  197,  176,  196,  197,  196,  197,   37,
 /*  1410 */   196,  197,   24,  101,   26,  176,  241,  176,  109,  107,
 /*  1420 */   108,   49,  176,  206,  196,  197,  176,  115,  176,  117,
 /*  1430 */   118,  119,   60,  225,  122,  196,  197,  196,  197,  176,
 /*  1440 */   232,  176,  196,  197,   72,  136,  196,  197,  196,  197,
 /*  1450 */   141,  139,  140,   16,  176,  259,  176,  225,   86,  196,
 /*  1460 */   197,  196,  197,   91,  232,  176,  154,  155,  156,  157,
 /*  1470 */   158,  176,  176,  101,  196,  197,  196,  197,   30,  107,
 /*  1480 */   108,  176,   34,  176,  119,  196,  197,  115,  251,  117,
 /*  1490 */   118,  119,  196,  197,  122,  176,  176,  176,   61,  176,
 /*  1500 */   176,  196,  197,  196,  197,  181,   20,  176,  285,  144,
 /*  1510 */    26,  288,  190,  191,   66,  196,  197,  196,  197,  196,
 /*  1520 */   197,  139,  140,  286,  287,   26,  154,  155,  156,  157,
 /*  1530 */   158,    0,    1,    2,   26,  176,    5,  176,   20,  176,
 /*  1540 */   181,   26,   11,   12,   13,   14,   15,  275,  276,   18,
 /*  1550 */   176,  220,   20,   21,  225,   23,   47,  196,  197,  196,
 /*  1560 */   197,  232,   31,   20,   33,   20,   21,  176,   23,   37,
 /*  1570 */   196,  197,   41,  176,   23,  176,   25,  176,  225,  176,
 /*  1580 */   234,  235,   37,  150,  151,  232,  176,  196,  197,   24,
 /*  1590 */    60,   26,   60,  196,  197,  196,  197,  196,  197,  196,
 /*  1600 */   197,  176,   71,  117,   72,   60,  196,  197,  176,  130,
 /*  1610 */    79,  280,   23,   82,   24,  130,   26,   72,   86,  176,
 /*  1620 */    23,  196,  197,   91,  216,  116,  127,  143,  196,  197,
 /*  1630 */    99,   86,  153,  101,   60,  117,   91,  176,  153,  107,
 /*  1640 */   108,   98,   24,   54,   26,  176,  101,  115,  118,  117,
 /*  1650 */   118,  119,  107,  108,  122,   24,  148,   26,  143,   62,
 /*  1660 */   115,   60,  117,  118,  119,  134,  176,  122,  121,  122,
 /*  1670 */   139,  140,   24,  176,   26,   24,   24,   26,   26,   24,
 /*  1680 */   176,   26,    7,    8,   84,   85,  154,  155,  156,  157,
 /*  1690 */   158,  233,  118,   24,  163,   26,   24,  293,   26,  154,
 /*  1700 */   155,  156,  157,  158,    1,    2,  264,   24,    5,   26,
 /*  1710 */    20,   21,  176,   23,   11,   12,   13,   14,   15,  118,
 /*  1720 */    24,   18,   26,  155,  156,  155,  156,   37,   24,  176,
 /*  1730 */    26,  176,  176,  176,   31,  176,   33,   20,   21,  176,
 /*  1740 */    23,  263,  176,  176,   41,  176,  174,  233,  224,  233,
 /*  1750 */    60,  249,  233,  273,   37,  245,  245,  237,  237,  226,
 /*  1760 */   226,  270,   72,  270,  227,  227,  249,  209,  243,  205,
 /*  1770 */   199,  249,  249,  200,   71,  199,  199,   60,  238,  238,
 /*  1780 */   238,  237,   79,  221,  238,   82,  258,  226,  182,   72,
 /*  1790 */    61,  101,   39,  152,  151,   23,  221,  107,  108,  273,
 /*  1800 */   273,  221,   99,  224,  222,  115,  271,  117,  118,  119,
 /*  1810 */    20,   21,  122,   23,  149,  261,  146,   44,  101,  271,
 /*  1820 */   222,  214,   19,  221,  107,  108,  109,   37,  250,  248,
 /*  1830 */   217,  141,  115,  217,  117,  118,  119,  134,  217,  122,
 /*  1840 */   217,   19,  139,  140,  154,  155,  156,  157,  158,  150,
 /*  1850 */    60,  180,  227,  180,  227,  250,  227,  248,  250,  227,
 /*  1860 */   214,  214,   72,   63,  159,  266,  163,  180,  265,   23,
 /*  1870 */   116,  154,  155,  156,  157,  158,   65,  201,  180,  201,
 /*  1880 */   198,  207,  198,   20,   21,   23,   23,  198,  127,  204,
 /*  1890 */   204,  101,  201,  198,  200,  198,  166,  107,  108,  207,
 /*  1900 */    37,  287,  222,   25,  198,  115,  198,  117,  118,  119,
 /*  1910 */   114,  281,  122,  260,  260,  201,   23,  255,   92,   83,
 /*  1920 */   292,  242,  159,   60,  257,  292,  147,  165,  227,  229,
 /*  1930 */   242,  141,  222,  222,   26,   72,  184,  228,  180,   14,
 /*  1940 */     6,  177,  177,  175,  154,  155,  156,  157,  158,  175,
 /*  1950 */   175,  202,  202,    4,    3,  189,  279,   16,  189,  189,
 /*  1960 */   279,   23,   61,   16,  101,  189,   24,   17,   24,  276,
 /*  1970 */   107,  108,  140,  152,  131,   21,  143,   25,  115,   26,
 /*  1980 */   117,  118,  119,  145,   17,  122,    5,   23,  116,  162,
 /*  1990 */    69,   26,   11,   12,   13,   14,   15,   69,   76,   18,
 /*  2000 */    42,    1,  143,  152,  131,  131,   38,   62,  131,   54,
 /*  2010 */    54,   54,   31,   54,   33,  117,    1,  154,  155,  156,
 /*  2020 */   157,  158,   41,   35,  142,    5,  116,   25,   21,   20,
 /*  2030 */   126,  132,   24,   23,   68,   23,   23,   23,   68,   60,
 /*  2040 */    97,   25,   23,   68,   38,   24,   29,  150,   23,   26,
 /*  2050 */    24,   24,   71,   24,   24,   23,  143,  143,   98,   24,
 /*  2060 */    79,   24,   24,   82,   24,  136,  142,   26,   24,   35,
 /*  2070 */    23,  144,   89,   76,   35,   76,   87,   35,  117,   94,
 /*  2080 */    99,   35,   26,   35,   35,   24,   23,   12,   45,   24,
 /*  2090 */    26,   24,   23,   23,   26,   23,   25,   24,   24,   23,
 /*  2100 */    23,   35,   24,   16,  142,   26,   26,   24,    1,    1,
 /*  2110 */   294,  142,  294,  142,  142,  134,  294,  294,  294,  294,
 /*  2120 */   139,  140,  294,  294,  294,  294,  294,  294,  294,  294,
 /*  2130 */   294,  294,  294,  294,  294,  294,  294,  294,  294,  294,
 /*  2140 */   294,  294,  294,  294,  163,  294,  294,  294,  294,  294,
 /*  2150 */   294,  294,  294,  294,  294,  294,  294,  294,  294,  294,
 /*  2160 */   294,  294,  294,  294,  294,  294,  294,  294,  294,  294,
 /*  2170 */   294,  294,  294,  294,  294,  294,  294,  294,  294,  294,
 /*  2180 */   294,  294,  294,  294,  294,  294,  294,  294,  294,  294,
 /*  2190 */   294,  294,  294,  294,  294,  294,  294,  294,  294,  294,
 /*  2200 */   294,  294,  294,  294,  294,  294,  294,  294,  294,  294,
 /*  2210 */   294,  294,  294,  294,  294,  294,  294,  294,  294,  294,
 /*  2220 */   294,  294,  294,  294,  294,  294,  294,  294,  294,  294,
 /*  2230 */   294,  294,  294,  294,  294,  294,  294,  294,  294,  294,
 /*  2240 */   294,  294,  294,  294,  294,  294,  294,  294,  294,  294,
 /*  2250 */   294,  294,  294,  294,  294,  294,  294,  294,  294,  294,
 /*  2260 */   294,  294,  294,  294,  294,  294,  294,  294,  294,  294,
 /*  2270 */   294,  294,  294,  294,  294,  294,  294,  294,  294,  294,
 /*  2280 */   294,  294,  294,  168,  168,  168,  168,  168,  168,  168,
 /*  2290 */   168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
 /*  2300 */   168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
 /*  2310 */   168,  168,  168,
];
#[allow(non_camel_case_types)]
type YY_SHIFT_TYPE = u16;
const YY_SHIFT_COUNT: YYACTIONTYPE =    562;
//const YY_SHIFT_MIN: YY_SHIFT_TYPE =      0;
//const YY_SHIFT_MAX: YY_SHIFT_TYPE =      2108;
#[rustfmt::skip]
#[allow(non_upper_case_globals)]
static yy_shift_ofst: [YY_SHIFT_TYPE; 563] = [
 /*     0 */  1703, 1531, 1312, 1981, 1312,  148,  151,   48, 1372, 1532,
 /*    10 */  1545, 1863, 1863, 1863,    0,  104,  104,  287, 1863, 1863,
 /*    20 */  1863, 1863, 1863, 1863, 1863, 1863, 1863, 1863, 1863, 1863,
 /*    30 */  1863,  965,  965,  396,  396,   14,  337,   48,   48,   48,
 /*    40 */    48,   48,  145,  216,  326,  397,  505,  549,  620,  659,
 /*    50 */   730,  769,  840,  879,  950, 1111, 1132, 1132, 1132, 1132,
 /*    60 */  1132, 1132, 1132, 1132, 1132, 1132, 1132, 1132, 1132, 1132,
 /*    70 */  1132, 1132, 1132, 1132, 1153, 1132, 1257,  994,  994, 1690,
 /*    80 */  1717, 1790, 1863, 1863, 1863, 1863, 1863, 1863, 1863, 1863,
 /*    90 */  1863, 1863, 1863, 1863, 1863, 1863, 1863, 1863, 1863, 1863,
 /*   100 */  1863, 1863, 1863, 1863, 1863, 1863, 1863, 1863, 1863, 1863,
 /*   110 */  1863, 1863, 1863, 1863, 1863, 1863, 1863, 1863, 1863, 1863,
 /*   120 */  1863, 1863, 1863, 1863, 1863, 1863, 1863, 1863, 1863, 1863,
 /*   130 */  1863,  253,  356,  356,  356,  356,  356,  356,  356,   63,
 /*   140 */    72,  183,  569,  576,  288,  309, 1207,  309,  194,  194,
 /*   150 */   309,  309,  541,  541,  541,  431,  541,  118,  118,   27,
 /*   160 */    27, 2145, 2145,  398,  398,  398,  398,  364,  677,  677,
 /*   170 */   677,  677, 1052, 1052,  732,  332,  738,  288, 1051, 1091,
 /*   180 */   309,  309,  309,  309,  309,  309,  309,  309,  309,  309,
 /*   190 */   309,  309,  309,  309,  309,  309,  309,  309,  309,  309,
 /*   200 */   309,  309,  309,  309,  309,  309,  309,  497,  309,  309,
 /*   210 */   309,  395,  395,  309,  497,  497,  309,  667,  878,  878,
 /*   220 */  1032, 1032, 1382, 2145, 2145, 2145, 2145, 2145, 2145, 2145,
 /*   230 */   467,  470,  470,  785,  180,  788,  419,  684,  687,  309,
 /*   240 */   309,  309,  309,  309,  309,  309,  509,  309,  309,  309,
 /*   250 */   309,  309,  309, 1060, 1060, 1060,  309,  309,  309,  890,
 /*   260 */   309,  309,  309,  510, 1183,  309, 1106,  309,  309,  309,
 /*   270 */   309,  309,  309,  309,  309,  309,  309,  219,  733, 1448,
 /*   280 */     8,    8,    8, 1365, 1309, 1437,  823, 1433, 1484, 1486,
 /*   290 */  1433, 1486, 1518,  491,  823,  823,  491,  823, 1484, 1518,
 /*   300 */  1499, 1388, 1509,  266,  266,  266, 1234, 1234, 1234, 1234,
 /*   310 */  1486, 1508,  887,  887, 1170, 1515,  339, 1551, 1729, 1729,
 /*   320 */  1753, 1753, 1641, 1643, 1772, 1665, 1670, 1773, 1665, 1670,
 /*   330 */  1803, 1803, 1803, 1803, 1822, 1699, 1822, 1699, 1643, 1643,
 /*   340 */  1699, 1772, 1699, 1773, 1773, 1705, 1800, 1822, 1846, 1822,
 /*   350 */  1846, 1754, 1754, 1754, 1811, 1862, 1862, 1846, 1754, 1761,
 /*   360 */  1754, 1811, 1754, 1754, 1730, 1665, 1878, 1796, 1796, 1846,
 /*   370 */  1893, 1826, 1826, 1836, 1836, 1763, 1762, 1779, 1699, 1665,
 /*   380 */  1665, 1908, 1822, 1925, 1925, 1934, 1934, 1934, 2145, 2145,
 /*   390 */  2145, 2145, 2145, 2145, 2145, 2145, 2145,  196,  252, 1013,
 /*   400 */   728,  877,  782,  958,  330, 1565, 1589, 1012, 1590, 1479,
 /*   410 */  1485, 1618, 1597, 1631, 1648, 1651, 1652, 1655, 1530, 1547,
 /*   420 */  1675,  888, 1543, 1574, 1669, 1672, 1600, 1683, 1696, 1568,
 /*   430 */  1570, 1704, 1601, 1247, 1949, 1951, 1938, 1941, 1901, 1947,
 /*   440 */  1950, 1942, 1944, 1832, 1821, 1843, 1953, 1953, 1952, 1833,
 /*   450 */  1954, 1838, 1967, 1964, 1872, 1827, 1921, 1965, 1928, 1922,
 /*   460 */  1958, 2000, 1859, 1873, 1953, 1874, 1945, 1968, 1953, 1851,
 /*   470 */  1955, 1956, 1957, 1959, 1877, 1898, 1988, 1882, 2015, 2020,
 /*   480 */  1910, 2002, 2007, 2009, 1899, 1904, 2010, 1966, 2012, 2013,
 /*   490 */  2008, 2014, 1970, 1979, 2016, 1943, 2017, 2019, 1975, 2006,
 /*   500 */  2021, 1897, 2025, 2026, 2027, 2029, 2023, 2030, 2032, 1960,
 /*   510 */  1913, 1914, 2035, 2037, 2038, 1929, 1924, 2040, 2041, 2044,
 /*   520 */  1961, 2034, 2047, 1927, 2039, 2042, 2046, 2048, 1983, 1997,
 /*   530 */  1989, 2043, 1999, 1985, 2056, 2049, 2061, 2063, 2065, 2064,
 /*   540 */  2067, 2069, 2075, 2070, 2068, 2072, 2073, 2074, 2076, 2066,
 /*   550 */  2078, 2077, 2071, 2079, 1962, 1969, 1971, 1972, 2080, 2083,
 /*   560 */  2087, 2107, 2108,
];
#[allow(non_camel_case_types)]
type YY_REDUCE_TYPE = i16;
const YY_REDUCE_COUNT: YYACTIONTYPE = 396;
//const YY_REDUCE_MIN: YY_REDUCE_TYPE =   -249;
//const YY_REDUCE_MAX: YY_REDUCE_TYPE =   1776;
#[rustfmt::skip]
#[allow(non_upper_case_globals)]
static yy_reduce_ofst: [YY_REDUCE_TYPE; 397] = [
 /*     0 */  -157,  794, -154, -147,  893, -152, -149,  346, -169,  -54,
 /*    10 */   -51,   44,  295,  451, -153, -164, -161, -249,  192,  525,
 /*    20 */   656,  745,  454,  762,  764,  522,  933,  835,  390,  839,
 /*    30 */   891, -176,  123,  211,  455,  937,  -90,  360, 1061, 1063,
 /*    40 */  1065, 1067, -244, -244, -244, -244, -244, -244, -244, -244,
 /*    50 */  -244, -244, -244, -244, -244, -244, -244, -244, -244, -244,
 /*    60 */  -244, -244, -244, -244, -244, -244, -244, -244, -244, -244,
 /*    70 */  -244, -244, -244, -244, -244, -244, -244, -244, -244,  562,
 /*    80 */   781,  942,  998, 1016, 1076, 1092, 1120, 1122, 1124, 1146,
 /*    90 */  1148, 1150, 1158, 1160, 1162, 1177, 1200, 1202, 1204, 1206,
 /*   100 */  1209, 1211, 1214, 1228, 1239, 1241, 1246, 1250, 1252, 1263,
 /*   110 */  1265, 1278, 1280, 1289, 1296, 1305, 1307, 1319, 1321, 1323,
 /*   120 */  1361, 1363, 1374, 1391, 1397, 1399, 1401, 1403, 1410, 1425,
 /*   130 */  1432, -244, -244, -244, -244, -244, -244, -244, -244, -244,
 /*   140 */  -244, -244,  311,  689,  683,  952,   18, 1331,   12,  347,
 /*   150 */    71,  508,  527,  628,  527, -244,  628, 1041, 1237, -244,
 /*   160 */  -244, -244, -244, -101, -101, -101, -101,  -97,   43,  228,
 /*   170 */   544,  566, -214, -175,  317,  414,  401,  623,  640,  640,
 /*   180 */  -113,  -53,  457,  516,  690,  739,  841,  786,  787,  845,
 /*   190 */   847,  956,  967,  970, 1050, 1053,  559,  902,  678,  964,
 /*   200 */  1099, 1100, 1079, 1118, 1205, 1324, 1125, -139, 1176,    2,
 /*   210 */  1175,  731,  986,  693,  586, 1007, 1359, 1196, 1002, 1223,
 /*   220 */   951, 1322, -200, 1272,  524, 1208, 1232, 1329, 1346, 1353,
 /*   230 */  -190, -160, -137,  138,  247,  226,  321,  371,  444,  565,
 /*   240 */   584,  621,  746,  853,  895,  957,  -23, 1020, 1072, 1149,
 /*   250 */  1201, 1295, 1320,  366,  702, 1217, 1443, 1461, 1469, 1147,
 /*   260 */  1490, 1497, 1504, 1458, 1404, 1536, 1408, 1553, 1555,  321,
 /*   270 */  1556, 1557, 1559, 1563, 1566, 1567, 1569, 1572, 1442, 1478,
 /*   280 */  1514, 1516, 1519, 1147, 1524, 1480, 1502, 1510, 1533, 1520,
 /*   290 */  1511, 1521, 1491, 1537, 1517, 1522, 1538, 1523, 1534, 1493,
 /*   300 */  1573, 1558, 1564, 1571, 1576, 1577, 1540, 1541, 1542, 1546,
 /*   310 */  1544, 1525, 1562, 1575, 1528, 1561, 1579, 1606, 1526, 1527,
 /*   320 */  1535, 1548, 1554, 1578, 1581, 1582, 1580, 1607, 1598, 1602,
 /*   330 */  1613, 1616, 1621, 1623, 1671, 1625, 1673, 1627, 1605, 1608,
 /*   340 */  1629, 1609, 1632, 1646, 1647, 1599, 1603, 1687, 1676, 1698,
 /*   350 */  1678, 1682, 1684, 1689, 1674, 1685, 1686, 1691, 1695, 1694,
 /*   360 */  1697, 1692, 1706, 1708, 1614, 1680, 1630, 1653, 1654, 1714,
 /*   370 */  1662, 1628, 1633, 1679, 1688, 1667, 1700, 1709, 1701, 1710,
 /*   380 */  1711, 1752, 1758, 1764, 1765, 1768, 1774, 1775, 1677, 1681,
 /*   390 */  1693, 1766, 1769, 1770, 1749, 1750, 1776,
];
#[rustfmt::skip]
#[allow(non_upper_case_globals)]
static yy_default: [YYACTIONTYPE; 563] = [
 /*     0 */  1571, 1571, 1492, 1571, 1235, 1235, 1235, 1367, 1235, 1235,
 /*    10 */  1235, 1492, 1492, 1492, 1363, 1395, 1395, 1553, 1235, 1235,
 /*    20 */  1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235, 1490, 1235,
 /*    30 */  1235, 1235, 1235, 1589, 1589, 1235, 1235, 1235, 1235, 1235,
 /*    40 */  1235, 1235, 1235, 1404, 1235, 1411, 1235, 1235, 1235, 1235,
 /*    50 */  1235, 1493, 1494, 1235, 1235, 1235, 1552, 1554, 1509, 1535,
 /*    60 */  1418, 1417, 1416, 1415, 1406, 1383, 1409, 1402, 1486, 1487,
 /*    70 */  1485, 1489, 1493, 1494, 1235, 1405, 1453, 1470, 1452, 1235,
 /*    80 */  1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235,
 /*    90 */  1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235,
 /*   100 */  1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235,
 /*   110 */  1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235,
 /*   120 */  1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235,
 /*   130 */  1235, 1462, 1469, 1468, 1467, 1476, 1466, 1463, 1455, 1454,
 /*   140 */  1456, 1457, 1235, 1235, 1262, 1235, 1326, 1235, 1235, 1235,
 /*   150 */  1235, 1235, 1573, 1572, 1235, 1458, 1235, 1447, 1446, 1473,
 /*   160 */  1459, 1472, 1471, 1563, 1560, 1268, 1267, 1510, 1235, 1235,
 /*   170 */  1235, 1235, 1235, 1235, 1235, 1589, 1235, 1235, 1235, 1235,
 /*   180 */  1235, 1235, 1235, 1235, 1557, 1555, 1235, 1235, 1235, 1235,
 /*   190 */  1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235,
 /*   200 */  1235, 1235, 1235, 1235, 1235, 1235, 1235, 1389, 1235, 1235,
 /*   210 */  1235, 1589, 1589, 1235, 1389, 1389, 1235, 1259, 1589, 1589,
 /*   220 */  1272, 1272, 1235, 1567, 1357, 1357, 1357, 1357, 1367, 1357,
 /*   230 */  1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235,
 /*   240 */  1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235,
 /*   250 */  1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235,
 /*   260 */  1235, 1235, 1235, 1363, 1235, 1235, 1235, 1235, 1235, 1235,
 /*   270 */  1235, 1266, 1235, 1235, 1235, 1235, 1243, 1245, 1235, 1527,
 /*   280 */  1363, 1363, 1363, 1365, 1356, 1636, 1421, 1410, 1364, 1386,
 /*   290 */  1410, 1386, 1633, 1408, 1421, 1421, 1408, 1421, 1364, 1633,
 /*   300 */  1298, 1627, 1291, 1395, 1395, 1395, 1385, 1385, 1385, 1385,
 /*   310 */  1386, 1401, 1389, 1389, 1488, 1364, 1356, 1235, 1636, 1636,
 /*   320 */  1635, 1635, 1510, 1428, 1431, 1403, 1389, 1330, 1403, 1389,
 /*   330 */  1336, 1336, 1336, 1336, 1256, 1408, 1256, 1408, 1428, 1428,
 /*   340 */  1408, 1431, 1408, 1330, 1330, 1534, 1532, 1256, 1500, 1256,
 /*   350 */  1500, 1328, 1328, 1328, 1313, 1235, 1235, 1500, 1328, 1298,
 /*   360 */  1328, 1313, 1328, 1328, 1611, 1403, 1235, 1504, 1504, 1500,
 /*   370 */  1495, 1602, 1602, 1398, 1398, 1235, 1349, 1399, 1408, 1403,
 /*   380 */  1403, 1316, 1256, 1622, 1622, 1242, 1242, 1242, 1641, 1641,
 /*   390 */  1567, 1279, 1279, 1279, 1300, 1300, 1279, 1235, 1235, 1235,
 /*   400 */  1235, 1235, 1235, 1273, 1235, 1235, 1511, 1374, 1235, 1235,
 /*   410 */  1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235,
 /*   420 */  1235, 1235, 1574, 1235, 1235, 1235, 1235, 1235, 1235, 1235,
 /*   430 */  1235, 1235, 1235, 1436, 1235, 1238, 1564, 1235, 1235, 1235,
 /*   440 */  1235, 1235, 1235, 1235, 1235, 1235, 1412, 1413, 1375, 1235,
 /*   450 */  1235, 1235, 1235, 1235, 1235, 1235, 1235, 1531, 1530, 1235,
 /*   460 */  1235, 1235, 1235, 1235, 1427, 1235, 1235, 1235, 1422, 1235,
 /*   470 */  1235, 1235, 1235, 1235, 1235, 1235, 1235, 1632, 1235, 1235,
 /*   480 */  1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235,
 /*   490 */  1235, 1235, 1235, 1296, 1235, 1235, 1235, 1235, 1235, 1235,
 /*   500 */  1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235,
 /*   510 */  1235, 1235, 1235, 1235, 1235, 1345, 1235, 1235, 1491, 1235,
 /*   520 */  1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235, 1235,
 /*   530 */  1235, 1235, 1235, 1235, 1390, 1235, 1235, 1235, 1235, 1235,
 /*   540 */  1235, 1235, 1235, 1235, 1351, 1235, 1235, 1235, 1235, 1235,
 /*   550 */  1235, 1235, 1235, 1608, 1372, 1437, 1235, 1441, 1260, 1235,
 /*   560 */  1251, 1235, 1235,
];
/********** End of lemon-generated parsing tables *****************************/

/* The next table maps tokens (terminal symbols) into fallback tokens.
** If a construct like the following:
**
**      %fallback ID X Y Z.
**
** appears in the grammar, then ID becomes a fallback token for X, Y,
** and Z.  Whenever one of the tokens X, Y, or Z is input to the parser
** but it does not parse, the type of the token is changed to ID and
** the parse is retried before an error is thrown.
**
** This feature can be used, for example, to cause some keywords in a language
** to revert to identifiers if they keyword does not apply in the context where
** it appears.
*/
#[allow(non_upper_case_globals)]
static yyFallback: [YYCODETYPE; 168] = [
    0,  /*          $ => nothing */
    0,  /*       SEMI => nothing */
   60,  /*    EXPLAIN => ID */
   60,  /*      QUERY => ID */
   60,  /*       PLAN => ID */
   60,  /*      BEGIN => ID */
    0,  /* TRANSACTION => nothing */
   60,  /*   DEFERRED => ID */
   60,  /*  IMMEDIATE => ID */
   60,  /*  EXCLUSIVE => ID */
   60,  /*   READONLY => ID */
    0,  /*     COMMIT => nothing */
   60,  /*        END => ID */
   60,  /*   ROLLBACK => ID */
   60,  /*  SAVEPOINT => ID */
   60,  /*    RELEASE => ID */
    0,  /*         TO => nothing */
    0,  /*      TABLE => nothing */
    0,  /*     CREATE => nothing */
   60,  /*         IF => ID */
    0,  /*        NOT => nothing */
    0,  /*     EXISTS => nothing */
   60,  /*       TEMP => ID */
    0,  /*         LP => nothing */
    0,  /*         RP => nothing */
    0,  /*         AS => nothing */
    0,  /*      COMMA => nothing */
   60,  /*    WITHOUT => ID */
   60,  /*      ABORT => ID */
   60,  /*     ACTION => ID */
   60,  /*      AFTER => ID */
   60,  /*    ANALYZE => ID */
   60,  /*        ASC => ID */
   60,  /*     ATTACH => ID */
   60,  /*     BEFORE => ID */
   60,  /*         BY => ID */
   60,  /*    CASCADE => ID */
   60,  /*       CAST => ID */
   60,  /*   CONFLICT => ID */
   60,  /*   DATABASE => ID */
   60,  /*       DESC => ID */
   60,  /*     DETACH => ID */
   60,  /*       EACH => ID */
   60,  /*       FAIL => ID */
    0,  /*         OR => nothing */
    0,  /*        AND => nothing */
    0,  /*         IS => nothing */
   60,  /*      MATCH => ID */
   60,  /*    LIKE_KW => ID */
    0,  /*    BETWEEN => nothing */
    0,  /*         IN => nothing */
    0,  /*     ISNULL => nothing */
    0,  /*    NOTNULL => nothing */
    0,  /*         NE => nothing */
    0,  /*         EQ => nothing */
    0,  /*         GT => nothing */
    0,  /*         LE => nothing */
    0,  /*         LT => nothing */
    0,  /*         GE => nothing */
    0,  /*     ESCAPE => nothing */
    0,  /*         ID => nothing */
   60,  /*   COLUMNKW => ID */
   60,  /*         DO => ID */
   60,  /*        FOR => ID */
   60,  /*     IGNORE => ID */
   60,  /*  INITIALLY => ID */
   60,  /*    INSTEAD => ID */
   60,  /*         NO => ID */
   60,  /*        KEY => ID */
   60,  /*         OF => ID */
   60,  /*     OFFSET => ID */
   60,  /*     PRAGMA => ID */
   60,  /*      RAISE => ID */
   60,  /*  RECURSIVE => ID */
   60,  /*    REPLACE => ID */
   60,  /*   RESTRICT => ID */
   60,  /*        ROW => ID */
   60,  /*       ROWS => ID */
   60,  /*    TRIGGER => ID */
   60,  /*     VACUUM => ID */
   60,  /*       VIEW => ID */
   60,  /*    VIRTUAL => ID */
   60,  /*       WITH => ID */
   60,  /*      NULLS => ID */
   60,  /*      FIRST => ID */
   60,  /*       LAST => ID */
   60,  /*    CURRENT => ID */
   60,  /*  FOLLOWING => ID */
   60,  /*  PARTITION => ID */
   60,  /*  PRECEDING => ID */
   60,  /*      RANGE => ID */
   60,  /*  UNBOUNDED => ID */
   60,  /*    EXCLUDE => ID */
   60,  /*     GROUPS => ID */
   60,  /*     OTHERS => ID */
   60,  /*       TIES => ID */
   60,  /*  GENERATED => ID */
   60,  /*     ALWAYS => ID */
   60,  /* MATERIALIZED => ID */
   60,  /*    REINDEX => ID */
   60,  /*     RENAME => ID */
   60,  /*   CTIME_KW => ID */
    0,  /*        ANY => nothing */
    0,  /*     BITAND => nothing */
    0,  /*      BITOR => nothing */
    0,  /*     LSHIFT => nothing */
    0,  /*     RSHIFT => nothing */
    0,  /*       PLUS => nothing */
    0,  /*      MINUS => nothing */
    0,  /*       STAR => nothing */
    0,  /*      SLASH => nothing */
    0,  /*        REM => nothing */
    0,  /*     CONCAT => nothing */
    0,  /*        PTR => nothing */
    0,  /*    COLLATE => nothing */
    0,  /*     BITNOT => nothing */
    0,  /*         ON => nothing */
    0,  /*    INDEXED => nothing */
    0,  /*     STRING => nothing */
    0,  /*    JOIN_KW => nothing */
    0,  /* CONSTRAINT => nothing */
    0,  /*    DEFAULT => nothing */
    0,  /*       NULL => nothing */
    0,  /*    PRIMARY => nothing */
    0,  /*     UNIQUE => nothing */
    0,  /*      CHECK => nothing */
    0,  /* REFERENCES => nothing */
    0,  /*   AUTOINCR => nothing */
    0,  /*     INSERT => nothing */
    0,  /*     DELETE => nothing */
    0,  /*     UPDATE => nothing */
    0,  /*        SET => nothing */
    0,  /* DEFERRABLE => nothing */
    0,  /*    FOREIGN => nothing */
    0,  /*       DROP => nothing */
    0,  /*      UNION => nothing */
    0,  /*        ALL => nothing */
    0,  /*     EXCEPT => nothing */
    0,  /*  INTERSECT => nothing */
    0,  /*     SELECT => nothing */
    0,  /*     VALUES => nothing */
    0,  /*   DISTINCT => nothing */
    0,  /*        DOT => nothing */
    0,  /*       FROM => nothing */
    0,  /*       JOIN => nothing */
    0,  /*      USING => nothing */
    0,  /*      ORDER => nothing */
    0,  /*      GROUP => nothing */
    0,  /*     HAVING => nothing */
    0,  /*      LIMIT => nothing */
    0,  /*      WHERE => nothing */
    0,  /*  RETURNING => nothing */
    0,  /*       INTO => nothing */
    0,  /*    NOTHING => nothing */
    0,  /*       BLOB => nothing */
    0,  /*      FLOAT => nothing */
    0,  /*    INTEGER => nothing */
    0,  /*   VARIABLE => nothing */
    0,  /*       CASE => nothing */
    0,  /*       WHEN => nothing */
    0,  /*       THEN => nothing */
    0,  /*       ELSE => nothing */
    0,  /*      INDEX => nothing */
    0,  /*      ALTER => nothing */
    0,  /*        ADD => nothing */
    0,  /*     WINDOW => nothing */
    0,  /*       OVER => nothing */
    0,  /*     FILTER => nothing */
];

/* The following structure represents a single element of the
** parser's stack.  Information stored includes:
**
**   +  The state number for the parser at this level of the stack.
**
**   +  The value of the token stored at this level of the stack.
**      (In other words, the "major" token.)
**
**   +  The semantic value stored at this level of the stack.  This is
**      the information used by the action routines in the grammar.
**      It is sometimes called the "minor" token.
**
** After the "shift" half of a SHIFTREDUCE action, the stateno field
** actually contains the reduce action for the second half of the
** SHIFTREDUCE.
*/
#[allow(non_camel_case_types)]
#[derive(Default)]
pub struct yyStackEntry {
    stateno: YYACTIONTYPE, /* The state-number, or reduce action in SHIFTREDUCE */
    major: YYCODETYPE,     /* The major token value.  This is the code
                            ** number for the token at this stack level */
    minor: YYMINORTYPE, /* The user-supplied minor token value.  This
                         ** is the value of the token  */
}

/* The state of the parser is completely contained in an instance of
** the following structure */
#[allow(non_camel_case_types)]
pub struct yyParser<'input> {
    yyidx: usize, /* Index to top element of the stack */
    #[cfg(feature = "YYTRACKMAXSTACKDEPTH")]
    yyhwm: usize, /* High-water mark of the stack */
    //#[cfg(not(feature = "YYNOERRORRECOVERY"))]
    yyerrcnt: i32, /* Shifts left before out of the error */
    pub ctx: Context<'input>,
    yystack: Vec<yyStackEntry>, /* The parser's stack */
}

use std::cmp::Ordering;
use std::ops::Neg;
impl yyParser<'_> {
    fn shift(&self, shift: i8) -> usize {
        assert!(shift <= 1);
        match shift.cmp(&0) {
            Ordering::Equal => self.yyidx,
            Ordering::Greater => self.yyidx + shift as usize,
            Ordering::Less => self.yyidx.checked_sub(shift.neg() as usize).unwrap(),
        }
    }

    fn yyidx_shift(&mut self, shift: i8) {
        match shift.cmp(&0) {
            Ordering::Greater => self.yyidx += shift as usize,
            Ordering::Less => self.yyidx = self.yyidx.checked_sub(shift.neg() as usize).unwrap(),
            Ordering::Equal => {}
        }
    }

    fn yy_move(&mut self, shift: i8) -> yyStackEntry {
        use std::mem::take;
        let idx = self.shift(shift);
        take(&mut self.yystack[idx])
    }

    fn push(&mut self, entry: yyStackEntry) {
        if self.yyidx == self.yystack.len() {
            self.yystack.push(entry);
        } else {
            self.yystack[self.yyidx] = entry;
        }
    }
}

use std::ops::{Index, IndexMut};
impl Index<i8> for yyParser<'_> {
    type Output = yyStackEntry;

    fn index(&self, shift: i8) -> &yyStackEntry {
        let idx = self.shift(shift);
        &self.yystack[idx]
    }
}
impl IndexMut<i8> for yyParser<'_> {
    fn index_mut(&mut self, shift: i8) -> &mut yyStackEntry {
        let idx = self.shift(shift);
        &mut self.yystack[idx]
    }
}

#[cfg(not(feature = "NDEBUG"))]
use log::Level::Debug;
#[cfg(not(feature = "NDEBUG"))]
static TARGET: &str = "sqlite3Parser";

/* For tracing shifts, the names of all terminals and nonterminals
** are required.  The following table supplies these names */
#[cfg(any(feature = "YYCOVERAGE", not(feature = "NDEBUG")))]
#[rustfmt::skip]
#[allow(non_upper_case_globals)]
static yyTokenName: [&str; 294] = [
  /*    0 */ "$",
  /*    1 */ "SEMI",
  /*    2 */ "EXPLAIN",
  /*    3 */ "QUERY",
  /*    4 */ "PLAN",
  /*    5 */ "BEGIN",
  /*    6 */ "TRANSACTION",
  /*    7 */ "DEFERRED",
  /*    8 */ "IMMEDIATE",
  /*    9 */ "EXCLUSIVE",
  /*   10 */ "READONLY",
  /*   11 */ "COMMIT",
  /*   12 */ "END",
  /*   13 */ "ROLLBACK",
  /*   14 */ "SAVEPOINT",
  /*   15 */ "RELEASE",
  /*   16 */ "TO",
  /*   17 */ "TABLE",
  /*   18 */ "CREATE",
  /*   19 */ "IF",
  /*   20 */ "NOT",
  /*   21 */ "EXISTS",
  /*   22 */ "TEMP",
  /*   23 */ "LP",
  /*   24 */ "RP",
  /*   25 */ "AS",
  /*   26 */ "COMMA",
  /*   27 */ "WITHOUT",
  /*   28 */ "ABORT",
  /*   29 */ "ACTION",
  /*   30 */ "AFTER",
  /*   31 */ "ANALYZE",
  /*   32 */ "ASC",
  /*   33 */ "ATTACH",
  /*   34 */ "BEFORE",
  /*   35 */ "BY",
  /*   36 */ "CASCADE",
  /*   37 */ "CAST",
  /*   38 */ "CONFLICT",
  /*   39 */ "DATABASE",
  /*   40 */ "DESC",
  /*   41 */ "DETACH",
  /*   42 */ "EACH",
  /*   43 */ "FAIL",
  /*   44 */ "OR",
  /*   45 */ "AND",
  /*   46 */ "IS",
  /*   47 */ "MATCH",
  /*   48 */ "LIKE_KW",
  /*   49 */ "BETWEEN",
  /*   50 */ "IN",
  /*   51 */ "ISNULL",
  /*   52 */ "NOTNULL",
  /*   53 */ "NE",
  /*   54 */ "EQ",
  /*   55 */ "GT",
  /*   56 */ "LE",
  /*   57 */ "LT",
  /*   58 */ "GE",
  /*   59 */ "ESCAPE",
  /*   60 */ "ID",
  /*   61 */ "COLUMNKW",
  /*   62 */ "DO",
  /*   63 */ "FOR",
  /*   64 */ "IGNORE",
  /*   65 */ "INITIALLY",
  /*   66 */ "INSTEAD",
  /*   67 */ "NO",
  /*   68 */ "KEY",
  /*   69 */ "OF",
  /*   70 */ "OFFSET",
  /*   71 */ "PRAGMA",
  /*   72 */ "RAISE",
  /*   73 */ "RECURSIVE",
  /*   74 */ "REPLACE",
  /*   75 */ "RESTRICT",
  /*   76 */ "ROW",
  /*   77 */ "ROWS",
  /*   78 */ "TRIGGER",
  /*   79 */ "VACUUM",
  /*   80 */ "VIEW",
  /*   81 */ "VIRTUAL",
  /*   82 */ "WITH",
  /*   83 */ "NULLS",
  /*   84 */ "FIRST",
  /*   85 */ "LAST",
  /*   86 */ "CURRENT",
  /*   87 */ "FOLLOWING",
  /*   88 */ "PARTITION",
  /*   89 */ "PRECEDING",
  /*   90 */ "RANGE",
  /*   91 */ "UNBOUNDED",
  /*   92 */ "EXCLUDE",
  /*   93 */ "GROUPS",
  /*   94 */ "OTHERS",
  /*   95 */ "TIES",
  /*   96 */ "GENERATED",
  /*   97 */ "ALWAYS",
  /*   98 */ "MATERIALIZED",
  /*   99 */ "REINDEX",
  /*  100 */ "RENAME",
  /*  101 */ "CTIME_KW",
  /*  102 */ "ANY",
  /*  103 */ "BITAND",
  /*  104 */ "BITOR",
  /*  105 */ "LSHIFT",
  /*  106 */ "RSHIFT",
  /*  107 */ "PLUS",
  /*  108 */ "MINUS",
  /*  109 */ "STAR",
  /*  110 */ "SLASH",
  /*  111 */ "REM",
  /*  112 */ "CONCAT",
  /*  113 */ "PTR",
  /*  114 */ "COLLATE",
  /*  115 */ "BITNOT",
  /*  116 */ "ON",
  /*  117 */ "INDEXED",
  /*  118 */ "STRING",
  /*  119 */ "JOIN_KW",
  /*  120 */ "CONSTRAINT",
  /*  121 */ "DEFAULT",
  /*  122 */ "NULL",
  /*  123 */ "PRIMARY",
  /*  124 */ "UNIQUE",
  /*  125 */ "CHECK",
  /*  126 */ "REFERENCES",
  /*  127 */ "AUTOINCR",
  /*  128 */ "INSERT",
  /*  129 */ "DELETE",
  /*  130 */ "UPDATE",
  /*  131 */ "SET",
  /*  132 */ "DEFERRABLE",
  /*  133 */ "FOREIGN",
  /*  134 */ "DROP",
  /*  135 */ "UNION",
  /*  136 */ "ALL",
  /*  137 */ "EXCEPT",
  /*  138 */ "INTERSECT",
  /*  139 */ "SELECT",
  /*  140 */ "VALUES",
  /*  141 */ "DISTINCT",
  /*  142 */ "DOT",
  /*  143 */ "FROM",
  /*  144 */ "JOIN",
  /*  145 */ "USING",
  /*  146 */ "ORDER",
  /*  147 */ "GROUP",
  /*  148 */ "HAVING",
  /*  149 */ "LIMIT",
  /*  150 */ "WHERE",
  /*  151 */ "RETURNING",
  /*  152 */ "INTO",
  /*  153 */ "NOTHING",
  /*  154 */ "BLOB",
  /*  155 */ "FLOAT",
  /*  156 */ "INTEGER",
  /*  157 */ "VARIABLE",
  /*  158 */ "CASE",
  /*  159 */ "WHEN",
  /*  160 */ "THEN",
  /*  161 */ "ELSE",
  /*  162 */ "INDEX",
  /*  163 */ "ALTER",
  /*  164 */ "ADD",
  /*  165 */ "WINDOW",
  /*  166 */ "OVER",
  /*  167 */ "FILTER",
  /*  168 */ "input",
  /*  169 */ "cmdlist",
  /*  170 */ "ecmd",
  /*  171 */ "cmdx",
  /*  172 */ "explain",
  /*  173 */ "cmd",
  /*  174 */ "transtype",
  /*  175 */ "trans_opt",
  /*  176 */ "nm",
  /*  177 */ "savepoint_opt",
  /*  178 */ "createkw",
  /*  179 */ "temp",
  /*  180 */ "ifnotexists",
  /*  181 */ "fullname",
  /*  182 */ "create_table_args",
  /*  183 */ "columnlist",
  /*  184 */ "conslist_opt",
  /*  185 */ "table_option_set",
  /*  186 */ "select",
  /*  187 */ "table_option",
  /*  188 */ "columnname",
  /*  189 */ "carglist",
  /*  190 */ "typetoken",
  /*  191 */ "typename",
  /*  192 */ "signed",
  /*  193 */ "plus_num",
  /*  194 */ "minus_num",
  /*  195 */ "ccons",
  /*  196 */ "term",
  /*  197 */ "expr",
  /*  198 */ "onconf",
  /*  199 */ "sortorder",
  /*  200 */ "autoinc",
  /*  201 */ "eidlist_opt",
  /*  202 */ "refargs",
  /*  203 */ "defer_subclause",
  /*  204 */ "generated",
  /*  205 */ "refarg",
  /*  206 */ "refact",
  /*  207 */ "init_deferred_pred_opt",
  /*  208 */ "conslist",
  /*  209 */ "tconscomma",
  /*  210 */ "tcons",
  /*  211 */ "sortlist",
  /*  212 */ "eidlist",
  /*  213 */ "defer_subclause_opt",
  /*  214 */ "orconf",
  /*  215 */ "resolvetype",
  /*  216 */ "raisetype",
  /*  217 */ "ifexists",
  /*  218 */ "selectnowith",
  /*  219 */ "oneselect",
  /*  220 */ "wqlist",
  /*  221 */ "orderby_opt",
  /*  222 */ "limit_opt",
  /*  223 */ "multiselect_op",
  /*  224 */ "distinct",
  /*  225 */ "selcollist",
  /*  226 */ "from",
  /*  227 */ "where_opt",
  /*  228 */ "groupby_opt",
  /*  229 */ "window_clause",
  /*  230 */ "values",
  /*  231 */ "nexprlist",
  /*  232 */ "sclp",
  /*  233 */ "as",
  /*  234 */ "seltablist",
  /*  235 */ "stl_prefix",
  /*  236 */ "joinop",
  /*  237 */ "indexed_opt",
  /*  238 */ "on_using",
  /*  239 */ "exprlist",
  /*  240 */ "xfullname",
  /*  241 */ "idlist",
  /*  242 */ "nulls",
  /*  243 */ "having_opt",
  /*  244 */ "with",
  /*  245 */ "where_opt_ret",
  /*  246 */ "setlist",
  /*  247 */ "insert_cmd",
  /*  248 */ "idlist_opt",
  /*  249 */ "upsert",
  /*  250 */ "returning",
  /*  251 */ "filter_over",
  /*  252 */ "likeop",
  /*  253 */ "between_op",
  /*  254 */ "in_op",
  /*  255 */ "paren_exprlist",
  /*  256 */ "case_operand",
  /*  257 */ "case_exprlist",
  /*  258 */ "case_else",
  /*  259 */ "uniqueflag",
  /*  260 */ "collate",
  /*  261 */ "vinto",
  /*  262 */ "nmnum",
  /*  263 */ "trigger_time",
  /*  264 */ "trigger_event",
  /*  265 */ "foreach_clause",
  /*  266 */ "when_clause",
  /*  267 */ "trigger_cmd_list",
  /*  268 */ "trigger_cmd",
  /*  269 */ "trnm",
  /*  270 */ "tridxby",
  /*  271 */ "database_kw_opt",
  /*  272 */ "key_opt",
  /*  273 */ "kwcolumn_opt",
  /*  274 */ "create_vtab",
  /*  275 */ "vtabarglist",
  /*  276 */ "vtabarg",
  /*  277 */ "vtabargtoken",
  /*  278 */ "lp",
  /*  279 */ "anylist",
  /*  280 */ "wqitem",
  /*  281 */ "wqas",
  /*  282 */ "windowdefn_list",
  /*  283 */ "windowdefn",
  /*  284 */ "window",
  /*  285 */ "frame_opt",
  /*  286 */ "filter_clause",
  /*  287 */ "over_clause",
  /*  288 */ "range_or_rows",
  /*  289 */ "frame_bound",
  /*  290 */ "frame_bound_s",
  /*  291 */ "frame_bound_e",
  /*  292 */ "frame_exclude_opt",
  /*  293 */ "frame_exclude",

];

/* For tracing reduce actions, the names of all rules are required.
*/
#[cfg(not(feature = "NDEBUG"))]
#[rustfmt::skip]
#[allow(non_upper_case_globals)]
static yyRuleName: [&str; YYNRULE] = [
 /*   0 */ "explain ::= EXPLAIN",
 /*   1 */ "explain ::= EXPLAIN QUERY PLAN",
 /*   2 */ "cmdx ::= cmd",
 /*   3 */ "cmd ::= BEGIN transtype trans_opt",
 /*   4 */ "trans_opt ::=",
 /*   5 */ "trans_opt ::= TRANSACTION",
 /*   6 */ "trans_opt ::= TRANSACTION nm",
 /*   7 */ "transtype ::=",
 /*   8 */ "transtype ::= DEFERRED",
 /*   9 */ "transtype ::= IMMEDIATE",
 /*  10 */ "transtype ::= EXCLUSIVE",
 /*  11 */ "transtype ::= READONLY",
 /*  12 */ "cmd ::= COMMIT|END trans_opt",
 /*  13 */ "cmd ::= ROLLBACK trans_opt",
 /*  14 */ "cmd ::= SAVEPOINT nm",
 /*  15 */ "cmd ::= RELEASE savepoint_opt nm",
 /*  16 */ "cmd ::= ROLLBACK trans_opt TO savepoint_opt nm",
 /*  17 */ "cmd ::= createkw temp TABLE ifnotexists fullname create_table_args",
 /*  18 */ "ifnotexists ::=",
 /*  19 */ "ifnotexists ::= IF NOT EXISTS",
 /*  20 */ "temp ::= TEMP",
 /*  21 */ "temp ::=",
 /*  22 */ "create_table_args ::= LP columnlist conslist_opt RP table_option_set",
 /*  23 */ "create_table_args ::= AS select",
 /*  24 */ "table_option_set ::=",
 /*  25 */ "table_option_set ::= table_option_set COMMA table_option",
 /*  26 */ "table_option ::= WITHOUT nm",
 /*  27 */ "table_option ::= nm nm",
 /*  28 */ "table_option ::= nm",
 /*  29 */ "columnlist ::= columnlist COMMA columnname carglist",
 /*  30 */ "columnlist ::= columnname carglist",
 /*  31 */ "columnname ::= nm typetoken",
 /*  32 */ "nm ::= ID|INDEXED|JOIN_KW",
 /*  33 */ "nm ::= STRING",
 /*  34 */ "typetoken ::=",
 /*  35 */ "typetoken ::= typename",
 /*  36 */ "typetoken ::= typename LP signed RP",
 /*  37 */ "typetoken ::= typename LP signed COMMA signed RP",
 /*  38 */ "typename ::= ID|STRING",
 /*  39 */ "typename ::= typename ID|STRING",
 /*  40 */ "carglist ::= carglist ccons",
 /*  41 */ "carglist ::=",
 /*  42 */ "ccons ::= CONSTRAINT nm",
 /*  43 */ "ccons ::= DEFAULT term",
 /*  44 */ "ccons ::= DEFAULT LP expr RP",
 /*  45 */ "ccons ::= DEFAULT PLUS term",
 /*  46 */ "ccons ::= DEFAULT MINUS term",
 /*  47 */ "ccons ::= DEFAULT ID|INDEXED",
 /*  48 */ "ccons ::= NULL onconf",
 /*  49 */ "ccons ::= NOT NULL onconf",
 /*  50 */ "ccons ::= PRIMARY KEY sortorder onconf autoinc",
 /*  51 */ "ccons ::= UNIQUE onconf",
 /*  52 */ "ccons ::= CHECK LP expr RP",
 /*  53 */ "ccons ::= REFERENCES nm eidlist_opt refargs",
 /*  54 */ "ccons ::= defer_subclause",
 /*  55 */ "ccons ::= COLLATE ID|STRING",
 /*  56 */ "ccons ::= GENERATED ALWAYS AS generated",
 /*  57 */ "ccons ::= AS generated",
 /*  58 */ "generated ::= LP expr RP",
 /*  59 */ "generated ::= LP expr RP ID",
 /*  60 */ "autoinc ::=",
 /*  61 */ "autoinc ::= AUTOINCR",
 /*  62 */ "refargs ::=",
 /*  63 */ "refargs ::= refargs refarg",
 /*  64 */ "refarg ::= MATCH nm",
 /*  65 */ "refarg ::= ON INSERT refact",
 /*  66 */ "refarg ::= ON DELETE refact",
 /*  67 */ "refarg ::= ON UPDATE refact",
 /*  68 */ "refact ::= SET NULL",
 /*  69 */ "refact ::= SET DEFAULT",
 /*  70 */ "refact ::= CASCADE",
 /*  71 */ "refact ::= RESTRICT",
 /*  72 */ "refact ::= NO ACTION",
 /*  73 */ "defer_subclause ::= NOT DEFERRABLE init_deferred_pred_opt",
 /*  74 */ "defer_subclause ::= DEFERRABLE init_deferred_pred_opt",
 /*  75 */ "init_deferred_pred_opt ::=",
 /*  76 */ "init_deferred_pred_opt ::= INITIALLY DEFERRED",
 /*  77 */ "init_deferred_pred_opt ::= INITIALLY IMMEDIATE",
 /*  78 */ "conslist_opt ::=",
 /*  79 */ "conslist_opt ::= COMMA conslist",
 /*  80 */ "conslist ::= conslist tconscomma tcons",
 /*  81 */ "conslist ::= tcons",
 /*  82 */ "tconscomma ::= COMMA",
 /*  83 */ "tcons ::= CONSTRAINT nm",
 /*  84 */ "tcons ::= PRIMARY KEY LP sortlist autoinc RP onconf",
 /*  85 */ "tcons ::= UNIQUE LP sortlist RP onconf",
 /*  86 */ "tcons ::= CHECK LP expr RP onconf",
 /*  87 */ "tcons ::= FOREIGN KEY LP eidlist RP REFERENCES nm eidlist_opt refargs defer_subclause_opt",
 /*  88 */ "defer_subclause_opt ::=",
 /*  89 */ "defer_subclause_opt ::= defer_subclause",
 /*  90 */ "onconf ::=",
 /*  91 */ "onconf ::= ON CONFLICT resolvetype",
 /*  92 */ "orconf ::=",
 /*  93 */ "orconf ::= OR resolvetype",
 /*  94 */ "resolvetype ::= IGNORE",
 /*  95 */ "resolvetype ::= REPLACE",
 /*  96 */ "cmd ::= DROP TABLE ifexists fullname",
 /*  97 */ "ifexists ::= IF EXISTS",
 /*  98 */ "ifexists ::=",
 /*  99 */ "cmd ::= createkw temp VIEW ifnotexists fullname eidlist_opt AS select",
 /* 100 */ "cmd ::= DROP VIEW ifexists fullname",
 /* 101 */ "cmd ::= select",
 /* 102 */ "select ::= WITH wqlist selectnowith orderby_opt limit_opt",
 /* 103 */ "select ::= WITH RECURSIVE wqlist selectnowith orderby_opt limit_opt",
 /* 104 */ "select ::= selectnowith orderby_opt limit_opt",
 /* 105 */ "selectnowith ::= oneselect",
 /* 106 */ "selectnowith ::= selectnowith multiselect_op oneselect",
 /* 107 */ "multiselect_op ::= UNION",
 /* 108 */ "multiselect_op ::= UNION ALL",
 /* 109 */ "multiselect_op ::= EXCEPT",
 /* 110 */ "multiselect_op ::= INTERSECT",
 /* 111 */ "oneselect ::= SELECT distinct selcollist from where_opt groupby_opt",
 /* 112 */ "oneselect ::= SELECT distinct selcollist from where_opt groupby_opt window_clause",
 /* 113 */ "oneselect ::= values",
 /* 114 */ "values ::= VALUES LP nexprlist RP",
 /* 115 */ "values ::= values COMMA LP nexprlist RP",
 /* 116 */ "distinct ::= DISTINCT",
 /* 117 */ "distinct ::= ALL",
 /* 118 */ "distinct ::=",
 /* 119 */ "sclp ::=",
 /* 120 */ "selcollist ::= sclp expr as",
 /* 121 */ "selcollist ::= sclp STAR",
 /* 122 */ "selcollist ::= sclp nm DOT STAR",
 /* 123 */ "as ::= AS nm",
 /* 124 */ "as ::= ID|STRING",
 /* 125 */ "as ::=",
 /* 126 */ "from ::=",
 /* 127 */ "from ::= FROM seltablist",
 /* 128 */ "stl_prefix ::= seltablist joinop",
 /* 129 */ "stl_prefix ::=",
 /* 130 */ "seltablist ::= stl_prefix fullname as indexed_opt on_using",
 /* 131 */ "seltablist ::= stl_prefix fullname LP exprlist RP as on_using",
 /* 132 */ "seltablist ::= stl_prefix LP select RP as on_using",
 /* 133 */ "seltablist ::= stl_prefix LP seltablist RP as on_using",
 /* 134 */ "fullname ::= nm",
 /* 135 */ "fullname ::= nm DOT nm",
 /* 136 */ "xfullname ::= nm",
 /* 137 */ "xfullname ::= nm DOT nm",
 /* 138 */ "xfullname ::= nm DOT nm AS nm",
 /* 139 */ "xfullname ::= nm AS nm",
 /* 140 */ "joinop ::= COMMA",
 /* 141 */ "joinop ::= JOIN",
 /* 142 */ "joinop ::= JOIN_KW JOIN",
 /* 143 */ "joinop ::= JOIN_KW nm JOIN",
 /* 144 */ "joinop ::= JOIN_KW nm nm JOIN",
 /* 145 */ "on_using ::= ON expr",
 /* 146 */ "on_using ::= USING LP idlist RP",
 /* 147 */ "on_using ::=",
 /* 148 */ "indexed_opt ::=",
 /* 149 */ "indexed_opt ::= INDEXED BY nm",
 /* 150 */ "indexed_opt ::= NOT INDEXED",
 /* 151 */ "orderby_opt ::=",
 /* 152 */ "orderby_opt ::= ORDER BY sortlist",
 /* 153 */ "sortlist ::= sortlist COMMA expr sortorder nulls",
 /* 154 */ "sortlist ::= expr sortorder nulls",
 /* 155 */ "sortorder ::= ASC",
 /* 156 */ "sortorder ::= DESC",
 /* 157 */ "sortorder ::=",
 /* 158 */ "nulls ::= NULLS FIRST",
 /* 159 */ "nulls ::= NULLS LAST",
 /* 160 */ "nulls ::=",
 /* 161 */ "groupby_opt ::=",
 /* 162 */ "groupby_opt ::= GROUP BY nexprlist having_opt",
 /* 163 */ "having_opt ::=",
 /* 164 */ "having_opt ::= HAVING expr",
 /* 165 */ "limit_opt ::=",
 /* 166 */ "limit_opt ::= LIMIT expr",
 /* 167 */ "limit_opt ::= LIMIT expr OFFSET expr",
 /* 168 */ "limit_opt ::= LIMIT expr COMMA expr",
 /* 169 */ "cmd ::= with DELETE FROM xfullname indexed_opt where_opt_ret orderby_opt limit_opt",
 /* 170 */ "where_opt ::=",
 /* 171 */ "where_opt ::= WHERE expr",
 /* 172 */ "where_opt_ret ::=",
 /* 173 */ "where_opt_ret ::= WHERE expr",
 /* 174 */ "where_opt_ret ::= RETURNING selcollist",
 /* 175 */ "where_opt_ret ::= WHERE expr RETURNING selcollist",
 /* 176 */ "cmd ::= with UPDATE orconf xfullname indexed_opt SET setlist from where_opt_ret orderby_opt limit_opt",
 /* 177 */ "setlist ::= setlist COMMA nm EQ expr",
 /* 178 */ "setlist ::= setlist COMMA LP idlist RP EQ expr",
 /* 179 */ "setlist ::= nm EQ expr",
 /* 180 */ "setlist ::= LP idlist RP EQ expr",
 /* 181 */ "cmd ::= with insert_cmd INTO xfullname idlist_opt select upsert",
 /* 182 */ "cmd ::= with insert_cmd INTO xfullname idlist_opt DEFAULT VALUES returning",
 /* 183 */ "upsert ::=",
 /* 184 */ "upsert ::= RETURNING selcollist",
 /* 185 */ "upsert ::= ON CONFLICT LP sortlist RP where_opt DO UPDATE SET setlist where_opt upsert",
 /* 186 */ "upsert ::= ON CONFLICT LP sortlist RP where_opt DO NOTHING upsert",
 /* 187 */ "upsert ::= ON CONFLICT DO NOTHING returning",
 /* 188 */ "upsert ::= ON CONFLICT DO UPDATE SET setlist where_opt returning",
 /* 189 */ "returning ::= RETURNING selcollist",
 /* 190 */ "returning ::=",
 /* 191 */ "insert_cmd ::= INSERT orconf",
 /* 192 */ "insert_cmd ::= REPLACE",
 /* 193 */ "idlist_opt ::=",
 /* 194 */ "idlist_opt ::= LP idlist RP",
 /* 195 */ "idlist ::= idlist COMMA nm",
 /* 196 */ "idlist ::= nm",
 /* 197 */ "expr ::= LP expr RP",
 /* 198 */ "expr ::= ID|INDEXED|JOIN_KW",
 /* 199 */ "expr ::= nm DOT nm",
 /* 200 */ "expr ::= nm DOT nm DOT nm",
 /* 201 */ "term ::= NULL",
 /* 202 */ "term ::= BLOB",
 /* 203 */ "term ::= STRING",
 /* 204 */ "term ::= FLOAT|INTEGER",
 /* 205 */ "expr ::= VARIABLE",
 /* 206 */ "expr ::= expr COLLATE ID|STRING",
 /* 207 */ "expr ::= CAST LP expr AS typetoken RP",
 /* 208 */ "expr ::= ID|INDEXED|JOIN_KW LP distinct exprlist RP",
 /* 209 */ "expr ::= ID|INDEXED|JOIN_KW LP STAR RP",
 /* 210 */ "expr ::= ID|INDEXED|JOIN_KW LP distinct exprlist RP filter_over",
 /* 211 */ "expr ::= ID|INDEXED|JOIN_KW LP STAR RP filter_over",
 /* 212 */ "term ::= CTIME_KW",
 /* 213 */ "expr ::= LP nexprlist COMMA expr RP",
 /* 214 */ "expr ::= expr AND expr",
 /* 215 */ "expr ::= expr OR expr",
 /* 216 */ "expr ::= expr LT|GT|GE|LE expr",
 /* 217 */ "expr ::= expr EQ|NE expr",
 /* 218 */ "expr ::= expr BITAND|BITOR|LSHIFT|RSHIFT expr",
 /* 219 */ "expr ::= expr PLUS|MINUS expr",
 /* 220 */ "expr ::= expr STAR|SLASH|REM expr",
 /* 221 */ "expr ::= expr CONCAT expr",
 /* 222 */ "likeop ::= LIKE_KW|MATCH",
 /* 223 */ "likeop ::= NOT LIKE_KW|MATCH",
 /* 224 */ "expr ::= expr likeop expr",
 /* 225 */ "expr ::= expr likeop expr ESCAPE expr",
 /* 226 */ "expr ::= expr ISNULL|NOTNULL",
 /* 227 */ "expr ::= expr NOT NULL",
 /* 228 */ "expr ::= expr IS expr",
 /* 229 */ "expr ::= expr IS NOT expr",
 /* 230 */ "expr ::= expr IS NOT DISTINCT FROM expr",
 /* 231 */ "expr ::= expr IS DISTINCT FROM expr",
 /* 232 */ "expr ::= NOT expr",
 /* 233 */ "expr ::= BITNOT expr",
 /* 234 */ "expr ::= PLUS|MINUS expr",
 /* 235 */ "expr ::= expr PTR expr",
 /* 236 */ "between_op ::= BETWEEN",
 /* 237 */ "between_op ::= NOT BETWEEN",
 /* 238 */ "expr ::= expr between_op expr AND expr",
 /* 239 */ "in_op ::= IN",
 /* 240 */ "in_op ::= NOT IN",
 /* 241 */ "expr ::= expr in_op LP exprlist RP",
 /* 242 */ "expr ::= LP select RP",
 /* 243 */ "expr ::= expr in_op LP select RP",
 /* 244 */ "expr ::= expr in_op fullname paren_exprlist",
 /* 245 */ "expr ::= EXISTS LP select RP",
 /* 246 */ "expr ::= CASE case_operand case_exprlist case_else END",
 /* 247 */ "case_exprlist ::= case_exprlist WHEN expr THEN expr",
 /* 248 */ "case_exprlist ::= WHEN expr THEN expr",
 /* 249 */ "case_else ::= ELSE expr",
 /* 250 */ "case_else ::=",
 /* 251 */ "case_operand ::= expr",
 /* 252 */ "case_operand ::=",
 /* 253 */ "exprlist ::= nexprlist",
 /* 254 */ "exprlist ::=",
 /* 255 */ "nexprlist ::= nexprlist COMMA expr",
 /* 256 */ "nexprlist ::= expr",
 /* 257 */ "paren_exprlist ::=",
 /* 258 */ "paren_exprlist ::= LP exprlist RP",
 /* 259 */ "cmd ::= createkw uniqueflag INDEX ifnotexists fullname ON nm LP sortlist RP where_opt",
 /* 260 */ "uniqueflag ::= UNIQUE",
 /* 261 */ "uniqueflag ::=",
 /* 262 */ "eidlist_opt ::=",
 /* 263 */ "eidlist_opt ::= LP eidlist RP",
 /* 264 */ "eidlist ::= eidlist COMMA nm collate sortorder",
 /* 265 */ "eidlist ::= nm collate sortorder",
 /* 266 */ "collate ::=",
 /* 267 */ "collate ::= COLLATE ID|STRING",
 /* 268 */ "cmd ::= DROP INDEX ifexists fullname",
 /* 269 */ "cmd ::= VACUUM vinto",
 /* 270 */ "cmd ::= VACUUM nm vinto",
 /* 271 */ "vinto ::= INTO expr",
 /* 272 */ "vinto ::=",
 /* 273 */ "cmd ::= PRAGMA fullname",
 /* 274 */ "cmd ::= PRAGMA fullname EQ nmnum",
 /* 275 */ "cmd ::= PRAGMA fullname LP nmnum RP",
 /* 276 */ "cmd ::= PRAGMA fullname EQ minus_num",
 /* 277 */ "cmd ::= PRAGMA fullname LP minus_num RP",
 /* 278 */ "nmnum ::= nm",
 /* 279 */ "nmnum ::= ON",
 /* 280 */ "nmnum ::= DELETE",
 /* 281 */ "nmnum ::= DEFAULT",
 /* 282 */ "plus_num ::= PLUS INTEGER|FLOAT",
 /* 283 */ "plus_num ::= INTEGER|FLOAT",
 /* 284 */ "minus_num ::= MINUS INTEGER|FLOAT",
 /* 285 */ "cmd ::= createkw temp TRIGGER ifnotexists fullname trigger_time trigger_event ON fullname foreach_clause when_clause BEGIN trigger_cmd_list END",
 /* 286 */ "trigger_time ::= BEFORE",
 /* 287 */ "trigger_time ::= AFTER",
 /* 288 */ "trigger_time ::= INSTEAD OF",
 /* 289 */ "trigger_time ::=",
 /* 290 */ "trigger_event ::= DELETE",
 /* 291 */ "trigger_event ::= INSERT",
 /* 292 */ "trigger_event ::= UPDATE",
 /* 293 */ "trigger_event ::= UPDATE OF idlist",
 /* 294 */ "foreach_clause ::=",
 /* 295 */ "foreach_clause ::= FOR EACH ROW",
 /* 296 */ "when_clause ::=",
 /* 297 */ "when_clause ::= WHEN expr",
 /* 298 */ "trigger_cmd_list ::= trigger_cmd_list trigger_cmd SEMI",
 /* 299 */ "trigger_cmd_list ::= trigger_cmd SEMI",
 /* 300 */ "trnm ::= nm DOT nm",
 /* 301 */ "tridxby ::= INDEXED BY nm",
 /* 302 */ "tridxby ::= NOT INDEXED",
 /* 303 */ "trigger_cmd ::= UPDATE orconf trnm tridxby SET setlist from where_opt",
 /* 304 */ "trigger_cmd ::= insert_cmd INTO trnm idlist_opt select upsert",
 /* 305 */ "trigger_cmd ::= DELETE FROM trnm tridxby where_opt",
 /* 306 */ "trigger_cmd ::= select",
 /* 307 */ "expr ::= RAISE LP IGNORE RP",
 /* 308 */ "expr ::= RAISE LP raisetype COMMA nm RP",
 /* 309 */ "raisetype ::= ROLLBACK",
 /* 310 */ "raisetype ::= ABORT",
 /* 311 */ "raisetype ::= FAIL",
 /* 312 */ "cmd ::= DROP TRIGGER ifexists fullname",
 /* 313 */ "cmd ::= ATTACH database_kw_opt expr AS expr key_opt",
 /* 314 */ "cmd ::= DETACH database_kw_opt expr",
 /* 315 */ "key_opt ::=",
 /* 316 */ "key_opt ::= KEY expr",
 /* 317 */ "cmd ::= REINDEX",
 /* 318 */ "cmd ::= REINDEX fullname",
 /* 319 */ "cmd ::= ANALYZE",
 /* 320 */ "cmd ::= ANALYZE fullname",
 /* 321 */ "cmd ::= ALTER TABLE fullname RENAME TO nm",
 /* 322 */ "cmd ::= ALTER TABLE fullname ADD kwcolumn_opt columnname carglist",
 /* 323 */ "cmd ::= ALTER TABLE fullname RENAME kwcolumn_opt nm TO nm",
 /* 324 */ "cmd ::= ALTER TABLE fullname DROP kwcolumn_opt nm",
 /* 325 */ "cmd ::= ALTER TABLE fullname ALTER COLUMNKW columnname TO columnname carglist",
 /* 326 */ "cmd ::= create_vtab",
 /* 327 */ "cmd ::= create_vtab LP vtabarglist RP",
 /* 328 */ "create_vtab ::= createkw VIRTUAL TABLE ifnotexists fullname USING nm",
 /* 329 */ "vtabarg ::=",
 /* 330 */ "vtabargtoken ::= ANY",
 /* 331 */ "vtabargtoken ::= lp anylist RP",
 /* 332 */ "lp ::= LP",
 /* 333 */ "with ::=",
 /* 334 */ "with ::= WITH wqlist",
 /* 335 */ "with ::= WITH RECURSIVE wqlist",
 /* 336 */ "wqas ::= AS",
 /* 337 */ "wqas ::= AS MATERIALIZED",
 /* 338 */ "wqas ::= AS NOT MATERIALIZED",
 /* 339 */ "wqitem ::= nm eidlist_opt wqas LP select RP",
 /* 340 */ "wqlist ::= wqitem",
 /* 341 */ "wqlist ::= wqlist COMMA wqitem",
 /* 342 */ "windowdefn_list ::= windowdefn",
 /* 343 */ "windowdefn_list ::= windowdefn_list COMMA windowdefn",
 /* 344 */ "windowdefn ::= nm AS LP window RP",
 /* 345 */ "window ::= PARTITION BY nexprlist orderby_opt frame_opt",
 /* 346 */ "window ::= nm PARTITION BY nexprlist orderby_opt frame_opt",
 /* 347 */ "window ::= ORDER BY sortlist frame_opt",
 /* 348 */ "window ::= nm ORDER BY sortlist frame_opt",
 /* 349 */ "window ::= frame_opt",
 /* 350 */ "window ::= nm frame_opt",
 /* 351 */ "frame_opt ::=",
 /* 352 */ "frame_opt ::= range_or_rows frame_bound_s frame_exclude_opt",
 /* 353 */ "frame_opt ::= range_or_rows BETWEEN frame_bound_s AND frame_bound_e frame_exclude_opt",
 /* 354 */ "range_or_rows ::= RANGE",
 /* 355 */ "range_or_rows ::= ROWS",
 /* 356 */ "range_or_rows ::= GROUPS",
 /* 357 */ "frame_bound_s ::= frame_bound",
 /* 358 */ "frame_bound_s ::= UNBOUNDED PRECEDING",
 /* 359 */ "frame_bound_e ::= frame_bound",
 /* 360 */ "frame_bound_e ::= UNBOUNDED FOLLOWING",
 /* 361 */ "frame_bound ::= expr PRECEDING",
 /* 362 */ "frame_bound ::= CURRENT ROW",
 /* 363 */ "frame_bound ::= expr FOLLOWING",
 /* 364 */ "frame_exclude_opt ::=",
 /* 365 */ "frame_exclude_opt ::= EXCLUDE frame_exclude",
 /* 366 */ "frame_exclude ::= NO OTHERS",
 /* 367 */ "frame_exclude ::= CURRENT ROW",
 /* 368 */ "frame_exclude ::= GROUP",
 /* 369 */ "frame_exclude ::= TIES",
 /* 370 */ "window_clause ::= WINDOW windowdefn_list",
 /* 371 */ "filter_over ::= filter_clause over_clause",
 /* 372 */ "filter_over ::= over_clause",
 /* 373 */ "filter_over ::= filter_clause",
 /* 374 */ "over_clause ::= OVER LP window RP",
 /* 375 */ "over_clause ::= OVER nm",
 /* 376 */ "filter_clause ::= FILTER LP WHERE expr RP",
 /* 377 */ "input ::= cmdlist",
 /* 378 */ "cmdlist ::= cmdlist ecmd",
 /* 379 */ "cmdlist ::= ecmd",
 /* 380 */ "ecmd ::= SEMI",
 /* 381 */ "ecmd ::= cmdx SEMI",
 /* 382 */ "ecmd ::= explain cmdx SEMI",
 /* 383 */ "savepoint_opt ::= SAVEPOINT",
 /* 384 */ "savepoint_opt ::=",
 /* 385 */ "createkw ::= CREATE",
 /* 386 */ "table_option_set ::= table_option",
 /* 387 */ "signed ::= plus_num",
 /* 388 */ "signed ::= minus_num",
 /* 389 */ "tconscomma ::=",
 /* 390 */ "resolvetype ::= raisetype",
 /* 391 */ "sclp ::= selcollist COMMA",
 /* 392 */ "expr ::= term",
 /* 393 */ "nmnum ::= plus_num",
 /* 394 */ "trnm ::= nm",
 /* 395 */ "tridxby ::=",
 /* 396 */ "database_kw_opt ::= DATABASE",
 /* 397 */ "database_kw_opt ::=",
 /* 398 */ "kwcolumn_opt ::=",
 /* 399 */ "kwcolumn_opt ::= COLUMNKW",
 /* 400 */ "vtabarglist ::= vtabarg",
 /* 401 */ "vtabarglist ::= vtabarglist COMMA vtabarg",
 /* 402 */ "vtabarg ::= vtabarg vtabargtoken",
 /* 403 */ "anylist ::=",
 /* 404 */ "anylist ::= anylist LP anylist RP",
 /* 405 */ "anylist ::= anylist ANY",
];

/*
** Try to increase the size of the parser stack.  Return the number
** of errors.  Return 0 on success.
*/
impl yyParser<'_> {
    fn yy_grow_stack_if_needed(&mut self) -> bool {
        if self.yyidx >= self.yystack.capacity() {
            if self.yyGrowStack() {
                self.yyidx = self.yyidx.checked_sub(1).unwrap();
                self.yyStackOverflow();
                return true;
            }
        }
        false
    }
    fn yy_grow_stack_for_push(&mut self) -> bool {
        if self.yyidx >= self.yystack.capacity() - 1 {
            if self.yyGrowStack() {
                self.yyStackOverflow();
                return true;
            }
        }
        // yystack is not prefilled with zero value like in C.
        if self.yyidx == self.yystack.len() {
            self.yystack.push(yyStackEntry::default());
        } else if self.yyidx + 1 == self.yystack.len() {
            self.yystack.push(yyStackEntry::default());
        }
        false
    }

    #[allow(non_snake_case)]
    #[cfg(feature = "YYSTACKDYNAMIC")]
    fn yyGrowStack(&mut self) -> bool {
        let capacity = self.yystack.capacity();
        let additional = capacity + 100;
        self.yystack.reserve(additional);
        #[cfg(not(feature = "NDEBUG"))]
        {
            trace!(
                target: TARGET,
                "Stack grows from {} to {} entries.",
                capacity,
                self.yystack.capacity()
            );
        }
        false
    }
    #[allow(non_snake_case)]
    #[cfg(not(feature = "YYSTACKDYNAMIC"))]
    fn yyGrowStack(&mut self) -> bool {
        true
    }
}

/* Initialize a new parser.
*/
impl yyParser<'_> {
    pub fn new(
        ctx: Context,
    ) -> yyParser {
        let mut p = yyParser {
            yyidx: 0,
            #[cfg(feature = "YYTRACKMAXSTACKDEPTH")]
            yyhwm: 0,
            yystack: Vec::with_capacity(YYSTACKDEPTH),
            //#[cfg(not(feature = "YYNOERRORRECOVERY"))]
            yyerrcnt: -1,
        ctx,
        };
        p.push(yyStackEntry::default());
        p
    }
}

/*
** Pop the parser's stack once.
*/
impl yyParser<'_> {
    fn yy_pop_parser_stack(&mut self) {
        use std::mem::take;
        let yytos = take(&mut self.yystack[self.yyidx]);
        self.yyidx = self.yyidx.checked_sub(1).unwrap();
        //assert_eq!(self.yyidx+1, self.yystack.len());
        #[cfg(not(feature = "NDEBUG"))]
        {
            trace!(
                target: TARGET,
                "Popping {}", yyTokenName[yytos.major as usize]
            );
        }
    }
}

/*
** Clear all secondary memory allocations from the parser
*/
impl yyParser<'_> {
    #[allow(non_snake_case)]
    pub fn sqlite3ParserFinalize(&mut self) {
        while self.yyidx > 0 {
            self.yy_pop_parser_stack();
        }
        // TODO check all elements remaining in yystack are yyinit()
    }
}

/*
** Return the peak depth of the stack for a parser.
*/
#[cfg(feature = "YYTRACKMAXSTACKDEPTH")]
impl yyParser<'_> {
    #[allow(non_snake_case)]
    pub fn sqlite3ParserStackPeak(&self) -> usize {
        self.yyhwm
    }
    fn yyhwm_incr(&mut self) {
        if self.yyidx > self.yyhwm {
            self.yyhwm += 1;
            assert_eq!(self.yyhwm, self.yyidx);
        }
    }
}
#[cfg(not(feature = "YYTRACKMAXSTACKDEPTH"))]
impl yyParser<'_> {
    #[inline]
    fn yyhwm_incr(&mut self) {}
}

/* This array of booleans keeps track of the parser statement
** coverage.  The element yycoverage[X][Y] is set when the parser
** is in state X and has a lookahead token Y.  In a well-tested
** systems, every element of this matrix should end up being set.
*/
#[cfg(feature = "YYCOVERAGE")]
static yycoverage: [[bool; YYNTOKEN]; YYNSTATE] = [];

/*
** Write into out a description of every state/lookahead combination that
**
**   (1)  has not been used by the parser, and
**   (2)  is not a syntax error.
**
** Return the number of missed state/lookahead combinations.
*/
#[cfg(feature = "YYCOVERAGE")]
fn sqlite3ParserCoverage(/*FILE *out*/) -> i32 {
    //int stateno, iLookAhead, i;
    let mut nMissed = 0;
    /*for(stateno=0; stateno<YYNSTATE; stateno++){
      i = yy_shift_ofst[stateno];
      for(iLookAhead=0; iLookAhead<YYNTOKEN; iLookAhead++){
        if( yy_lookahead[i+iLookAhead]!=iLookAhead ) continue;
        if( yycoverage[stateno][iLookAhead]==0 ) nMissed++;
        if( out ){
          fprintf(out,"State %d lookahead %s %s\n", stateno,
                  yyTokenName[iLookAhead],
                  yycoverage[stateno][iLookAhead] ? "ok" : "missed");
        }
      }
    }*/
    return nMissed;
}

/*
** Find the appropriate action for a parser given the terminal
** look-ahead token iLookAhead.
*/
#[allow(non_snake_case)]
fn yy_find_shift_action(
    mut iLookAhead: YYCODETYPE, /* The look-ahead token */
    stateno: YYACTIONTYPE,      /* Current state number */
) -> YYACTIONTYPE {
    if stateno > YY_MAX_SHIFT {
        return stateno;
    }
    assert!(stateno <= YY_SHIFT_COUNT);
    #[cfg(feature = "YYCOVERAGE")]
    {
        //yycoverage[stateno][iLookAhead] = true;
    }
    loop {
        let mut i = yy_shift_ofst[stateno as usize] as usize;
        assert!(i <= YY_ACTTAB_COUNT!());
        assert!(i + usize::from(YYNTOKEN) <= yy_lookahead.len());
        assert_ne!(iLookAhead, YYNOCODE);
        assert!((iLookAhead as YYACTIONTYPE) < YYNTOKEN);
        i += iLookAhead as usize;
        if yy_lookahead[i] != iLookAhead {
            if YYFALLBACK {
                let iFallback = yyFallback[iLookAhead as usize]; /* Fallback token */
                if iFallback != 0 {
                    #[cfg(not(feature = "NDEBUG"))]
                    {
                        trace!(
                            target: TARGET,
                            "FALLBACK {} => {}",
                            yyTokenName[iLookAhead as usize],
                            yyTokenName[iFallback as usize]
                        );
                    }
                    assert_eq!(yyFallback[iFallback as usize], 0); /* Fallback loop must terminate */
                    iLookAhead = iFallback;
                    continue;
                }
            }
            if YYWILDCARD > 0 {
                let j = i - iLookAhead as usize + YYWILDCARD as usize;
                if yy_lookahead[j] == YYWILDCARD && iLookAhead > 0 {
                    #[cfg(not(feature = "NDEBUG"))]
                    {
                        trace!(
                            target: TARGET,
                            "WILDCARD {} => {}",
                            yyTokenName[iLookAhead as usize],
                            yyTokenName[YYWILDCARD as usize]
                        );
                    }
                    return yy_action[j];
                }
            } /* YYWILDCARD */
            return yy_default[stateno as usize];
        } else {
            return yy_action[i];
        }
    }
}

/*
** Find the appropriate action for a parser given the non-terminal
** look-ahead token iLookAhead.
*/
#[allow(non_snake_case)]
fn yy_find_reduce_action(
    stateno: YYACTIONTYPE,  /* Current state number */
    iLookAhead: YYCODETYPE, /* The look-ahead token */
) -> YYACTIONTYPE {
    if YYERRORSYMBOL > 0 {
        if stateno > YY_REDUCE_COUNT {
            return yy_default[stateno as usize];
        }
    } else {
        assert!(stateno <= YY_REDUCE_COUNT);
    }
    let mut i: i32 = yy_reduce_ofst[stateno as usize].into();
    assert_ne!(iLookAhead, YYNOCODE);
    i += i32::from(iLookAhead);
    if YYERRORSYMBOL > 0 {
        if !(0..YY_ACTTAB_COUNT!()).contains(&i) || yy_lookahead[i as usize] != iLookAhead {
            return yy_default[stateno as usize];
        }
    } else {
        assert!((0..YY_ACTTAB_COUNT!()).contains(&i));
        assert_eq!(yy_lookahead[i as usize], iLookAhead);
    }
    yy_action[i as usize]
}

/*
** The following routine is called if the stack overflows.
*/
impl yyParser<'_> {
    #[allow(non_snake_case)]
    fn yyStackOverflow(&mut self) {
        #[cfg(not(feature = "NDEBUG"))]
        {
            error!(target: TARGET, "Stack Overflow!");
        }
        while self.yyidx > 0 {
            self.yy_pop_parser_stack();
        }
        /* Here code is inserted which will execute if the parser
         ** stack every overflows */
        /******** Begin %stack_overflow code ******************************************/
//line 51 "src/parser/parse.y"

  error!(target: TARGET, "parser stack overflow");
  self.ctx.error = Some(ParserError::StackOverflow);
        /******** End %stack_overflow code ********************************************/
    }
}

/*
** Print tracing information for a SHIFT action
*/
impl yyParser<'_> {
    #[allow(non_snake_case)]
    fn yyTraceShift(&self, yyNewState: YYACTIONTYPE, zTag: &str) {
        #[cfg(not(feature = "NDEBUG"))]
        {
            let yytos = &self[0];
            if yyNewState < YYNSTATE {
                trace!(
                    target: TARGET,
                    "{} '{}', go to state {}", zTag, yyTokenName[yytos.major as usize], yyNewState
                );
            } else {
                trace!(
                    target: TARGET,
                    "{} '{}', pending reduce {:?}",
                    zTag,
                    yyTokenName[yytos.major as usize],
                    yyNewState.checked_sub(YY_MIN_REDUCE)
                );
            }
        }
    }
}

/*
** Perform a shift action.
*/
impl yyParser<'_> {
    #[allow(non_snake_case)]
    fn yy_shift(
        &mut self,
        mut yyNewState: YYACTIONTYPE,    /* The new state to shift in */
        yyMajor: YYCODETYPE,             /* The major token to shift in */
        yyMinor: sqlite3ParserTOKENTYPE, /* The minor token to shift in */
    ) {
        self.yyidx_shift(1);
        self.yyhwm_incr();
        if self.yy_grow_stack_if_needed() {
            return;
        }
        if yyNewState > YY_MAX_SHIFT {
            yyNewState += YY_MIN_REDUCE - YY_MIN_SHIFTREDUCE;
        }
        let yytos = yyStackEntry {
            stateno: yyNewState,
            major: yyMajor,
            minor: YYMINORTYPE::yy0(yyMinor),
        };
        self.push(yytos);
        self.yyTraceShift(yyNewState, "Shift");
    }
}

/* For rule J, yyRuleInfoLhs[J] contains the symbol on the left-hand side
** of that rule */
#[allow(non_upper_case_globals)]
static yyRuleInfoLhs: [YYCODETYPE; YYNRULE] = [
     172, /* (0)explain ::= EXPLAIN */
     172, /* (1)explain ::= EXPLAIN QUERY PLAN */
     171, /* (2)cmdx ::= cmd */
     173, /* (3)cmd ::= BEGIN transtype trans_opt */
     175, /* (4)trans_opt ::= */
     175, /* (5)trans_opt ::= TRANSACTION */
     175, /* (6)trans_opt ::= TRANSACTION nm */
     174, /* (7)transtype ::= */
     174, /* (8)transtype ::= DEFERRED */
     174, /* (9)transtype ::= IMMEDIATE */
     174, /* (10)transtype ::= EXCLUSIVE */
     174, /* (11)transtype ::= READONLY */
     173, /* (12)cmd ::= COMMIT|END trans_opt */
     173, /* (13)cmd ::= ROLLBACK trans_opt */
     173, /* (14)cmd ::= SAVEPOINT nm */
     173, /* (15)cmd ::= RELEASE savepoint_opt nm */
     173, /* (16)cmd ::= ROLLBACK trans_opt TO savepoint_opt nm */
     173, /* (17)cmd ::= createkw temp TABLE ifnotexists fullname create_table_args */
     180, /* (18)ifnotexists ::= */
     180, /* (19)ifnotexists ::= IF NOT EXISTS */
     179, /* (20)temp ::= TEMP */
     179, /* (21)temp ::= */
     182, /* (22)create_table_args ::= LP columnlist conslist_opt RP table_option_set */
     182, /* (23)create_table_args ::= AS select */
     185, /* (24)table_option_set ::= */
     185, /* (25)table_option_set ::= table_option_set COMMA table_option */
     187, /* (26)table_option ::= WITHOUT nm */
     187, /* (27)table_option ::= nm nm */
     187, /* (28)table_option ::= nm */
     183, /* (29)columnlist ::= columnlist COMMA columnname carglist */
     183, /* (30)columnlist ::= columnname carglist */
     188, /* (31)columnname ::= nm typetoken */
     176, /* (32)nm ::= ID|INDEXED|JOIN_KW */
     176, /* (33)nm ::= STRING */
     190, /* (34)typetoken ::= */
     190, /* (35)typetoken ::= typename */
     190, /* (36)typetoken ::= typename LP signed RP */
     190, /* (37)typetoken ::= typename LP signed COMMA signed RP */
     191, /* (38)typename ::= ID|STRING */
     191, /* (39)typename ::= typename ID|STRING */
     189, /* (40)carglist ::= carglist ccons */
     189, /* (41)carglist ::= */
     195, /* (42)ccons ::= CONSTRAINT nm */
     195, /* (43)ccons ::= DEFAULT term */
     195, /* (44)ccons ::= DEFAULT LP expr RP */
     195, /* (45)ccons ::= DEFAULT PLUS term */
     195, /* (46)ccons ::= DEFAULT MINUS term */
     195, /* (47)ccons ::= DEFAULT ID|INDEXED */
     195, /* (48)ccons ::= NULL onconf */
     195, /* (49)ccons ::= NOT NULL onconf */
     195, /* (50)ccons ::= PRIMARY KEY sortorder onconf autoinc */
     195, /* (51)ccons ::= UNIQUE onconf */
     195, /* (52)ccons ::= CHECK LP expr RP */
     195, /* (53)ccons ::= REFERENCES nm eidlist_opt refargs */
     195, /* (54)ccons ::= defer_subclause */
     195, /* (55)ccons ::= COLLATE ID|STRING */
     195, /* (56)ccons ::= GENERATED ALWAYS AS generated */
     195, /* (57)ccons ::= AS generated */
     204, /* (58)generated ::= LP expr RP */
     204, /* (59)generated ::= LP expr RP ID */
     200, /* (60)autoinc ::= */
     200, /* (61)autoinc ::= AUTOINCR */
     202, /* (62)refargs ::= */
     202, /* (63)refargs ::= refargs refarg */
     205, /* (64)refarg ::= MATCH nm */
     205, /* (65)refarg ::= ON INSERT refact */
     205, /* (66)refarg ::= ON DELETE refact */
     205, /* (67)refarg ::= ON UPDATE refact */
     206, /* (68)refact ::= SET NULL */
     206, /* (69)refact ::= SET DEFAULT */
     206, /* (70)refact ::= CASCADE */
     206, /* (71)refact ::= RESTRICT */
     206, /* (72)refact ::= NO ACTION */
     203, /* (73)defer_subclause ::= NOT DEFERRABLE init_deferred_pred_opt */
     203, /* (74)defer_subclause ::= DEFERRABLE init_deferred_pred_opt */
     207, /* (75)init_deferred_pred_opt ::= */
     207, /* (76)init_deferred_pred_opt ::= INITIALLY DEFERRED */
     207, /* (77)init_deferred_pred_opt ::= INITIALLY IMMEDIATE */
     184, /* (78)conslist_opt ::= */
     184, /* (79)conslist_opt ::= COMMA conslist */
     208, /* (80)conslist ::= conslist tconscomma tcons */
     208, /* (81)conslist ::= tcons */
     209, /* (82)tconscomma ::= COMMA */
     210, /* (83)tcons ::= CONSTRAINT nm */
     210, /* (84)tcons ::= PRIMARY KEY LP sortlist autoinc RP onconf */
     210, /* (85)tcons ::= UNIQUE LP sortlist RP onconf */
     210, /* (86)tcons ::= CHECK LP expr RP onconf */
     210, /* (87)tcons ::= FOREIGN KEY LP eidlist RP REFERENCES nm eidlist_opt refargs defer_subclause_opt */
     213, /* (88)defer_subclause_opt ::= */
     213, /* (89)defer_subclause_opt ::= defer_subclause */
     198, /* (90)onconf ::= */
     198, /* (91)onconf ::= ON CONFLICT resolvetype */
     214, /* (92)orconf ::= */
     214, /* (93)orconf ::= OR resolvetype */
     215, /* (94)resolvetype ::= IGNORE */
     215, /* (95)resolvetype ::= REPLACE */
     173, /* (96)cmd ::= DROP TABLE ifexists fullname */
     217, /* (97)ifexists ::= IF EXISTS */
     217, /* (98)ifexists ::= */
     173, /* (99)cmd ::= createkw temp VIEW ifnotexists fullname eidlist_opt AS select */
     173, /* (100)cmd ::= DROP VIEW ifexists fullname */
     173, /* (101)cmd ::= select */
     186, /* (102)select ::= WITH wqlist selectnowith orderby_opt limit_opt */
     186, /* (103)select ::= WITH RECURSIVE wqlist selectnowith orderby_opt limit_opt */
     186, /* (104)select ::= selectnowith orderby_opt limit_opt */
     218, /* (105)selectnowith ::= oneselect */
     218, /* (106)selectnowith ::= selectnowith multiselect_op oneselect */
     223, /* (107)multiselect_op ::= UNION */
     223, /* (108)multiselect_op ::= UNION ALL */
     223, /* (109)multiselect_op ::= EXCEPT */
     223, /* (110)multiselect_op ::= INTERSECT */
     219, /* (111)oneselect ::= SELECT distinct selcollist from where_opt groupby_opt */
     219, /* (112)oneselect ::= SELECT distinct selcollist from where_opt groupby_opt window_clause */
     219, /* (113)oneselect ::= values */
     230, /* (114)values ::= VALUES LP nexprlist RP */
     230, /* (115)values ::= values COMMA LP nexprlist RP */
     224, /* (116)distinct ::= DISTINCT */
     224, /* (117)distinct ::= ALL */
     224, /* (118)distinct ::= */
     232, /* (119)sclp ::= */
     225, /* (120)selcollist ::= sclp expr as */
     225, /* (121)selcollist ::= sclp STAR */
     225, /* (122)selcollist ::= sclp nm DOT STAR */
     233, /* (123)as ::= AS nm */
     233, /* (124)as ::= ID|STRING */
     233, /* (125)as ::= */
     226, /* (126)from ::= */
     226, /* (127)from ::= FROM seltablist */
     235, /* (128)stl_prefix ::= seltablist joinop */
     235, /* (129)stl_prefix ::= */
     234, /* (130)seltablist ::= stl_prefix fullname as indexed_opt on_using */
     234, /* (131)seltablist ::= stl_prefix fullname LP exprlist RP as on_using */
     234, /* (132)seltablist ::= stl_prefix LP select RP as on_using */
     234, /* (133)seltablist ::= stl_prefix LP seltablist RP as on_using */
     181, /* (134)fullname ::= nm */
     181, /* (135)fullname ::= nm DOT nm */
     240, /* (136)xfullname ::= nm */
     240, /* (137)xfullname ::= nm DOT nm */
     240, /* (138)xfullname ::= nm DOT nm AS nm */
     240, /* (139)xfullname ::= nm AS nm */
     236, /* (140)joinop ::= COMMA */
     236, /* (141)joinop ::= JOIN */
     236, /* (142)joinop ::= JOIN_KW JOIN */
     236, /* (143)joinop ::= JOIN_KW nm JOIN */
     236, /* (144)joinop ::= JOIN_KW nm nm JOIN */
     238, /* (145)on_using ::= ON expr */
     238, /* (146)on_using ::= USING LP idlist RP */
     238, /* (147)on_using ::= */
     237, /* (148)indexed_opt ::= */
     237, /* (149)indexed_opt ::= INDEXED BY nm */
     237, /* (150)indexed_opt ::= NOT INDEXED */
     221, /* (151)orderby_opt ::= */
     221, /* (152)orderby_opt ::= ORDER BY sortlist */
     211, /* (153)sortlist ::= sortlist COMMA expr sortorder nulls */
     211, /* (154)sortlist ::= expr sortorder nulls */
     199, /* (155)sortorder ::= ASC */
     199, /* (156)sortorder ::= DESC */
     199, /* (157)sortorder ::= */
     242, /* (158)nulls ::= NULLS FIRST */
     242, /* (159)nulls ::= NULLS LAST */
     242, /* (160)nulls ::= */
     228, /* (161)groupby_opt ::= */
     228, /* (162)groupby_opt ::= GROUP BY nexprlist having_opt */
     243, /* (163)having_opt ::= */
     243, /* (164)having_opt ::= HAVING expr */
     222, /* (165)limit_opt ::= */
     222, /* (166)limit_opt ::= LIMIT expr */
     222, /* (167)limit_opt ::= LIMIT expr OFFSET expr */
     222, /* (168)limit_opt ::= LIMIT expr COMMA expr */
     173, /* (169)cmd ::= with DELETE FROM xfullname indexed_opt where_opt_ret orderby_opt limit_opt */
     227, /* (170)where_opt ::= */
     227, /* (171)where_opt ::= WHERE expr */
     245, /* (172)where_opt_ret ::= */
     245, /* (173)where_opt_ret ::= WHERE expr */
     245, /* (174)where_opt_ret ::= RETURNING selcollist */
     245, /* (175)where_opt_ret ::= WHERE expr RETURNING selcollist */
     173, /* (176)cmd ::= with UPDATE orconf xfullname indexed_opt SET setlist from where_opt_ret orderby_opt limit_opt */
     246, /* (177)setlist ::= setlist COMMA nm EQ expr */
     246, /* (178)setlist ::= setlist COMMA LP idlist RP EQ expr */
     246, /* (179)setlist ::= nm EQ expr */
     246, /* (180)setlist ::= LP idlist RP EQ expr */
     173, /* (181)cmd ::= with insert_cmd INTO xfullname idlist_opt select upsert */
     173, /* (182)cmd ::= with insert_cmd INTO xfullname idlist_opt DEFAULT VALUES returning */
     249, /* (183)upsert ::= */
     249, /* (184)upsert ::= RETURNING selcollist */
     249, /* (185)upsert ::= ON CONFLICT LP sortlist RP where_opt DO UPDATE SET setlist where_opt upsert */
     249, /* (186)upsert ::= ON CONFLICT LP sortlist RP where_opt DO NOTHING upsert */
     249, /* (187)upsert ::= ON CONFLICT DO NOTHING returning */
     249, /* (188)upsert ::= ON CONFLICT DO UPDATE SET setlist where_opt returning */
     250, /* (189)returning ::= RETURNING selcollist */
     250, /* (190)returning ::= */
     247, /* (191)insert_cmd ::= INSERT orconf */
     247, /* (192)insert_cmd ::= REPLACE */
     248, /* (193)idlist_opt ::= */
     248, /* (194)idlist_opt ::= LP idlist RP */
     241, /* (195)idlist ::= idlist COMMA nm */
     241, /* (196)idlist ::= nm */
     197, /* (197)expr ::= LP expr RP */
     197, /* (198)expr ::= ID|INDEXED|JOIN_KW */
     197, /* (199)expr ::= nm DOT nm */
     197, /* (200)expr ::= nm DOT nm DOT nm */
     196, /* (201)term ::= NULL */
     196, /* (202)term ::= BLOB */
     196, /* (203)term ::= STRING */
     196, /* (204)term ::= FLOAT|INTEGER */
     197, /* (205)expr ::= VARIABLE */
     197, /* (206)expr ::= expr COLLATE ID|STRING */
     197, /* (207)expr ::= CAST LP expr AS typetoken RP */
     197, /* (208)expr ::= ID|INDEXED|JOIN_KW LP distinct exprlist RP */
     197, /* (209)expr ::= ID|INDEXED|JOIN_KW LP STAR RP */
     197, /* (210)expr ::= ID|INDEXED|JOIN_KW LP distinct exprlist RP filter_over */
     197, /* (211)expr ::= ID|INDEXED|JOIN_KW LP STAR RP filter_over */
     196, /* (212)term ::= CTIME_KW */
     197, /* (213)expr ::= LP nexprlist COMMA expr RP */
     197, /* (214)expr ::= expr AND expr */
     197, /* (215)expr ::= expr OR expr */
     197, /* (216)expr ::= expr LT|GT|GE|LE expr */
     197, /* (217)expr ::= expr EQ|NE expr */
     197, /* (218)expr ::= expr BITAND|BITOR|LSHIFT|RSHIFT expr */
     197, /* (219)expr ::= expr PLUS|MINUS expr */
     197, /* (220)expr ::= expr STAR|SLASH|REM expr */
     197, /* (221)expr ::= expr CONCAT expr */
     252, /* (222)likeop ::= LIKE_KW|MATCH */
     252, /* (223)likeop ::= NOT LIKE_KW|MATCH */
     197, /* (224)expr ::= expr likeop expr */
     197, /* (225)expr ::= expr likeop expr ESCAPE expr */
     197, /* (226)expr ::= expr ISNULL|NOTNULL */
     197, /* (227)expr ::= expr NOT NULL */
     197, /* (228)expr ::= expr IS expr */
     197, /* (229)expr ::= expr IS NOT expr */
     197, /* (230)expr ::= expr IS NOT DISTINCT FROM expr */
     197, /* (231)expr ::= expr IS DISTINCT FROM expr */
     197, /* (232)expr ::= NOT expr */
     197, /* (233)expr ::= BITNOT expr */
     197, /* (234)expr ::= PLUS|MINUS expr */
     197, /* (235)expr ::= expr PTR expr */
     253, /* (236)between_op ::= BETWEEN */
     253, /* (237)between_op ::= NOT BETWEEN */
     197, /* (238)expr ::= expr between_op expr AND expr */
     254, /* (239)in_op ::= IN */
     254, /* (240)in_op ::= NOT IN */
     197, /* (241)expr ::= expr in_op LP exprlist RP */
     197, /* (242)expr ::= LP select RP */
     197, /* (243)expr ::= expr in_op LP select RP */
     197, /* (244)expr ::= expr in_op fullname paren_exprlist */
     197, /* (245)expr ::= EXISTS LP select RP */
     197, /* (246)expr ::= CASE case_operand case_exprlist case_else END */
     257, /* (247)case_exprlist ::= case_exprlist WHEN expr THEN expr */
     257, /* (248)case_exprlist ::= WHEN expr THEN expr */
     258, /* (249)case_else ::= ELSE expr */
     258, /* (250)case_else ::= */
     256, /* (251)case_operand ::= expr */
     256, /* (252)case_operand ::= */
     239, /* (253)exprlist ::= nexprlist */
     239, /* (254)exprlist ::= */
     231, /* (255)nexprlist ::= nexprlist COMMA expr */
     231, /* (256)nexprlist ::= expr */
     255, /* (257)paren_exprlist ::= */
     255, /* (258)paren_exprlist ::= LP exprlist RP */
     173, /* (259)cmd ::= createkw uniqueflag INDEX ifnotexists fullname ON nm LP sortlist RP where_opt */
     259, /* (260)uniqueflag ::= UNIQUE */
     259, /* (261)uniqueflag ::= */
     201, /* (262)eidlist_opt ::= */
     201, /* (263)eidlist_opt ::= LP eidlist RP */
     212, /* (264)eidlist ::= eidlist COMMA nm collate sortorder */
     212, /* (265)eidlist ::= nm collate sortorder */
     260, /* (266)collate ::= */
     260, /* (267)collate ::= COLLATE ID|STRING */
     173, /* (268)cmd ::= DROP INDEX ifexists fullname */
     173, /* (269)cmd ::= VACUUM vinto */
     173, /* (270)cmd ::= VACUUM nm vinto */
     261, /* (271)vinto ::= INTO expr */
     261, /* (272)vinto ::= */
     173, /* (273)cmd ::= PRAGMA fullname */
     173, /* (274)cmd ::= PRAGMA fullname EQ nmnum */
     173, /* (275)cmd ::= PRAGMA fullname LP nmnum RP */
     173, /* (276)cmd ::= PRAGMA fullname EQ minus_num */
     173, /* (277)cmd ::= PRAGMA fullname LP minus_num RP */
     262, /* (278)nmnum ::= nm */
     262, /* (279)nmnum ::= ON */
     262, /* (280)nmnum ::= DELETE */
     262, /* (281)nmnum ::= DEFAULT */
     193, /* (282)plus_num ::= PLUS INTEGER|FLOAT */
     193, /* (283)plus_num ::= INTEGER|FLOAT */
     194, /* (284)minus_num ::= MINUS INTEGER|FLOAT */
     173, /* (285)cmd ::= createkw temp TRIGGER ifnotexists fullname trigger_time trigger_event ON fullname foreach_clause when_clause BEGIN trigger_cmd_list END */
     263, /* (286)trigger_time ::= BEFORE */
     263, /* (287)trigger_time ::= AFTER */
     263, /* (288)trigger_time ::= INSTEAD OF */
     263, /* (289)trigger_time ::= */
     264, /* (290)trigger_event ::= DELETE */
     264, /* (291)trigger_event ::= INSERT */
     264, /* (292)trigger_event ::= UPDATE */
     264, /* (293)trigger_event ::= UPDATE OF idlist */
     265, /* (294)foreach_clause ::= */
     265, /* (295)foreach_clause ::= FOR EACH ROW */
     266, /* (296)when_clause ::= */
     266, /* (297)when_clause ::= WHEN expr */
     267, /* (298)trigger_cmd_list ::= trigger_cmd_list trigger_cmd SEMI */
     267, /* (299)trigger_cmd_list ::= trigger_cmd SEMI */
     269, /* (300)trnm ::= nm DOT nm */
     270, /* (301)tridxby ::= INDEXED BY nm */
     270, /* (302)tridxby ::= NOT INDEXED */
     268, /* (303)trigger_cmd ::= UPDATE orconf trnm tridxby SET setlist from where_opt */
     268, /* (304)trigger_cmd ::= insert_cmd INTO trnm idlist_opt select upsert */
     268, /* (305)trigger_cmd ::= DELETE FROM trnm tridxby where_opt */
     268, /* (306)trigger_cmd ::= select */
     197, /* (307)expr ::= RAISE LP IGNORE RP */
     197, /* (308)expr ::= RAISE LP raisetype COMMA nm RP */
     216, /* (309)raisetype ::= ROLLBACK */
     216, /* (310)raisetype ::= ABORT */
     216, /* (311)raisetype ::= FAIL */
     173, /* (312)cmd ::= DROP TRIGGER ifexists fullname */
     173, /* (313)cmd ::= ATTACH database_kw_opt expr AS expr key_opt */
     173, /* (314)cmd ::= DETACH database_kw_opt expr */
     272, /* (315)key_opt ::= */
     272, /* (316)key_opt ::= KEY expr */
     173, /* (317)cmd ::= REINDEX */
     173, /* (318)cmd ::= REINDEX fullname */
     173, /* (319)cmd ::= ANALYZE */
     173, /* (320)cmd ::= ANALYZE fullname */
     173, /* (321)cmd ::= ALTER TABLE fullname RENAME TO nm */
     173, /* (322)cmd ::= ALTER TABLE fullname ADD kwcolumn_opt columnname carglist */
     173, /* (323)cmd ::= ALTER TABLE fullname RENAME kwcolumn_opt nm TO nm */
     173, /* (324)cmd ::= ALTER TABLE fullname DROP kwcolumn_opt nm */
     173, /* (325)cmd ::= ALTER TABLE fullname ALTER COLUMNKW columnname TO columnname carglist */
     173, /* (326)cmd ::= create_vtab */
     173, /* (327)cmd ::= create_vtab LP vtabarglist RP */
     274, /* (328)create_vtab ::= createkw VIRTUAL TABLE ifnotexists fullname USING nm */
     276, /* (329)vtabarg ::= */
     277, /* (330)vtabargtoken ::= ANY */
     277, /* (331)vtabargtoken ::= lp anylist RP */
     278, /* (332)lp ::= LP */
     244, /* (333)with ::= */
     244, /* (334)with ::= WITH wqlist */
     244, /* (335)with ::= WITH RECURSIVE wqlist */
     281, /* (336)wqas ::= AS */
     281, /* (337)wqas ::= AS MATERIALIZED */
     281, /* (338)wqas ::= AS NOT MATERIALIZED */
     280, /* (339)wqitem ::= nm eidlist_opt wqas LP select RP */
     220, /* (340)wqlist ::= wqitem */
     220, /* (341)wqlist ::= wqlist COMMA wqitem */
     282, /* (342)windowdefn_list ::= windowdefn */
     282, /* (343)windowdefn_list ::= windowdefn_list COMMA windowdefn */
     283, /* (344)windowdefn ::= nm AS LP window RP */
     284, /* (345)window ::= PARTITION BY nexprlist orderby_opt frame_opt */
     284, /* (346)window ::= nm PARTITION BY nexprlist orderby_opt frame_opt */
     284, /* (347)window ::= ORDER BY sortlist frame_opt */
     284, /* (348)window ::= nm ORDER BY sortlist frame_opt */
     284, /* (349)window ::= frame_opt */
     284, /* (350)window ::= nm frame_opt */
     285, /* (351)frame_opt ::= */
     285, /* (352)frame_opt ::= range_or_rows frame_bound_s frame_exclude_opt */
     285, /* (353)frame_opt ::= range_or_rows BETWEEN frame_bound_s AND frame_bound_e frame_exclude_opt */
     288, /* (354)range_or_rows ::= RANGE */
     288, /* (355)range_or_rows ::= ROWS */
     288, /* (356)range_or_rows ::= GROUPS */
     290, /* (357)frame_bound_s ::= frame_bound */
     290, /* (358)frame_bound_s ::= UNBOUNDED PRECEDING */
     291, /* (359)frame_bound_e ::= frame_bound */
     291, /* (360)frame_bound_e ::= UNBOUNDED FOLLOWING */
     289, /* (361)frame_bound ::= expr PRECEDING */
     289, /* (362)frame_bound ::= CURRENT ROW */
     289, /* (363)frame_bound ::= expr FOLLOWING */
     292, /* (364)frame_exclude_opt ::= */
     292, /* (365)frame_exclude_opt ::= EXCLUDE frame_exclude */
     293, /* (366)frame_exclude ::= NO OTHERS */
     293, /* (367)frame_exclude ::= CURRENT ROW */
     293, /* (368)frame_exclude ::= GROUP */
     293, /* (369)frame_exclude ::= TIES */
     229, /* (370)window_clause ::= WINDOW windowdefn_list */
     251, /* (371)filter_over ::= filter_clause over_clause */
     251, /* (372)filter_over ::= over_clause */
     251, /* (373)filter_over ::= filter_clause */
     287, /* (374)over_clause ::= OVER LP window RP */
     287, /* (375)over_clause ::= OVER nm */
     286, /* (376)filter_clause ::= FILTER LP WHERE expr RP */
     168, /* (377)input ::= cmdlist */
     169, /* (378)cmdlist ::= cmdlist ecmd */
     169, /* (379)cmdlist ::= ecmd */
     170, /* (380)ecmd ::= SEMI */
     170, /* (381)ecmd ::= cmdx SEMI */
     170, /* (382)ecmd ::= explain cmdx SEMI */
     177, /* (383)savepoint_opt ::= SAVEPOINT */
     177, /* (384)savepoint_opt ::= */
     178, /* (385)createkw ::= CREATE */
     185, /* (386)table_option_set ::= table_option */
     192, /* (387)signed ::= plus_num */
     192, /* (388)signed ::= minus_num */
     209, /* (389)tconscomma ::= */
     215, /* (390)resolvetype ::= raisetype */
     232, /* (391)sclp ::= selcollist COMMA */
     197, /* (392)expr ::= term */
     262, /* (393)nmnum ::= plus_num */
     269, /* (394)trnm ::= nm */
     270, /* (395)tridxby ::= */
     271, /* (396)database_kw_opt ::= DATABASE */
     271, /* (397)database_kw_opt ::= */
     273, /* (398)kwcolumn_opt ::= */
     273, /* (399)kwcolumn_opt ::= COLUMNKW */
     275, /* (400)vtabarglist ::= vtabarg */
     275, /* (401)vtabarglist ::= vtabarglist COMMA vtabarg */
     276, /* (402)vtabarg ::= vtabarg vtabargtoken */
     279, /* (403)anylist ::= */
     279, /* (404)anylist ::= anylist LP anylist RP */
     279, /* (405)anylist ::= anylist ANY */
];

/* For rule J, yyRuleInfoNRhs[J] contains the negative of the number
** of symbols on the right-hand side of that rule. */
#[allow(non_upper_case_globals)]
static yyRuleInfoNRhs: [i8; YYNRULE] = [
     -1,  /* (0)explain ::= EXPLAIN */
     -3,  /* (1)explain ::= EXPLAIN QUERY PLAN */
     -1,  /* (2)cmdx ::= cmd */
     -3,  /* (3)cmd ::= BEGIN transtype trans_opt */
      0,  /* (4)trans_opt ::= */
     -1,  /* (5)trans_opt ::= TRANSACTION */
     -2,  /* (6)trans_opt ::= TRANSACTION nm */
      0,  /* (7)transtype ::= */
     -1,  /* (8)transtype ::= DEFERRED */
     -1,  /* (9)transtype ::= IMMEDIATE */
     -1,  /* (10)transtype ::= EXCLUSIVE */
     -1,  /* (11)transtype ::= READONLY */
     -2,  /* (12)cmd ::= COMMIT|END trans_opt */
     -2,  /* (13)cmd ::= ROLLBACK trans_opt */
     -2,  /* (14)cmd ::= SAVEPOINT nm */
     -3,  /* (15)cmd ::= RELEASE savepoint_opt nm */
     -5,  /* (16)cmd ::= ROLLBACK trans_opt TO savepoint_opt nm */
     -6,  /* (17)cmd ::= createkw temp TABLE ifnotexists fullname create_table_args */
      0,  /* (18)ifnotexists ::= */
     -3,  /* (19)ifnotexists ::= IF NOT EXISTS */
     -1,  /* (20)temp ::= TEMP */
      0,  /* (21)temp ::= */
     -5,  /* (22)create_table_args ::= LP columnlist conslist_opt RP table_option_set */
     -2,  /* (23)create_table_args ::= AS select */
      0,  /* (24)table_option_set ::= */
     -3,  /* (25)table_option_set ::= table_option_set COMMA table_option */
     -2,  /* (26)table_option ::= WITHOUT nm */
     -2,  /* (27)table_option ::= nm nm */
     -1,  /* (28)table_option ::= nm */
     -4,  /* (29)columnlist ::= columnlist COMMA columnname carglist */
     -2,  /* (30)columnlist ::= columnname carglist */
     -2,  /* (31)columnname ::= nm typetoken */
     -1,  /* (32)nm ::= ID|INDEXED|JOIN_KW */
     -1,  /* (33)nm ::= STRING */
      0,  /* (34)typetoken ::= */
     -1,  /* (35)typetoken ::= typename */
     -4,  /* (36)typetoken ::= typename LP signed RP */
     -6,  /* (37)typetoken ::= typename LP signed COMMA signed RP */
     -1,  /* (38)typename ::= ID|STRING */
     -2,  /* (39)typename ::= typename ID|STRING */
     -2,  /* (40)carglist ::= carglist ccons */
      0,  /* (41)carglist ::= */
     -2,  /* (42)ccons ::= CONSTRAINT nm */
     -2,  /* (43)ccons ::= DEFAULT term */
     -4,  /* (44)ccons ::= DEFAULT LP expr RP */
     -3,  /* (45)ccons ::= DEFAULT PLUS term */
     -3,  /* (46)ccons ::= DEFAULT MINUS term */
     -2,  /* (47)ccons ::= DEFAULT ID|INDEXED */
     -2,  /* (48)ccons ::= NULL onconf */
     -3,  /* (49)ccons ::= NOT NULL onconf */
     -5,  /* (50)ccons ::= PRIMARY KEY sortorder onconf autoinc */
     -2,  /* (51)ccons ::= UNIQUE onconf */
     -4,  /* (52)ccons ::= CHECK LP expr RP */
     -4,  /* (53)ccons ::= REFERENCES nm eidlist_opt refargs */
     -1,  /* (54)ccons ::= defer_subclause */
     -2,  /* (55)ccons ::= COLLATE ID|STRING */
     -4,  /* (56)ccons ::= GENERATED ALWAYS AS generated */
     -2,  /* (57)ccons ::= AS generated */
     -3,  /* (58)generated ::= LP expr RP */
     -4,  /* (59)generated ::= LP expr RP ID */
      0,  /* (60)autoinc ::= */
     -1,  /* (61)autoinc ::= AUTOINCR */
      0,  /* (62)refargs ::= */
     -2,  /* (63)refargs ::= refargs refarg */
     -2,  /* (64)refarg ::= MATCH nm */
     -3,  /* (65)refarg ::= ON INSERT refact */
     -3,  /* (66)refarg ::= ON DELETE refact */
     -3,  /* (67)refarg ::= ON UPDATE refact */
     -2,  /* (68)refact ::= SET NULL */
     -2,  /* (69)refact ::= SET DEFAULT */
     -1,  /* (70)refact ::= CASCADE */
     -1,  /* (71)refact ::= RESTRICT */
     -2,  /* (72)refact ::= NO ACTION */
     -3,  /* (73)defer_subclause ::= NOT DEFERRABLE init_deferred_pred_opt */
     -2,  /* (74)defer_subclause ::= DEFERRABLE init_deferred_pred_opt */
      0,  /* (75)init_deferred_pred_opt ::= */
     -2,  /* (76)init_deferred_pred_opt ::= INITIALLY DEFERRED */
     -2,  /* (77)init_deferred_pred_opt ::= INITIALLY IMMEDIATE */
      0,  /* (78)conslist_opt ::= */
     -2,  /* (79)conslist_opt ::= COMMA conslist */
     -3,  /* (80)conslist ::= conslist tconscomma tcons */
     -1,  /* (81)conslist ::= tcons */
     -1,  /* (82)tconscomma ::= COMMA */
     -2,  /* (83)tcons ::= CONSTRAINT nm */
     -7,  /* (84)tcons ::= PRIMARY KEY LP sortlist autoinc RP onconf */
     -5,  /* (85)tcons ::= UNIQUE LP sortlist RP onconf */
     -5,  /* (86)tcons ::= CHECK LP expr RP onconf */
    -10,  /* (87)tcons ::= FOREIGN KEY LP eidlist RP REFERENCES nm eidlist_opt refargs defer_subclause_opt */
      0,  /* (88)defer_subclause_opt ::= */
     -1,  /* (89)defer_subclause_opt ::= defer_subclause */
      0,  /* (90)onconf ::= */
     -3,  /* (91)onconf ::= ON CONFLICT resolvetype */
      0,  /* (92)orconf ::= */
     -2,  /* (93)orconf ::= OR resolvetype */
     -1,  /* (94)resolvetype ::= IGNORE */
     -1,  /* (95)resolvetype ::= REPLACE */
     -4,  /* (96)cmd ::= DROP TABLE ifexists fullname */
     -2,  /* (97)ifexists ::= IF EXISTS */
      0,  /* (98)ifexists ::= */
     -8,  /* (99)cmd ::= createkw temp VIEW ifnotexists fullname eidlist_opt AS select */
     -4,  /* (100)cmd ::= DROP VIEW ifexists fullname */
     -1,  /* (101)cmd ::= select */
     -5,  /* (102)select ::= WITH wqlist selectnowith orderby_opt limit_opt */
     -6,  /* (103)select ::= WITH RECURSIVE wqlist selectnowith orderby_opt limit_opt */
     -3,  /* (104)select ::= selectnowith orderby_opt limit_opt */
     -1,  /* (105)selectnowith ::= oneselect */
     -3,  /* (106)selectnowith ::= selectnowith multiselect_op oneselect */
     -1,  /* (107)multiselect_op ::= UNION */
     -2,  /* (108)multiselect_op ::= UNION ALL */
     -1,  /* (109)multiselect_op ::= EXCEPT */
     -1,  /* (110)multiselect_op ::= INTERSECT */
     -6,  /* (111)oneselect ::= SELECT distinct selcollist from where_opt groupby_opt */
     -7,  /* (112)oneselect ::= SELECT distinct selcollist from where_opt groupby_opt window_clause */
     -1,  /* (113)oneselect ::= values */
     -4,  /* (114)values ::= VALUES LP nexprlist RP */
     -5,  /* (115)values ::= values COMMA LP nexprlist RP */
     -1,  /* (116)distinct ::= DISTINCT */
     -1,  /* (117)distinct ::= ALL */
      0,  /* (118)distinct ::= */
      0,  /* (119)sclp ::= */
     -3,  /* (120)selcollist ::= sclp expr as */
     -2,  /* (121)selcollist ::= sclp STAR */
     -4,  /* (122)selcollist ::= sclp nm DOT STAR */
     -2,  /* (123)as ::= AS nm */
     -1,  /* (124)as ::= ID|STRING */
      0,  /* (125)as ::= */
      0,  /* (126)from ::= */
     -2,  /* (127)from ::= FROM seltablist */
     -2,  /* (128)stl_prefix ::= seltablist joinop */
      0,  /* (129)stl_prefix ::= */
     -5,  /* (130)seltablist ::= stl_prefix fullname as indexed_opt on_using */
     -7,  /* (131)seltablist ::= stl_prefix fullname LP exprlist RP as on_using */
     -6,  /* (132)seltablist ::= stl_prefix LP select RP as on_using */
     -6,  /* (133)seltablist ::= stl_prefix LP seltablist RP as on_using */
     -1,  /* (134)fullname ::= nm */
     -3,  /* (135)fullname ::= nm DOT nm */
     -1,  /* (136)xfullname ::= nm */
     -3,  /* (137)xfullname ::= nm DOT nm */
     -5,  /* (138)xfullname ::= nm DOT nm AS nm */
     -3,  /* (139)xfullname ::= nm AS nm */
     -1,  /* (140)joinop ::= COMMA */
     -1,  /* (141)joinop ::= JOIN */
     -2,  /* (142)joinop ::= JOIN_KW JOIN */
     -3,  /* (143)joinop ::= JOIN_KW nm JOIN */
     -4,  /* (144)joinop ::= JOIN_KW nm nm JOIN */
     -2,  /* (145)on_using ::= ON expr */
     -4,  /* (146)on_using ::= USING LP idlist RP */
      0,  /* (147)on_using ::= */
      0,  /* (148)indexed_opt ::= */
     -3,  /* (149)indexed_opt ::= INDEXED BY nm */
     -2,  /* (150)indexed_opt ::= NOT INDEXED */
      0,  /* (151)orderby_opt ::= */
     -3,  /* (152)orderby_opt ::= ORDER BY sortlist */
     -5,  /* (153)sortlist ::= sortlist COMMA expr sortorder nulls */
     -3,  /* (154)sortlist ::= expr sortorder nulls */
     -1,  /* (155)sortorder ::= ASC */
     -1,  /* (156)sortorder ::= DESC */
      0,  /* (157)sortorder ::= */
     -2,  /* (158)nulls ::= NULLS FIRST */
     -2,  /* (159)nulls ::= NULLS LAST */
      0,  /* (160)nulls ::= */
      0,  /* (161)groupby_opt ::= */
     -4,  /* (162)groupby_opt ::= GROUP BY nexprlist having_opt */
      0,  /* (163)having_opt ::= */
     -2,  /* (164)having_opt ::= HAVING expr */
      0,  /* (165)limit_opt ::= */
     -2,  /* (166)limit_opt ::= LIMIT expr */
     -4,  /* (167)limit_opt ::= LIMIT expr OFFSET expr */
     -4,  /* (168)limit_opt ::= LIMIT expr COMMA expr */
     -8,  /* (169)cmd ::= with DELETE FROM xfullname indexed_opt where_opt_ret orderby_opt limit_opt */
      0,  /* (170)where_opt ::= */
     -2,  /* (171)where_opt ::= WHERE expr */
      0,  /* (172)where_opt_ret ::= */
     -2,  /* (173)where_opt_ret ::= WHERE expr */
     -2,  /* (174)where_opt_ret ::= RETURNING selcollist */
     -4,  /* (175)where_opt_ret ::= WHERE expr RETURNING selcollist */
    -11,  /* (176)cmd ::= with UPDATE orconf xfullname indexed_opt SET setlist from where_opt_ret orderby_opt limit_opt */
     -5,  /* (177)setlist ::= setlist COMMA nm EQ expr */
     -7,  /* (178)setlist ::= setlist COMMA LP idlist RP EQ expr */
     -3,  /* (179)setlist ::= nm EQ expr */
     -5,  /* (180)setlist ::= LP idlist RP EQ expr */
     -7,  /* (181)cmd ::= with insert_cmd INTO xfullname idlist_opt select upsert */
     -8,  /* (182)cmd ::= with insert_cmd INTO xfullname idlist_opt DEFAULT VALUES returning */
      0,  /* (183)upsert ::= */
     -2,  /* (184)upsert ::= RETURNING selcollist */
    -12,  /* (185)upsert ::= ON CONFLICT LP sortlist RP where_opt DO UPDATE SET setlist where_opt upsert */
     -9,  /* (186)upsert ::= ON CONFLICT LP sortlist RP where_opt DO NOTHING upsert */
     -5,  /* (187)upsert ::= ON CONFLICT DO NOTHING returning */
     -8,  /* (188)upsert ::= ON CONFLICT DO UPDATE SET setlist where_opt returning */
     -2,  /* (189)returning ::= RETURNING selcollist */
      0,  /* (190)returning ::= */
     -2,  /* (191)insert_cmd ::= INSERT orconf */
     -1,  /* (192)insert_cmd ::= REPLACE */
      0,  /* (193)idlist_opt ::= */
     -3,  /* (194)idlist_opt ::= LP idlist RP */
     -3,  /* (195)idlist ::= idlist COMMA nm */
     -1,  /* (196)idlist ::= nm */
     -3,  /* (197)expr ::= LP expr RP */
     -1,  /* (198)expr ::= ID|INDEXED|JOIN_KW */
     -3,  /* (199)expr ::= nm DOT nm */
     -5,  /* (200)expr ::= nm DOT nm DOT nm */
     -1,  /* (201)term ::= NULL */
     -1,  /* (202)term ::= BLOB */
     -1,  /* (203)term ::= STRING */
     -1,  /* (204)term ::= FLOAT|INTEGER */
     -1,  /* (205)expr ::= VARIABLE */
     -3,  /* (206)expr ::= expr COLLATE ID|STRING */
     -6,  /* (207)expr ::= CAST LP expr AS typetoken RP */
     -5,  /* (208)expr ::= ID|INDEXED|JOIN_KW LP distinct exprlist RP */
     -4,  /* (209)expr ::= ID|INDEXED|JOIN_KW LP STAR RP */
     -6,  /* (210)expr ::= ID|INDEXED|JOIN_KW LP distinct exprlist RP filter_over */
     -5,  /* (211)expr ::= ID|INDEXED|JOIN_KW LP STAR RP filter_over */
     -1,  /* (212)term ::= CTIME_KW */
     -5,  /* (213)expr ::= LP nexprlist COMMA expr RP */
     -3,  /* (214)expr ::= expr AND expr */
     -3,  /* (215)expr ::= expr OR expr */
     -3,  /* (216)expr ::= expr LT|GT|GE|LE expr */
     -3,  /* (217)expr ::= expr EQ|NE expr */
     -3,  /* (218)expr ::= expr BITAND|BITOR|LSHIFT|RSHIFT expr */
     -3,  /* (219)expr ::= expr PLUS|MINUS expr */
     -3,  /* (220)expr ::= expr STAR|SLASH|REM expr */
     -3,  /* (221)expr ::= expr CONCAT expr */
     -1,  /* (222)likeop ::= LIKE_KW|MATCH */
     -2,  /* (223)likeop ::= NOT LIKE_KW|MATCH */
     -3,  /* (224)expr ::= expr likeop expr */
     -5,  /* (225)expr ::= expr likeop expr ESCAPE expr */
     -2,  /* (226)expr ::= expr ISNULL|NOTNULL */
     -3,  /* (227)expr ::= expr NOT NULL */
     -3,  /* (228)expr ::= expr IS expr */
     -4,  /* (229)expr ::= expr IS NOT expr */
     -6,  /* (230)expr ::= expr IS NOT DISTINCT FROM expr */
     -5,  /* (231)expr ::= expr IS DISTINCT FROM expr */
     -2,  /* (232)expr ::= NOT expr */
     -2,  /* (233)expr ::= BITNOT expr */
     -2,  /* (234)expr ::= PLUS|MINUS expr */
     -3,  /* (235)expr ::= expr PTR expr */
     -1,  /* (236)between_op ::= BETWEEN */
     -2,  /* (237)between_op ::= NOT BETWEEN */
     -5,  /* (238)expr ::= expr between_op expr AND expr */
     -1,  /* (239)in_op ::= IN */
     -2,  /* (240)in_op ::= NOT IN */
     -5,  /* (241)expr ::= expr in_op LP exprlist RP */
     -3,  /* (242)expr ::= LP select RP */
     -5,  /* (243)expr ::= expr in_op LP select RP */
     -4,  /* (244)expr ::= expr in_op fullname paren_exprlist */
     -4,  /* (245)expr ::= EXISTS LP select RP */
     -5,  /* (246)expr ::= CASE case_operand case_exprlist case_else END */
     -5,  /* (247)case_exprlist ::= case_exprlist WHEN expr THEN expr */
     -4,  /* (248)case_exprlist ::= WHEN expr THEN expr */
     -2,  /* (249)case_else ::= ELSE expr */
      0,  /* (250)case_else ::= */
     -1,  /* (251)case_operand ::= expr */
      0,  /* (252)case_operand ::= */
     -1,  /* (253)exprlist ::= nexprlist */
      0,  /* (254)exprlist ::= */
     -3,  /* (255)nexprlist ::= nexprlist COMMA expr */
     -1,  /* (256)nexprlist ::= expr */
      0,  /* (257)paren_exprlist ::= */
     -3,  /* (258)paren_exprlist ::= LP exprlist RP */
    -11,  /* (259)cmd ::= createkw uniqueflag INDEX ifnotexists fullname ON nm LP sortlist RP where_opt */
     -1,  /* (260)uniqueflag ::= UNIQUE */
      0,  /* (261)uniqueflag ::= */
      0,  /* (262)eidlist_opt ::= */
     -3,  /* (263)eidlist_opt ::= LP eidlist RP */
     -5,  /* (264)eidlist ::= eidlist COMMA nm collate sortorder */
     -3,  /* (265)eidlist ::= nm collate sortorder */
      0,  /* (266)collate ::= */
     -2,  /* (267)collate ::= COLLATE ID|STRING */
     -4,  /* (268)cmd ::= DROP INDEX ifexists fullname */
     -2,  /* (269)cmd ::= VACUUM vinto */
     -3,  /* (270)cmd ::= VACUUM nm vinto */
     -2,  /* (271)vinto ::= INTO expr */
      0,  /* (272)vinto ::= */
     -2,  /* (273)cmd ::= PRAGMA fullname */
     -4,  /* (274)cmd ::= PRAGMA fullname EQ nmnum */
     -5,  /* (275)cmd ::= PRAGMA fullname LP nmnum RP */
     -4,  /* (276)cmd ::= PRAGMA fullname EQ minus_num */
     -5,  /* (277)cmd ::= PRAGMA fullname LP minus_num RP */
     -1,  /* (278)nmnum ::= nm */
     -1,  /* (279)nmnum ::= ON */
     -1,  /* (280)nmnum ::= DELETE */
     -1,  /* (281)nmnum ::= DEFAULT */
     -2,  /* (282)plus_num ::= PLUS INTEGER|FLOAT */
     -1,  /* (283)plus_num ::= INTEGER|FLOAT */
     -2,  /* (284)minus_num ::= MINUS INTEGER|FLOAT */
    -14,  /* (285)cmd ::= createkw temp TRIGGER ifnotexists fullname trigger_time trigger_event ON fullname foreach_clause when_clause BEGIN trigger_cmd_list END */
     -1,  /* (286)trigger_time ::= BEFORE */
     -1,  /* (287)trigger_time ::= AFTER */
     -2,  /* (288)trigger_time ::= INSTEAD OF */
      0,  /* (289)trigger_time ::= */
     -1,  /* (290)trigger_event ::= DELETE */
     -1,  /* (291)trigger_event ::= INSERT */
     -1,  /* (292)trigger_event ::= UPDATE */
     -3,  /* (293)trigger_event ::= UPDATE OF idlist */
      0,  /* (294)foreach_clause ::= */
     -3,  /* (295)foreach_clause ::= FOR EACH ROW */
      0,  /* (296)when_clause ::= */
     -2,  /* (297)when_clause ::= WHEN expr */
     -3,  /* (298)trigger_cmd_list ::= trigger_cmd_list trigger_cmd SEMI */
     -2,  /* (299)trigger_cmd_list ::= trigger_cmd SEMI */
     -3,  /* (300)trnm ::= nm DOT nm */
     -3,  /* (301)tridxby ::= INDEXED BY nm */
     -2,  /* (302)tridxby ::= NOT INDEXED */
     -8,  /* (303)trigger_cmd ::= UPDATE orconf trnm tridxby SET setlist from where_opt */
     -6,  /* (304)trigger_cmd ::= insert_cmd INTO trnm idlist_opt select upsert */
     -5,  /* (305)trigger_cmd ::= DELETE FROM trnm tridxby where_opt */
     -1,  /* (306)trigger_cmd ::= select */
     -4,  /* (307)expr ::= RAISE LP IGNORE RP */
     -6,  /* (308)expr ::= RAISE LP raisetype COMMA nm RP */
     -1,  /* (309)raisetype ::= ROLLBACK */
     -1,  /* (310)raisetype ::= ABORT */
     -1,  /* (311)raisetype ::= FAIL */
     -4,  /* (312)cmd ::= DROP TRIGGER ifexists fullname */
     -6,  /* (313)cmd ::= ATTACH database_kw_opt expr AS expr key_opt */
     -3,  /* (314)cmd ::= DETACH database_kw_opt expr */
      0,  /* (315)key_opt ::= */
     -2,  /* (316)key_opt ::= KEY expr */
     -1,  /* (317)cmd ::= REINDEX */
     -2,  /* (318)cmd ::= REINDEX fullname */
     -1,  /* (319)cmd ::= ANALYZE */
     -2,  /* (320)cmd ::= ANALYZE fullname */
     -6,  /* (321)cmd ::= ALTER TABLE fullname RENAME TO nm */
     -7,  /* (322)cmd ::= ALTER TABLE fullname ADD kwcolumn_opt columnname carglist */
     -8,  /* (323)cmd ::= ALTER TABLE fullname RENAME kwcolumn_opt nm TO nm */
     -6,  /* (324)cmd ::= ALTER TABLE fullname DROP kwcolumn_opt nm */
     -9,  /* (325)cmd ::= ALTER TABLE fullname ALTER COLUMNKW columnname TO columnname carglist */
     -1,  /* (326)cmd ::= create_vtab */
     -4,  /* (327)cmd ::= create_vtab LP vtabarglist RP */
     -7,  /* (328)create_vtab ::= createkw VIRTUAL TABLE ifnotexists fullname USING nm */
      0,  /* (329)vtabarg ::= */
     -1,  /* (330)vtabargtoken ::= ANY */
     -3,  /* (331)vtabargtoken ::= lp anylist RP */
     -1,  /* (332)lp ::= LP */
      0,  /* (333)with ::= */
     -2,  /* (334)with ::= WITH wqlist */
     -3,  /* (335)with ::= WITH RECURSIVE wqlist */
     -1,  /* (336)wqas ::= AS */
     -2,  /* (337)wqas ::= AS MATERIALIZED */
     -3,  /* (338)wqas ::= AS NOT MATERIALIZED */
     -6,  /* (339)wqitem ::= nm eidlist_opt wqas LP select RP */
     -1,  /* (340)wqlist ::= wqitem */
     -3,  /* (341)wqlist ::= wqlist COMMA wqitem */
     -1,  /* (342)windowdefn_list ::= windowdefn */
     -3,  /* (343)windowdefn_list ::= windowdefn_list COMMA windowdefn */
     -5,  /* (344)windowdefn ::= nm AS LP window RP */
     -5,  /* (345)window ::= PARTITION BY nexprlist orderby_opt frame_opt */
     -6,  /* (346)window ::= nm PARTITION BY nexprlist orderby_opt frame_opt */
     -4,  /* (347)window ::= ORDER BY sortlist frame_opt */
     -5,  /* (348)window ::= nm ORDER BY sortlist frame_opt */
     -1,  /* (349)window ::= frame_opt */
     -2,  /* (350)window ::= nm frame_opt */
      0,  /* (351)frame_opt ::= */
     -3,  /* (352)frame_opt ::= range_or_rows frame_bound_s frame_exclude_opt */
     -6,  /* (353)frame_opt ::= range_or_rows BETWEEN frame_bound_s AND frame_bound_e frame_exclude_opt */
     -1,  /* (354)range_or_rows ::= RANGE */
     -1,  /* (355)range_or_rows ::= ROWS */
     -1,  /* (356)range_or_rows ::= GROUPS */
     -1,  /* (357)frame_bound_s ::= frame_bound */
     -2,  /* (358)frame_bound_s ::= UNBOUNDED PRECEDING */
     -1,  /* (359)frame_bound_e ::= frame_bound */
     -2,  /* (360)frame_bound_e ::= UNBOUNDED FOLLOWING */
     -2,  /* (361)frame_bound ::= expr PRECEDING */
     -2,  /* (362)frame_bound ::= CURRENT ROW */
     -2,  /* (363)frame_bound ::= expr FOLLOWING */
      0,  /* (364)frame_exclude_opt ::= */
     -2,  /* (365)frame_exclude_opt ::= EXCLUDE frame_exclude */
     -2,  /* (366)frame_exclude ::= NO OTHERS */
     -2,  /* (367)frame_exclude ::= CURRENT ROW */
     -1,  /* (368)frame_exclude ::= GROUP */
     -1,  /* (369)frame_exclude ::= TIES */
     -2,  /* (370)window_clause ::= WINDOW windowdefn_list */
     -2,  /* (371)filter_over ::= filter_clause over_clause */
     -1,  /* (372)filter_over ::= over_clause */
     -1,  /* (373)filter_over ::= filter_clause */
     -4,  /* (374)over_clause ::= OVER LP window RP */
     -2,  /* (375)over_clause ::= OVER nm */
     -5,  /* (376)filter_clause ::= FILTER LP WHERE expr RP */
     -1,  /* (377)input ::= cmdlist */
     -2,  /* (378)cmdlist ::= cmdlist ecmd */
     -1,  /* (379)cmdlist ::= ecmd */
     -1,  /* (380)ecmd ::= SEMI */
     -2,  /* (381)ecmd ::= cmdx SEMI */
     -3,  /* (382)ecmd ::= explain cmdx SEMI */
     -1,  /* (383)savepoint_opt ::= SAVEPOINT */
      0,  /* (384)savepoint_opt ::= */
     -1,  /* (385)createkw ::= CREATE */
     -1,  /* (386)table_option_set ::= table_option */
     -1,  /* (387)signed ::= plus_num */
     -1,  /* (388)signed ::= minus_num */
      0,  /* (389)tconscomma ::= */
     -1,  /* (390)resolvetype ::= raisetype */
     -2,  /* (391)sclp ::= selcollist COMMA */
     -1,  /* (392)expr ::= term */
     -1,  /* (393)nmnum ::= plus_num */
     -1,  /* (394)trnm ::= nm */
      0,  /* (395)tridxby ::= */
     -1,  /* (396)database_kw_opt ::= DATABASE */
      0,  /* (397)database_kw_opt ::= */
      0,  /* (398)kwcolumn_opt ::= */
     -1,  /* (399)kwcolumn_opt ::= COLUMNKW */
     -1,  /* (400)vtabarglist ::= vtabarg */
     -3,  /* (401)vtabarglist ::= vtabarglist COMMA vtabarg */
     -2,  /* (402)vtabarg ::= vtabarg vtabargtoken */
      0,  /* (403)anylist ::= */
     -4,  /* (404)anylist ::= anylist LP anylist RP */
     -2,  /* (405)anylist ::= anylist ANY */
];

/*
** Perform a reduce action and the shift that must immediately
** follow the reduce.
**
** The yyLookahead and yyLookaheadToken parameters provide reduce actions
** access to the lookahead token (if any).  The yyLookahead will be YYNOCODE
** if the lookahead token has already been consumed.  As this procedure is
** only called from one place, optimizing compilers will in-line it, which
** means that the extra parameters have no performance impact.
*/
impl yyParser<'_> {
    fn yy_reduce(
        &mut self,
        yyruleno: YYACTIONTYPE,    /* Number of the rule by which to reduce */
        yy_look_ahead: YYCODETYPE, /* Lookahead token, or YYNOCODE if none */
        yy_lookahead_token: &sqlite3ParserTOKENTYPE, /* Value of the lookahead token */
    ) -> Result<YYACTIONTYPE, sqlite3ParserError> {
        let _ = yy_look_ahead;
        let _ = yy_lookahead_token;

        let yylhsminor: YYMINORTYPE;
        match yyruleno {
  /* Beginning here are the reduction cases.  A typical example
  ** follows:
  **   case 0:
  **  #line <lineno> <grammarfile>
  **     { ... }           // User supplied code
  **  #line <lineno> <thisfile>
  **     break;
  */
/********** Begin reduce actions **********************************************/
      0 /* explain ::= EXPLAIN */
     => {
//line 81 "src/parser/parse.y"
{ self.ctx.explain = Some(ExplainKind::Explain); }
        }
      1 /* explain ::= EXPLAIN QUERY PLAN */
     => {
//line 82 "src/parser/parse.y"
{ self.ctx.explain = Some(ExplainKind::QueryPlan); }
        }
      2 /* cmdx ::= cmd */
     => {
//line 84 "src/parser/parse.y"
{ self.ctx.sqlite3_finish_coding(); }
        }
      3 /* cmd ::= BEGIN transtype trans_opt */
     => {
//line 89 "src/parser/parse.y"
{self.ctx.stmt = Some(Stmt::Begin(self.yy_move(-1).yy276(), self.yy_move(0).yy329()));}
        }
      4 /* trans_opt ::= */
     | 266 /* collate ::= */
     => {
//line 91 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy329( None);}
        }
      5 /* trans_opt ::= TRANSACTION */
     => {
//line 92 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy329( None);}
        }
      6 /* trans_opt ::= TRANSACTION nm */
     => {
//line 93 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy329( Some(self.yy_move(0).yy222()));}
        }
      7 /* transtype ::= */
     => {
//line 95 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy276( None);}
        }
      8 /* transtype ::= DEFERRED */
     => {
//line 96 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy276( Some(TransactionType::Deferred));}
        }
      9 /* transtype ::= IMMEDIATE */
     => {
//line 97 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy276( Some(TransactionType::Immediate));}
        }
      10 /* transtype ::= EXCLUSIVE */
     => {
//line 98 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy276( Some(TransactionType::Exclusive));}
        }
      11 /* transtype ::= READONLY */
     => {
//line 99 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy276( Some(TransactionType::ReadOnly));}
        }
      12 /* cmd ::= COMMIT|END trans_opt */
     => {
//line 100 "src/parser/parse.y"
{self.ctx.stmt = Some(Stmt::Commit(self.yy_move(0).yy329()));}
        }
      13 /* cmd ::= ROLLBACK trans_opt */
     => {
//line 101 "src/parser/parse.y"
{self.ctx.stmt = Some(Stmt::Rollback{tx_name: self.yy_move(0).yy329(), savepoint_name: None});}
        }
      14 /* cmd ::= SAVEPOINT nm */
     => {
//line 105 "src/parser/parse.y"
{
  self.ctx.stmt = Some(Stmt::Savepoint(self.yy_move(0).yy222()));
}
        }
      15 /* cmd ::= RELEASE savepoint_opt nm */
     => {
//line 108 "src/parser/parse.y"
{
  self.ctx.stmt = Some(Stmt::Release(self.yy_move(0).yy222()));
}
        }
      16 /* cmd ::= ROLLBACK trans_opt TO savepoint_opt nm */
     => {
//line 111 "src/parser/parse.y"
{
  self.ctx.stmt = Some(Stmt::Rollback{tx_name: self.yy_move(-3).yy329(), savepoint_name: Some(self.yy_move(0).yy222())});
}
        }
      17 /* cmd ::= createkw temp TABLE ifnotexists fullname create_table_args */
     => {
//line 117 "src/parser/parse.y"
{
  self.ctx.stmt = Some(Stmt::CreateTable{ temporary: self.yy_move(-4).yy173(), if_not_exists: self.yy_move(-2).yy173(), tbl_name: self.yy_move(-1).yy474(), body: self.yy_move(0).yy203() });
}
        }
      18 /* ifnotexists ::= */
     | 21 /* temp ::= */
     | 60 /* autoinc ::= */
     | 98 /* ifexists ::= */
     | 261 /* uniqueflag ::= */
     => {
//line 123 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy173( false);}
        }
      19 /* ifnotexists ::= IF NOT EXISTS */
     => {
//line 124 "src/parser/parse.y"
{self[-2] .minor= YYMINORTYPE::yy173( true);}
        }
      20 /* temp ::= TEMP */
     | 61 /* autoinc ::= AUTOINCR */
     | 260 /* uniqueflag ::= UNIQUE */
     => {
//line 127 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy173( true);}
        }
      22 /* create_table_args ::= LP columnlist conslist_opt RP table_option_set */
     => {
//line 132 "src/parser/parse.y"
{
  self[-4] .minor= YYMINORTYPE::yy203( CreateTableBody::columns_and_constraints(self.yy_move(-3).yy208(), self.yy_move(-2).yy182(), self.yy_move(0).yy201())?);
}
        }
      23 /* create_table_args ::= AS select */
     => {
//line 135 "src/parser/parse.y"
{
  self[-1] .minor= YYMINORTYPE::yy203( CreateTableBody::AsSelect(self.yy_move(0).yy213()));
}
        }
      24 /* table_option_set ::= */
     => {
//line 140 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy201( TableOptions::NONE);}
        }
      25 /* table_option_set ::= table_option_set COMMA table_option */
     => {
//line 142 "src/parser/parse.y"
{yylhsminor = YYMINORTYPE::yy201( self.yy_move(-2).yy201()|self.yy_move(0).yy201());}
  self[-2].minor = yylhsminor;
        }
      26 /* table_option ::= WITHOUT nm */
     => {
//line 143 "src/parser/parse.y"
{
  let name = self.yy_move(0).yy222();
  if "rowid".eq_ignore_ascii_case(&name.0) {
    self[-1] .minor= YYMINORTYPE::yy201( TableOptions::WITHOUT_ROWID);
  }else{
    // self[-1] .minor= YYMINORTYPE::yy201( TableOptions::NONE);
    let msg = format!("unknown table option: {name}");
    self.ctx.sqlite3_error_msg(&msg);
    return Err(ParserError::Custom(msg));
  }
}
        }
      27 /* table_option ::= nm nm */
     => {
//line 154 "src/parser/parse.y"
{
  let random = self.yy_move(-1).yy222();
  let rowid = self.yy_move(0).yy222();
  if "random".eq_ignore_ascii_case(&random.0) && "rowid".eq_ignore_ascii_case(&rowid.0) {
    yylhsminor = YYMINORTYPE::yy201( TableOptions::RANDOM_ROWID);
  }else{
    // yylhsminor = YYMINORTYPE::yy201( TableOptions::NONE);
    let msg = format!("unknown table option: {random} {rowid}");
    self.ctx.sqlite3_error_msg(&msg);
    return Err(ParserError::Custom(msg));
  }
}
  self[-1].minor = yylhsminor;
        }
      28 /* table_option ::= nm */
     => {
//line 166 "src/parser/parse.y"
{
  let name = self.yy_move(0).yy222();
  if "strict".eq_ignore_ascii_case(&name.0) {
    yylhsminor = YYMINORTYPE::yy201( TableOptions::STRICT);
  }else{
    // yylhsminor = YYMINORTYPE::yy201( TableOptions::NONE);
    let msg = format!("unknown table option: {name}");
    self.ctx.sqlite3_error_msg(&msg);
    return Err(ParserError::Custom(msg));
  }
}
  self[0].minor = yylhsminor;
        }
      29 /* columnlist ::= columnlist COMMA columnname carglist */
     => {
//line 178 "src/parser/parse.y"
{
  let col = self.yy_move(-1).yy444();
  let cd = ColumnDefinition{ col_name: col.0, col_type: col.1, constraints: self.yy_move(0).yy305() };
  ColumnDefinition::add_column(self[-3].yy208_ref(), cd)?;
}
        }
      30 /* columnlist ::= columnname carglist */
     => {
//line 183 "src/parser/parse.y"
{
  let col = self.yy_move(-1).yy444();
  yylhsminor = YYMINORTYPE::yy208( vec![ColumnDefinition{ col_name: col.0, col_type: col.1, constraints: self.yy_move(0).yy305() }]);
}
  self[-1].minor = yylhsminor;
        }
      31 /* columnname ::= nm typetoken */
     => {
//line 188 "src/parser/parse.y"
{yylhsminor = YYMINORTYPE::yy444( (self.yy_move(-1).yy222(), self.yy_move(0).yy362()));}
  self[-1].minor = yylhsminor;
        }
      32 /* nm ::= ID|INDEXED|JOIN_KW */
     | 33 /* nm ::= STRING */
     => {
//line 269 "src/parser/parse.y"
{ yylhsminor = YYMINORTYPE::yy222( Name::from_token(self[0].major, self.yy_move(0).yy0())); }
  self[0].minor = yylhsminor;
        }
      34 /* typetoken ::= */
     => {
//line 277 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy362( None);}
        }
      35 /* typetoken ::= typename */
     => {
//line 278 "src/parser/parse.y"
{yylhsminor = YYMINORTYPE::yy362( Some(Type{ name: self.yy_move(0).yy220(), size: None }));}
  self[0].minor = yylhsminor;
        }
      36 /* typetoken ::= typename LP signed RP */
     => {
//line 279 "src/parser/parse.y"
{
  yylhsminor = YYMINORTYPE::yy362( Some(Type{ name: self.yy_move(-3).yy220(), size: Some(TypeSize::MaxSize(Box::new(self.yy_move(-1).yy480()))) }));
}
  self[-3].minor = yylhsminor;
        }
      37 /* typetoken ::= typename LP signed COMMA signed RP */
     => {
//line 282 "src/parser/parse.y"
{
  yylhsminor = YYMINORTYPE::yy362( Some(Type{ name: self.yy_move(-5).yy220(), size: Some(TypeSize::TypeSize(Box::new(self.yy_move(-3).yy480()), Box::new(self.yy_move(-1).yy480()))) }));
}
  self[-5].minor = yylhsminor;
        }
      38 /* typename ::= ID|STRING */
     => {
//line 286 "src/parser/parse.y"
{yylhsminor= YYMINORTYPE::yy220(from_token(self[0].major, self.yy_move(0).yy0()));}
  self[0].minor = yylhsminor;
        }
      39 /* typename ::= typename ID|STRING */
     => {
//line 287 "src/parser/parse.y"
{let ids=from_token(self[0].major, self.yy_move(0).yy0()); self[-1].yy220_ref().push(' '); self[-1].yy220_ref().push_str(&ids);}
        }
      40 /* carglist ::= carglist ccons */
     => {
//line 310 "src/parser/parse.y"
{if self.ctx.no_constraint_name() { let cc = self.yy_move(0).yy277(); self[-1].yy305_ref().push(cc); }}
        }
      41 /* carglist ::= */
     => {
//line 311 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy305( vec![]);}
        }
      42 /* ccons ::= CONSTRAINT nm */
     => {
//line 313 "src/parser/parse.y"
{ self.ctx.constraint_name = Some(self.yy_move(0).yy222());}
        }
      43 /* ccons ::= DEFAULT term */
     => {
//line 314 "src/parser/parse.y"
{
  let name = self.ctx.constraint_name();
  let constraint = ColumnConstraint::Default(self.yy_move(0).yy480());
  self[-1] .minor= YYMINORTYPE::yy277( NamedColumnConstraint{ name, constraint });
}
        }
      44 /* ccons ::= DEFAULT LP expr RP */
     => {
//line 319 "src/parser/parse.y"
{
  let name = self.ctx.constraint_name();
  let constraint = ColumnConstraint::Default(Expr::parenthesized(self.yy_move(-1).yy480()));
  self[-3] .minor= YYMINORTYPE::yy277( NamedColumnConstraint{ name, constraint });
}
        }
      45 /* ccons ::= DEFAULT PLUS term */
     => {
//line 324 "src/parser/parse.y"
{
  let name = self.ctx.constraint_name();
  let constraint = ColumnConstraint::Default(Expr::Unary(UnaryOperator::Positive, Box::new(self.yy_move(0).yy480())));
  self[-2] .minor= YYMINORTYPE::yy277( NamedColumnConstraint{ name, constraint });
}
        }
      46 /* ccons ::= DEFAULT MINUS term */
     => {
//line 329 "src/parser/parse.y"
{
  let name = self.ctx.constraint_name();
  let constraint = ColumnConstraint::Default(Expr::Unary(UnaryOperator::Negative, Box::new(self.yy_move(0).yy480())));
  self[-2] .minor= YYMINORTYPE::yy277( NamedColumnConstraint{ name, constraint });
}
        }
      47 /* ccons ::= DEFAULT ID|INDEXED */
     => {
//line 334 "src/parser/parse.y"
{
  let name = self.ctx.constraint_name();
  let constraint = ColumnConstraint::Default(Expr::id(self[0].major, self.yy_move(0).yy0()));
  self[-1] .minor= YYMINORTYPE::yy277( NamedColumnConstraint{ name, constraint });
}
        }
      48 /* ccons ::= NULL onconf */
     => {
//line 343 "src/parser/parse.y"
{
  let name = self.ctx.constraint_name();
  let constraint = ColumnConstraint::NotNull{ nullable: true, conflict_clause: self.yy_move(0).yy254()};
  self[-1] .minor= YYMINORTYPE::yy277( NamedColumnConstraint{ name, constraint });
}
        }
      49 /* ccons ::= NOT NULL onconf */
     => {
//line 348 "src/parser/parse.y"
{
  let name = self.ctx.constraint_name();
  let constraint = ColumnConstraint::NotNull{ nullable: false, conflict_clause: self.yy_move(0).yy254()};
  self[-2] .minor= YYMINORTYPE::yy277( NamedColumnConstraint{ name, constraint });
}
        }
      50 /* ccons ::= PRIMARY KEY sortorder onconf autoinc */
     => {
//line 353 "src/parser/parse.y"
{
  let name = self.ctx.constraint_name();
  let constraint = ColumnConstraint::PrimaryKey{ order: self.yy_move(-2).yy296(), conflict_clause: self.yy_move(-1).yy254(), auto_increment: self.yy_move(0).yy173() };
  self[-4] .minor= YYMINORTYPE::yy277( NamedColumnConstraint{ name, constraint });
}
        }
      51 /* ccons ::= UNIQUE onconf */
     => {
//line 358 "src/parser/parse.y"
{
  let name = self.ctx.constraint_name();
  let constraint = ColumnConstraint::Unique(self.yy_move(0).yy254());
  self[-1] .minor= YYMINORTYPE::yy277( NamedColumnConstraint{ name, constraint });
}
        }
      52 /* ccons ::= CHECK LP expr RP */
     => {
//line 363 "src/parser/parse.y"
{
  let name = self.ctx.constraint_name();
  let constraint = ColumnConstraint::Check(self.yy_move(-1).yy480());
  self[-3] .minor= YYMINORTYPE::yy277( NamedColumnConstraint{ name, constraint });
}
        }
      53 /* ccons ::= REFERENCES nm eidlist_opt refargs */
     => {
//line 368 "src/parser/parse.y"
{
  let name = self.ctx.constraint_name();
  let clause = ForeignKeyClause{ tbl_name: self.yy_move(-2).yy222(), columns: self.yy_move(-1).yy143(), args: self.yy_move(0).yy404() };
  let constraint = ColumnConstraint::ForeignKey{ clause, deref_clause: None }; // FIXME deref_clause
  self[-3] .minor= YYMINORTYPE::yy277( NamedColumnConstraint{ name, constraint });
}
        }
      54 /* ccons ::= defer_subclause */
     => {
//line 374 "src/parser/parse.y"
{
  let constraint = ColumnConstraint::Defer(self.yy_move(0).yy394());
  yylhsminor = YYMINORTYPE::yy277( NamedColumnConstraint{ name: None, constraint });
}
  self[0].minor = yylhsminor;
        }
      55 /* ccons ::= COLLATE ID|STRING */
     => {
//line 378 "src/parser/parse.y"
{
  let name = self.ctx.constraint_name();
  let constraint = ColumnConstraint::Collate{ collation_name: Name::from_token(self[0].major, self.yy_move(0).yy0()) };
  self[-1] .minor= YYMINORTYPE::yy277( NamedColumnConstraint{ name, constraint });
}
        }
      56 /* ccons ::= GENERATED ALWAYS AS generated */
     => {
//line 383 "src/parser/parse.y"
{
  let name = self.ctx.constraint_name();
  let constraint = self.yy_move(0).yy84();
  self[-3] .minor= YYMINORTYPE::yy277( NamedColumnConstraint{ name, constraint });
}
        }
      57 /* ccons ::= AS generated */
     => {
//line 388 "src/parser/parse.y"
{
  let name = self.ctx.constraint_name();
  let constraint = self.yy_move(0).yy84();
  self[-1] .minor= YYMINORTYPE::yy277( NamedColumnConstraint{ name, constraint });
}
        }
      58 /* generated ::= LP expr RP */
     => {
//line 394 "src/parser/parse.y"
{
  self[-2] .minor= YYMINORTYPE::yy84( ColumnConstraint::Generated{ expr: self.yy_move(-1).yy480(), typ: None });
}
        }
      59 /* generated ::= LP expr RP ID */
     => {
//line 397 "src/parser/parse.y"
{
  self[-3] .minor= YYMINORTYPE::yy84( ColumnConstraint::Generated{ expr: self.yy_move(-2).yy480(), typ: Some(Id::from_token(self[0].major, self.yy_move(0).yy0())) });
}
        }
      62 /* refargs ::= */
     => {
//line 412 "src/parser/parse.y"
{ self[1] .minor= YYMINORTYPE::yy404( vec![]); /* EV: R-19803-45884 */}
        }
      63 /* refargs ::= refargs refarg */
     => {
//line 413 "src/parser/parse.y"
{ let ra = self.yy_move(0).yy432(); self[-1].yy404_ref().push(ra); }
        }
      64 /* refarg ::= MATCH nm */
     => {
//line 415 "src/parser/parse.y"
{ self[-1] .minor= YYMINORTYPE::yy432( RefArg::Match(self.yy_move(0).yy222())); }
        }
      65 /* refarg ::= ON INSERT refact */
     => {
//line 416 "src/parser/parse.y"
{ self[-2] .minor= YYMINORTYPE::yy432( RefArg::OnInsert(self.yy_move(0).yy238())); }
        }
      66 /* refarg ::= ON DELETE refact */
     => {
//line 417 "src/parser/parse.y"
{ self[-2] .minor= YYMINORTYPE::yy432( RefArg::OnDelete(self.yy_move(0).yy238())); }
        }
      67 /* refarg ::= ON UPDATE refact */
     => {
//line 418 "src/parser/parse.y"
{ self[-2] .minor= YYMINORTYPE::yy432( RefArg::OnUpdate(self.yy_move(0).yy238())); }
        }
      68 /* refact ::= SET NULL */
     => {
//line 420 "src/parser/parse.y"
{ self[-1] .minor= YYMINORTYPE::yy238( RefAct::SetNull);  /* EV: R-33326-45252 */}
        }
      69 /* refact ::= SET DEFAULT */
     => {
//line 421 "src/parser/parse.y"
{ self[-1] .minor= YYMINORTYPE::yy238( RefAct::SetDefault);  /* EV: R-33326-45252 */}
        }
      70 /* refact ::= CASCADE */
     => {
//line 422 "src/parser/parse.y"
{ self[0] .minor= YYMINORTYPE::yy238( RefAct::Cascade);  /* EV: R-33326-45252 */}
        }
      71 /* refact ::= RESTRICT */
     => {
//line 423 "src/parser/parse.y"
{ self[0] .minor= YYMINORTYPE::yy238( RefAct::Restrict); /* EV: R-33326-45252 */}
        }
      72 /* refact ::= NO ACTION */
     => {
//line 424 "src/parser/parse.y"
{ self[-1] .minor= YYMINORTYPE::yy238( RefAct::NoAction);     /* EV: R-33326-45252 */}
        }
      73 /* defer_subclause ::= NOT DEFERRABLE init_deferred_pred_opt */
     => {
//line 426 "src/parser/parse.y"
{self[-2] .minor= YYMINORTYPE::yy394( DeferSubclause{ deferrable: false, init_deferred: self.yy_move(0).yy300() });}
        }
      74 /* defer_subclause ::= DEFERRABLE init_deferred_pred_opt */
     => {
//line 427 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy394( DeferSubclause{ deferrable: true, init_deferred: self.yy_move(0).yy300() });}
        }
      75 /* init_deferred_pred_opt ::= */
     => {
//line 429 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy300( None);}
        }
      76 /* init_deferred_pred_opt ::= INITIALLY DEFERRED */
     => {
//line 430 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy300( Some(InitDeferredPred::InitiallyDeferred));}
        }
      77 /* init_deferred_pred_opt ::= INITIALLY IMMEDIATE */
     => {
//line 431 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy300( Some(InitDeferredPred::InitiallyImmediate));}
        }
      78 /* conslist_opt ::= */
     => {
//line 434 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy182( None);}
        }
      79 /* conslist_opt ::= COMMA conslist */
     => {
//line 435 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy182( Some(self.yy_move(0).yy559()));}
        }
      80 /* conslist ::= conslist tconscomma tcons */
     => {
//line 437 "src/parser/parse.y"
{if self.ctx.no_constraint_name() { let tc = self.yy_move(0).yy439(); self[-2].yy559_ref().push(tc); }}
        }
      81 /* conslist ::= tcons */
     => {
//line 438 "src/parser/parse.y"
{if self.ctx.no_constraint_name() { let tc = self.yy_move(0).yy439(); yylhsminor = YYMINORTYPE::yy559( vec![tc]); } else { yylhsminor = YYMINORTYPE::yy559( vec![]); }}
  self[0].minor = yylhsminor;
        }
      82 /* tconscomma ::= COMMA */
     => {
//line 439 "src/parser/parse.y"
{ self.ctx.constraint_name = None;}
        }
      83 /* tcons ::= CONSTRAINT nm */
     => {
//line 442 "src/parser/parse.y"
{ self.ctx.constraint_name = Some(self.yy_move(0).yy222())}
        }
      84 /* tcons ::= PRIMARY KEY LP sortlist autoinc RP onconf */
     => {
//line 443 "src/parser/parse.y"
{
  let name = self.ctx.constraint_name();
  let constraint = TableConstraint::PrimaryKey{ columns: self.yy_move(-3).yy536(), auto_increment: self.yy_move(-2).yy173(), conflict_clause: self.yy_move(0).yy254() };
  self[-6] .minor= YYMINORTYPE::yy439( NamedTableConstraint{ name, constraint });
}
        }
      85 /* tcons ::= UNIQUE LP sortlist RP onconf */
     => {
//line 448 "src/parser/parse.y"
{
  let name = self.ctx.constraint_name();
  let constraint = TableConstraint::Unique{ columns: self.yy_move(-2).yy536(), conflict_clause: self.yy_move(0).yy254() };
  self[-4] .minor= YYMINORTYPE::yy439( NamedTableConstraint{ name, constraint });
}
        }
      86 /* tcons ::= CHECK LP expr RP onconf */
     => {
//line 453 "src/parser/parse.y"
{
  let name = self.ctx.constraint_name();
  let constraint = TableConstraint::Check(self.yy_move(-2).yy480());
  self[-4] .minor= YYMINORTYPE::yy439( NamedTableConstraint{ name, constraint });
}
        }
      87 /* tcons ::= FOREIGN KEY LP eidlist RP REFERENCES nm eidlist_opt refargs defer_subclause_opt */
     => {
//line 459 "src/parser/parse.y"
{
  let name = self.ctx.constraint_name();
  let clause = ForeignKeyClause{ tbl_name: self.yy_move(-3).yy222(), columns: self.yy_move(-2).yy143(), args: self.yy_move(-1).yy404() };
  let constraint = TableConstraint::ForeignKey{ columns: self.yy_move(-6).yy360(), clause, deref_clause: self.yy_move(0).yy85() };
  self[-9] .minor= YYMINORTYPE::yy439( NamedTableConstraint{ name, constraint });
}
        }
      88 /* defer_subclause_opt ::= */
     => {
//line 466 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy85( None);}
        }
      89 /* defer_subclause_opt ::= defer_subclause */
     => {
//line 467 "src/parser/parse.y"
{yylhsminor = YYMINORTYPE::yy85( Some(self.yy_move(0).yy394()));}
  self[0].minor = yylhsminor;
        }
      90 /* onconf ::= */
     | 92 /* orconf ::= */
     => {
//line 475 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy254( None);}
        }
      91 /* onconf ::= ON CONFLICT resolvetype */
     => {
//line 476 "src/parser/parse.y"
{self[-2] .minor= YYMINORTYPE::yy254( Some(self.yy_move(0).yy207()));}
        }
      93 /* orconf ::= OR resolvetype */
     => {
//line 478 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy254( Some(self.yy_move(0).yy207()));}
        }
      94 /* resolvetype ::= IGNORE */
     => {
//line 480 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy207( ResolveType::Ignore);}
        }
      95 /* resolvetype ::= REPLACE */
     => {
//line 481 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy207( ResolveType::Replace);}
        }
      96 /* cmd ::= DROP TABLE ifexists fullname */
     => {
//line 485 "src/parser/parse.y"
{
  self.ctx.stmt = Some(Stmt::DropTable{ if_exists: self.yy_move(-1).yy173(), tbl_name: self.yy_move(0).yy474()});
}
        }
      97 /* ifexists ::= IF EXISTS */
     | 237 /* between_op ::= NOT BETWEEN */
     | 240 /* in_op ::= NOT IN */
     => {
//line 489 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy173( true);}
        }
      99 /* cmd ::= createkw temp VIEW ifnotexists fullname eidlist_opt AS select */
     => {
//line 496 "src/parser/parse.y"
{
  self.ctx.stmt = Some(Stmt::CreateView{ temporary: self.yy_move(-6).yy173(), if_not_exists: self.yy_move(-4).yy173(), view_name: self.yy_move(-3).yy474(), columns: self.yy_move(-2).yy143(),
                                         select: self.yy_move(0).yy213() });
}
        }
      100 /* cmd ::= DROP VIEW ifexists fullname */
     => {
//line 500 "src/parser/parse.y"
{
  self.ctx.stmt = Some(Stmt::DropView{ if_exists: self.yy_move(-1).yy173(), view_name: self.yy_move(0).yy474() });
}
        }
      101 /* cmd ::= select */
     => {
//line 507 "src/parser/parse.y"
{
  self.ctx.stmt = Some(Stmt::Select(self.yy_move(0).yy213()));
}
        }
      102 /* select ::= WITH wqlist selectnowith orderby_opt limit_opt */
     => {
//line 519 "src/parser/parse.y"
{
  self[-4] .minor= YYMINORTYPE::yy213( Select{ with: Some(With { recursive: false, ctes: self.yy_move(-3).yy97() }), body: self.yy_move(-2).yy503(), order_by: self.yy_move(-1).yy19(), limit: self.yy_move(0).yy331() });
}
        }
      103 /* select ::= WITH RECURSIVE wqlist selectnowith orderby_opt limit_opt */
     => {
//line 522 "src/parser/parse.y"
{
  self[-5] .minor= YYMINORTYPE::yy213( Select{ with: Some(With { recursive: true, ctes: self.yy_move(-3).yy97() }), body: self.yy_move(-2).yy503(), order_by: self.yy_move(-1).yy19(), limit: self.yy_move(0).yy331() });
}
        }
      104 /* select ::= selectnowith orderby_opt limit_opt */
     => {
//line 526 "src/parser/parse.y"
{
  self[-2] .minor= YYMINORTYPE::yy213( Select{ with: None, body: self.yy_move(-2).yy503(), order_by: self.yy_move(-1).yy19(), limit: self.yy_move(0).yy331() }); /*A-overwrites-X*/
}
        }
      105 /* selectnowith ::= oneselect */
     => {
//line 530 "src/parser/parse.y"
{
  yylhsminor = YYMINORTYPE::yy503( SelectBody{ select: self.yy_move(0).yy463(), compounds: None });
}
  self[0].minor = yylhsminor;
        }
      106 /* selectnowith ::= selectnowith multiselect_op oneselect */
     => {
//line 534 "src/parser/parse.y"
{
  let cs = CompoundSelect{ operator: self.yy_move(-1).yy166(), select: self.yy_move(0).yy463() };
  self[-2].yy503_ref().push(cs);
}
        }
      107 /* multiselect_op ::= UNION */
     => {
//line 539 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy166( CompoundOperator::Union);}
        }
      108 /* multiselect_op ::= UNION ALL */
     => {
//line 540 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy166( CompoundOperator::UnionAll);}
        }
      109 /* multiselect_op ::= EXCEPT */
     => {
//line 541 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy166( CompoundOperator::Except);}
        }
      110 /* multiselect_op ::= INTERSECT */
     => {
//line 542 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy166( CompoundOperator::Intersect);}
        }
      111 /* oneselect ::= SELECT distinct selcollist from where_opt groupby_opt */
     => {
//line 546 "src/parser/parse.y"
{
  self[-5] .minor= YYMINORTYPE::yy463( OneSelect::Select{ distinctness: self.yy_move(-4).yy415(), columns: self.yy_move(-3).yy106(), from: self.yy_move(-2).yy485(), where_clause: self.yy_move(-1).yy479(),
                         group_by: self.yy_move(0).yy68(), window_clause: None });
    }
        }
      112 /* oneselect ::= SELECT distinct selcollist from where_opt groupby_opt window_clause */
     => {
//line 552 "src/parser/parse.y"
{
  self[-6] .minor= YYMINORTYPE::yy463( OneSelect::Select{ distinctness: self.yy_move(-5).yy415(), columns: self.yy_move(-4).yy106(), from: self.yy_move(-3).yy485(), where_clause: self.yy_move(-2).yy479(),
                         group_by: self.yy_move(-1).yy68(), window_clause: Some(self.yy_move(0).yy472()) });
}
        }
      113 /* oneselect ::= values */
     => {
//line 559 "src/parser/parse.y"
{ yylhsminor = YYMINORTYPE::yy463( OneSelect::Values(self.yy_move(0).yy468())); }
  self[0].minor = yylhsminor;
        }
      114 /* values ::= VALUES LP nexprlist RP */
     => {
//line 562 "src/parser/parse.y"
{
  self[-3] .minor= YYMINORTYPE::yy468( vec![self.yy_move(-1).yy312()]);
}
        }
      115 /* values ::= values COMMA LP nexprlist RP */
     => {
//line 565 "src/parser/parse.y"
{
  let exprs = self.yy_move(-1).yy312();
  self[-4].yy468_ref().push(exprs);
}
        }
      116 /* distinct ::= DISTINCT */
     => {
//line 574 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy415( Some(Distinctness::Distinct));}
        }
      117 /* distinct ::= ALL */
     => {
//line 575 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy415( Some(Distinctness::All));}
        }
      118 /* distinct ::= */
     => {
//line 576 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy415( None);}
        }
      119 /* sclp ::= */
     => {
//line 586 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy106( Vec::<ResultColumn>::new());}
        }
      120 /* selcollist ::= sclp expr as */
     => {
//line 587 "src/parser/parse.y"
{
  let rc = ResultColumn::Expr(self.yy_move(-1).yy480(), self.yy_move(0).yy364());
  self[-2].yy106_ref().push(rc);
}
        }
      121 /* selcollist ::= sclp STAR */
     => {
//line 591 "src/parser/parse.y"
{
  let rc = ResultColumn::Star;
  self[-1].yy106_ref().push(rc);
}
        }
      122 /* selcollist ::= sclp nm DOT STAR */
     => {
//line 595 "src/parser/parse.y"
{
  let rc = ResultColumn::TableStar(self.yy_move(-2).yy222());
  self[-3].yy106_ref().push(rc);
}
        }
      123 /* as ::= AS nm */
     => {
//line 604 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy364( Some(As::As(self.yy_move(0).yy222())));}
        }
      124 /* as ::= ID|STRING */
     => {
//line 605 "src/parser/parse.y"
{yylhsminor = YYMINORTYPE::yy364( Some(As::Elided(Name::from_token(self[0].major, self.yy_move(0).yy0()))));}
  self[0].minor = yylhsminor;
        }
      125 /* as ::= */
     => {
//line 606 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy364( None);}
        }
      126 /* from ::= */
     => {
//line 615 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy485( None);}
        }
      127 /* from ::= FROM seltablist */
     => {
//line 616 "src/parser/parse.y"
{
  self[-1] .minor= YYMINORTYPE::yy485( Some(self.yy_move(0).yy314()));
}
        }
      128 /* stl_prefix ::= seltablist joinop */
     => {
//line 623 "src/parser/parse.y"
{
   let op = self.yy_move(0).yy577();
   self[-1].yy314_ref().push_op(op);
}
        }
      129 /* stl_prefix ::= */
     => {
//line 627 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy314( FromClause::empty());}
        }
      130 /* seltablist ::= stl_prefix fullname as indexed_opt on_using */
     => {
//line 629 "src/parser/parse.y"
{
    let st = SelectTable::Table(self.yy_move(-3).yy474(), self.yy_move(-2).yy364(), self.yy_move(-1).yy181());
    let jc = self.yy_move(0).yy578();
    self[-4].yy314_ref().push(st, jc)?;
}
        }
      131 /* seltablist ::= stl_prefix fullname LP exprlist RP as on_using */
     => {
//line 635 "src/parser/parse.y"
{
    let st = SelectTable::TableCall(self.yy_move(-5).yy474(), self.yy_move(-3).yy419(), self.yy_move(-1).yy364());
    let jc = self.yy_move(0).yy578();
    self[-6].yy314_ref().push(st, jc)?;
}
        }
      132 /* seltablist ::= stl_prefix LP select RP as on_using */
     => {
//line 642 "src/parser/parse.y"
{
    let st = SelectTable::Select(self.yy_move(-3).yy213(), self.yy_move(-1).yy364());
    let jc = self.yy_move(0).yy578();
    self[-5].yy314_ref().push(st, jc)?;
  }
        }
      133 /* seltablist ::= stl_prefix LP seltablist RP as on_using */
     => {
//line 648 "src/parser/parse.y"
{
    let st = SelectTable::Sub(self.yy_move(-3).yy314(), self.yy_move(-1).yy364());
    let jc = self.yy_move(0).yy578();
    self[-5].yy314_ref().push(st, jc)?;
  }
        }
      134 /* fullname ::= nm */
     => {
//line 656 "src/parser/parse.y"
{
  yylhsminor = YYMINORTYPE::yy474( QualifiedName::single(self.yy_move(0).yy222()));
}
  self[0].minor = yylhsminor;
        }
      135 /* fullname ::= nm DOT nm */
     => {
//line 659 "src/parser/parse.y"
{
  yylhsminor = YYMINORTYPE::yy474( QualifiedName::fullname(self.yy_move(-2).yy222(), self.yy_move(0).yy222()));
}
  self[-2].minor = yylhsminor;
        }
      136 /* xfullname ::= nm */
     => {
//line 665 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy474( QualifiedName::single(self.yy_move(0).yy222())); /*A-overwrites-X*/}
        }
      137 /* xfullname ::= nm DOT nm */
     => {
//line 667 "src/parser/parse.y"
{self[-2] .minor= YYMINORTYPE::yy474( QualifiedName::fullname(self.yy_move(-2).yy222(), self.yy_move(0).yy222())); /*A-overwrites-X*/}
        }
      138 /* xfullname ::= nm DOT nm AS nm */
     => {
//line 668 "src/parser/parse.y"
{
   self[-4] .minor= YYMINORTYPE::yy474( QualifiedName::xfullname(self.yy_move(-4).yy222(), self.yy_move(-2).yy222(), self.yy_move(0).yy222())); /*A-overwrites-X*/
}
        }
      139 /* xfullname ::= nm AS nm */
     => {
//line 671 "src/parser/parse.y"
{
   self[-2] .minor= YYMINORTYPE::yy474( QualifiedName::alias(self.yy_move(-2).yy222(), self.yy_move(0).yy222())); /*A-overwrites-X*/
}
        }
      140 /* joinop ::= COMMA */
     => {
//line 676 "src/parser/parse.y"
{ self[0] .minor= YYMINORTYPE::yy577( JoinOperator::Comma); }
        }
      141 /* joinop ::= JOIN */
     => {
//line 677 "src/parser/parse.y"
{ self[0] .minor= YYMINORTYPE::yy577( JoinOperator::TypedJoin{ natural: false, join_type: None }); }
        }
      142 /* joinop ::= JOIN_KW JOIN */
     => {
//line 679 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy577( JoinOperator::from_single(self.yy_move(-1).yy0())?);  /*X-overwrites-A*/}
        }
      143 /* joinop ::= JOIN_KW nm JOIN */
     => {
//line 681 "src/parser/parse.y"
{self[-2] .minor= YYMINORTYPE::yy577( JoinOperator::from_couple(self.yy_move(-2).yy0(), self.yy_move(-1).yy222())?); /*X-overwrites-A*/}
        }
      144 /* joinop ::= JOIN_KW nm nm JOIN */
     => {
//line 683 "src/parser/parse.y"
{self[-3] .minor= YYMINORTYPE::yy577( JoinOperator::from_triple(self.yy_move(-3).yy0(), self.yy_move(-2).yy222(), self.yy_move(-1).yy222())?);/*X-overwrites-A*/}
        }
      145 /* on_using ::= ON expr */
     => {
//line 703 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy578( Some(JoinConstraint::On(self.yy_move(0).yy480())));}
        }
      146 /* on_using ::= USING LP idlist RP */
     => {
//line 704 "src/parser/parse.y"
{self[-3] .minor= YYMINORTYPE::yy578( Some(JoinConstraint::Using(self.yy_move(-1).yy162())));}
        }
      147 /* on_using ::= */
     => {
//line 705 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy578( None);}
        }
      148 /* indexed_opt ::= */
     => {
//line 718 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy181( None);}
        }
      149 /* indexed_opt ::= INDEXED BY nm */
     => {
//line 719 "src/parser/parse.y"
{self[-2] .minor= YYMINORTYPE::yy181( Some(Indexed::IndexedBy(self.yy_move(0).yy222())));}
        }
      150 /* indexed_opt ::= NOT INDEXED */
     => {
//line 720 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy181( Some(Indexed::NotIndexed));}
        }
      151 /* orderby_opt ::= */
     => {
//line 730 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy19( None);}
        }
      152 /* orderby_opt ::= ORDER BY sortlist */
     => {
//line 731 "src/parser/parse.y"
{self[-2] .minor= YYMINORTYPE::yy19( Some(self.yy_move(0).yy536()));}
        }
      153 /* sortlist ::= sortlist COMMA expr sortorder nulls */
     => {
//line 732 "src/parser/parse.y"
{
  let sc = SortedColumn { expr: self.yy_move(-2).yy480(), order: self.yy_move(-1).yy296(), nulls: self.yy_move(0).yy322() };
  self[-4].yy536_ref().push(sc);
}
        }
      154 /* sortlist ::= expr sortorder nulls */
     => {
//line 736 "src/parser/parse.y"
{
  self[-2] .minor= YYMINORTYPE::yy536( vec![SortedColumn { expr: self.yy_move(-2).yy480(), order: self.yy_move(-1).yy296(), nulls: self.yy_move(0).yy322() }]); /*A-overwrites-Y*/
}
        }
      155 /* sortorder ::= ASC */
     => {
//line 742 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy296( Some(SortOrder::Asc));}
        }
      156 /* sortorder ::= DESC */
     => {
//line 743 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy296( Some(SortOrder::Desc));}
        }
      157 /* sortorder ::= */
     => {
//line 744 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy296( None);}
        }
      158 /* nulls ::= NULLS FIRST */
     => {
//line 747 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy322( Some(NullsOrder::First));}
        }
      159 /* nulls ::= NULLS LAST */
     => {
//line 748 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy322( Some(NullsOrder::Last));}
        }
      160 /* nulls ::= */
     => {
//line 749 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy322( None);}
        }
      161 /* groupby_opt ::= */
     => {
//line 752 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy68( None);}
        }
      162 /* groupby_opt ::= GROUP BY nexprlist having_opt */
     => {
//line 753 "src/parser/parse.y"
{self[-3] .minor= YYMINORTYPE::yy68( Some(GroupBy{ exprs: self.yy_move(-1).yy312(), having: self.yy_move(0).yy479() }));}
        }
      163 /* having_opt ::= */
     | 170 /* where_opt ::= */
     | 250 /* case_else ::= */
     | 252 /* case_operand ::= */
     | 272 /* vinto ::= */
     => {
//line 756 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy479( None);}
        }
      164 /* having_opt ::= HAVING expr */
     | 171 /* where_opt ::= WHERE expr */
     | 249 /* case_else ::= ELSE expr */
     | 271 /* vinto ::= INTO expr */
     => {
//line 757 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy479( Some(self.yy_move(0).yy480()));}
        }
      165 /* limit_opt ::= */
     => {
//line 769 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy331( None);}
        }
      166 /* limit_opt ::= LIMIT expr */
     => {
//line 771 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy331( Some(Limit{ expr: self.yy_move(0).yy480(), offset: None }));}
        }
      167 /* limit_opt ::= LIMIT expr OFFSET expr */
     | 168 /* limit_opt ::= LIMIT expr COMMA expr */
     => {
//line 773 "src/parser/parse.y"
{self[-3] .minor= YYMINORTYPE::yy331( Some(Limit{ expr: self.yy_move(-2).yy480(), offset: Some(self.yy_move(0).yy480()) }));}
        }
      169 /* cmd ::= with DELETE FROM xfullname indexed_opt where_opt_ret orderby_opt limit_opt */
     => {
//line 781 "src/parser/parse.y"
{
  let (where_clause, returning) = self.yy_move(-2).yy496();
  self.ctx.stmt = Some(Stmt::Delete{ with: self.yy_move(-7).yy280(), tbl_name: self.yy_move(-4).yy474(), indexed: self.yy_move(-3).yy181(), where_clause, returning,
                                     order_by: self.yy_move(-1).yy19(), limit: self.yy_move(0).yy331() });
}
        }
      172 /* where_opt_ret ::= */
     => {
//line 799 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy496( (None, None));}
        }
      173 /* where_opt_ret ::= WHERE expr */
     => {
//line 800 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy496( (Some(self.yy_move(0).yy480()), None));}
        }
      174 /* where_opt_ret ::= RETURNING selcollist */
     => {
//line 802 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy496( (None, Some(self.yy_move(0).yy106())));}
        }
      175 /* where_opt_ret ::= WHERE expr RETURNING selcollist */
     => {
//line 804 "src/parser/parse.y"
{self[-3] .minor= YYMINORTYPE::yy496( (Some(self.yy_move(-2).yy480()), Some(self.yy_move(0).yy106())));}
        }
      176 /* cmd ::= with UPDATE orconf xfullname indexed_opt SET setlist from where_opt_ret orderby_opt limit_opt */
     => {
//line 810 "src/parser/parse.y"
{
  let (where_clause, returning) = self.yy_move(-2).yy496();
  self.ctx.stmt = Some(Stmt::Update { with: self.yy_move(-10).yy280(), or_conflict: self.yy_move(-8).yy254(), tbl_name: self.yy_move(-7).yy474(), indexed: self.yy_move(-6).yy181(), sets: self.yy_move(-4).yy517(), from: self.yy_move(-3).yy485(),
                                      where_clause, returning, order_by: self.yy_move(-1).yy19(), limit: self.yy_move(0).yy331() });
}
        }
      177 /* setlist ::= setlist COMMA nm EQ expr */
     => {
//line 828 "src/parser/parse.y"
{
  let s = Set{ col_names: vec![self.yy_move(-2).yy222()], expr: self.yy_move(0).yy480() };
  self[-4].yy517_ref().push(s);
}
        }
      178 /* setlist ::= setlist COMMA LP idlist RP EQ expr */
     => {
//line 832 "src/parser/parse.y"
{
  let s = Set{ col_names: self.yy_move(-3).yy162(), expr: self.yy_move(0).yy480() };
  self[-6].yy517_ref().push(s);
}
        }
      179 /* setlist ::= nm EQ expr */
     => {
//line 836 "src/parser/parse.y"
{
  yylhsminor = YYMINORTYPE::yy517( vec![Set{ col_names: vec![self.yy_move(-2).yy222()], expr: self.yy_move(0).yy480() }]);
}
  self[-2].minor = yylhsminor;
        }
      180 /* setlist ::= LP idlist RP EQ expr */
     => {
//line 839 "src/parser/parse.y"
{
  self[-4] .minor= YYMINORTYPE::yy517( vec![Set{ col_names: self.yy_move(-3).yy162(), expr: self.yy_move(0).yy480() }]);
}
        }
      181 /* cmd ::= with insert_cmd INTO xfullname idlist_opt select upsert */
     => {
//line 846 "src/parser/parse.y"
{
  let (upsert, returning) = self.yy_move(0).yy504();
  let body = InsertBody::Select(self.yy_move(-1).yy213(), upsert);
  self.ctx.stmt = Some(Stmt::Insert{ with: self.yy_move(-6).yy280(), or_conflict: self.yy_move(-5).yy254(), tbl_name: self.yy_move(-3).yy474(), columns: self.yy_move(-2).yy25(),
                                     body, returning });
}
        }
      182 /* cmd ::= with insert_cmd INTO xfullname idlist_opt DEFAULT VALUES returning */
     => {
//line 853 "src/parser/parse.y"
{
  let body = InsertBody::DefaultValues;
  self.ctx.stmt = Some(Stmt::Insert{ with: self.yy_move(-7).yy280(), or_conflict: self.yy_move(-6).yy254(), tbl_name: self.yy_move(-4).yy474(), columns: self.yy_move(-3).yy25(),
                                     body, returning: self.yy_move(0).yy265() });
}
        }
      183 /* upsert ::= */
     => {
//line 866 "src/parser/parse.y"
{ self[1] .minor= YYMINORTYPE::yy504( (None, None)); }
        }
      184 /* upsert ::= RETURNING selcollist */
     => {
//line 867 "src/parser/parse.y"
{ self[-1] .minor= YYMINORTYPE::yy504( (None, Some(self.yy_move(0).yy106()))); }
        }
      185 /* upsert ::= ON CONFLICT LP sortlist RP where_opt DO UPDATE SET setlist where_opt upsert */
     => {
//line 870 "src/parser/parse.y"
{ let index = UpsertIndex{ targets: self.yy_move(-8).yy536(), where_clause: self.yy_move(-6).yy479() };
                let do_clause = UpsertDo::Set{ sets: self.yy_move(-2).yy517(), where_clause: self.yy_move(-1).yy479() };
                let (next, returning) = self.yy_move(0).yy504();
                self[-11] .minor= YYMINORTYPE::yy504( (Some(Upsert{ index: Some(index), do_clause, next: next.map(Box::new) }), returning));}
        }
      186 /* upsert ::= ON CONFLICT LP sortlist RP where_opt DO NOTHING upsert */
     => {
//line 875 "src/parser/parse.y"
{ let index = UpsertIndex{ targets: self.yy_move(-5).yy536(), where_clause: self.yy_move(-3).yy479() };
                let (next, returning) = self.yy_move(0).yy504();
                self[-8] .minor= YYMINORTYPE::yy504( (Some(Upsert{ index: Some(index), do_clause: UpsertDo::Nothing, next: next.map(Box::new) }), returning)); }
        }
      187 /* upsert ::= ON CONFLICT DO NOTHING returning */
     => {
//line 879 "src/parser/parse.y"
{ self[-4] .minor= YYMINORTYPE::yy504( (Some(Upsert{ index: None, do_clause: UpsertDo::Nothing, next: None }), self.yy_move(0).yy265())); }
        }
      188 /* upsert ::= ON CONFLICT DO UPDATE SET setlist where_opt returning */
     => {
//line 881 "src/parser/parse.y"
{ let do_clause = UpsertDo::Set{ sets: self.yy_move(-2).yy517(), where_clause: self.yy_move(-1).yy479() };
                self[-7] .minor= YYMINORTYPE::yy504( (Some(Upsert{ index: None, do_clause, next: None }), self.yy_move(0).yy265()));}
        }
      189 /* returning ::= RETURNING selcollist */
     => {
//line 885 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy265( Some(self.yy_move(0).yy106()));}
        }
      190 /* returning ::= */
     => {
//line 886 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy265( None);}
        }
      191 /* insert_cmd ::= INSERT orconf */
     => {
//line 889 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy254( self.yy_move(0).yy254());}
        }
      192 /* insert_cmd ::= REPLACE */
     => {
//line 890 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy254( Some(ResolveType::Replace));}
        }
      193 /* idlist_opt ::= */
     => {
//line 894 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy25( None);}
        }
      194 /* idlist_opt ::= LP idlist RP */
     => {
//line 895 "src/parser/parse.y"
{self[-2] .minor= YYMINORTYPE::yy25( Some(self.yy_move(-1).yy162()));}
        }
      195 /* idlist ::= idlist COMMA nm */
     => {
//line 897 "src/parser/parse.y"
{let id = self.yy_move(0).yy222(); self[-2].yy162_ref().push(id);}
        }
      196 /* idlist ::= nm */
     => {
//line 899 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy162( vec![self.yy_move(0).yy222()]); /*A-overwrites-Y*/}
        }
      197 /* expr ::= LP expr RP */
     => {
//line 911 "src/parser/parse.y"
{self[-2] .minor= YYMINORTYPE::yy480( Expr::parenthesized(self.yy_move(-1).yy480()));}
        }
      198 /* expr ::= ID|INDEXED|JOIN_KW */
     => {
//line 912 "src/parser/parse.y"
{self[0].minor= YYMINORTYPE::yy480( Expr::id(self[0].major, self.yy_move(0).yy0())); /*A-overwrites-X*/}
        }
      199 /* expr ::= nm DOT nm */
     => {
//line 913 "src/parser/parse.y"
{
  self[-2] .minor= YYMINORTYPE::yy480( Expr::Qualified(self.yy_move(-2).yy222(), self.yy_move(0).yy222())); /*A-overwrites-X*/
}
        }
      200 /* expr ::= nm DOT nm DOT nm */
     => {
//line 916 "src/parser/parse.y"
{
  self[-4] .minor= YYMINORTYPE::yy480( Expr::DoublyQualified(self.yy_move(-4).yy222(), self.yy_move(-2).yy222(), self.yy_move(0).yy222())); /*A-overwrites-X*/
}
        }
      201 /* term ::= NULL */
     => {
//line 919 "src/parser/parse.y"
{self[0].minor= YYMINORTYPE::yy480(Expr::Literal(Literal::Null));}
        }
      202 /* term ::= BLOB */
     => {
//line 920 "src/parser/parse.y"
{self[0].minor= YYMINORTYPE::yy480(Expr::Literal(Literal::Blob(self.yy_move(0).yy0().unwrap()))); /*A-overwrites-X*/}
        }
      203 /* term ::= STRING */
     => {
//line 921 "src/parser/parse.y"
{self[0].minor= YYMINORTYPE::yy480(Expr::Literal(Literal::String(self.yy_move(0).yy0().unwrap()))); /*A-overwrites-X*/}
        }
      204 /* term ::= FLOAT|INTEGER */
     => {
//line 922 "src/parser/parse.y"
{
  self[0] .minor= YYMINORTYPE::yy480( Expr::Literal(Literal::Numeric(self.yy_move(0).yy0().unwrap()))); /*A-overwrites-X*/
}
        }
      205 /* expr ::= VARIABLE */
     => {
//line 925 "src/parser/parse.y"
{
  self[0] .minor= YYMINORTYPE::yy480( Expr::Variable(self.yy_move(0).yy0().unwrap())); /*A-overwrites-X*/
}
        }
      206 /* expr ::= expr COLLATE ID|STRING */
     => {
//line 928 "src/parser/parse.y"
{
  self[-2] .minor= YYMINORTYPE::yy480( Expr::collate(self.yy_move(-2).yy480(), self[0].major, self.yy_move(0).yy0())); /*A-overwrites-X*/
}
        }
      207 /* expr ::= CAST LP expr AS typetoken RP */
     => {
//line 932 "src/parser/parse.y"
{
  self[-5] .minor= YYMINORTYPE::yy480( Expr::cast(self.yy_move(-3).yy480(), self.yy_move(-1).yy362().unwrap())); // FIXME mandatory ?
}
        }
      208 /* expr ::= ID|INDEXED|JOIN_KW LP distinct exprlist RP */
     => {
//line 937 "src/parser/parse.y"
{
  self[-4] .minor= YYMINORTYPE::yy480( Expr::FunctionCall{ name: Id::from_token(self[-4].major, self.yy_move(-4).yy0()), distinctness: self.yy_move(-2).yy415(), args: self.yy_move(-1).yy419(), filter_over: None }); /*A-overwrites-X*/
}
        }
      209 /* expr ::= ID|INDEXED|JOIN_KW LP STAR RP */
     => {
//line 940 "src/parser/parse.y"
{
  self[-3] .minor= YYMINORTYPE::yy480( Expr::FunctionCallStar{ name: Id::from_token(self[-3].major, self.yy_move(-3).yy0()), filter_over: None }); /*A-overwrites-X*/
}
        }
      210 /* expr ::= ID|INDEXED|JOIN_KW LP distinct exprlist RP filter_over */
     => {
//line 945 "src/parser/parse.y"
{
  self[-5] .minor= YYMINORTYPE::yy480( Expr::FunctionCall{ name: Id::from_token(self[-5].major, self.yy_move(-5).yy0()), distinctness: self.yy_move(-3).yy415(), args: self.yy_move(-2).yy419(), filter_over: Some(self.yy_move(0).yy353()) }); /*A-overwrites-X*/
}
        }
      211 /* expr ::= ID|INDEXED|JOIN_KW LP STAR RP filter_over */
     => {
//line 948 "src/parser/parse.y"
{
  self[-4] .minor= YYMINORTYPE::yy480( Expr::FunctionCallStar{ name: Id::from_token(self[-4].major, self.yy_move(-4).yy0()), filter_over: Some(self.yy_move(0).yy353()) }); /*A-overwrites-X*/
}
        }
      212 /* term ::= CTIME_KW */
     => {
//line 953 "src/parser/parse.y"
{
  yylhsminor = YYMINORTYPE::yy480( Expr::Literal(Literal::from_ctime_kw(self.yy_move(0).yy0())));
}
  self[0].minor = yylhsminor;
        }
      213 /* expr ::= LP nexprlist COMMA expr RP */
     => {
//line 957 "src/parser/parse.y"
{
  let mut x = self.yy_move(-3).yy312();
  x.push(self.yy_move(-1).yy480());
  self[-4] .minor= YYMINORTYPE::yy480( Expr::Parenthesized(x));
}
        }
      214 /* expr ::= expr AND expr */
     | 215 /* expr ::= expr OR expr */
     | 216 /* expr ::= expr LT|GT|GE|LE expr */
     | 217 /* expr ::= expr EQ|NE expr */
     | 218 /* expr ::= expr BITAND|BITOR|LSHIFT|RSHIFT expr */
     | 219 /* expr ::= expr PLUS|MINUS expr */
     | 220 /* expr ::= expr STAR|SLASH|REM expr */
     | 221 /* expr ::= expr CONCAT expr */
     => {
//line 963 "src/parser/parse.y"
{self[-2].minor= YYMINORTYPE::yy480(Expr::binary(self.yy_move(-2).yy480(),self[-1].major,self.yy_move(0).yy480())); /*A-overwrites-X*/}
        }
      222 /* likeop ::= LIKE_KW|MATCH */
     => {
//line 976 "src/parser/parse.y"
{yylhsminor= YYMINORTYPE::yy343((false, LikeOperator::from_token(self[0].major, self.yy_move(0).yy0()))); /*yylhsminor.yy343_ref()-overwrite-self.yy_move(0).yy0()*/}
  self[0].minor = yylhsminor;
        }
      223 /* likeop ::= NOT LIKE_KW|MATCH */
     => {
//line 977 "src/parser/parse.y"
{self[-1].minor= YYMINORTYPE::yy343((true, LikeOperator::from_token(self[0].major, self.yy_move(0).yy0()))); /*self[-1].yy343_ref()-overwrite-self.yy_move(0).yy0()*/}
        }
      224 /* expr ::= expr likeop expr */
     => {
//line 978 "src/parser/parse.y"
{
  let op = self.yy_move(-1).yy343();
  self[-2].minor= YYMINORTYPE::yy480(Expr::like(self.yy_move(-2).yy480(),op.0,op.1,self.yy_move(0).yy480(),None)); /*A-overwrites-X*/
}
        }
      225 /* expr ::= expr likeop expr ESCAPE expr */
     => {
//line 982 "src/parser/parse.y"
{
  let op = self.yy_move(-3).yy343();
  self[-4].minor= YYMINORTYPE::yy480(Expr::like(self.yy_move(-4).yy480(),op.0,op.1,self.yy_move(-2).yy480(),Some(self.yy_move(0).yy480()))); /*A-overwrites-X*/
}
        }
      226 /* expr ::= expr ISNULL|NOTNULL */
     => {
//line 987 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy480( Expr::not_null(self.yy_move(-1).yy480(), self[0].major)); /*A-overwrites-X*/}
        }
      227 /* expr ::= expr NOT NULL */
     => {
//line 988 "src/parser/parse.y"
{self[-2] .minor= YYMINORTYPE::yy480( Expr::not_null(self.yy_move(-2).yy480(), TokenType::TK_NOTNULL as YYCODETYPE)); /*A-overwrites-X*/}
        }
      228 /* expr ::= expr IS expr */
     => {
//line 999 "src/parser/parse.y"
{
  self[-2] .minor= YYMINORTYPE::yy480( Expr::binary(self.yy_move(-2).yy480(), self[-1].major, self.yy_move(0).yy480())); /*A-overwrites-X*/
}
        }
      229 /* expr ::= expr IS NOT expr */
     => {
//line 1002 "src/parser/parse.y"
{
  self[-3] .minor= YYMINORTYPE::yy480( Expr::binary(self.yy_move(-3).yy480(), TokenType::TK_NOT as YYCODETYPE, self.yy_move(0).yy480())); /*A-overwrites-X*/
}
        }
      230 /* expr ::= expr IS NOT DISTINCT FROM expr */
     => {
//line 1005 "src/parser/parse.y"
{
  self[-5] .minor= YYMINORTYPE::yy480( Expr::binary(self.yy_move(-5).yy480(), TokenType::TK_IS as YYCODETYPE, self.yy_move(0).yy480())); /*A-overwrites-X*/
}
        }
      231 /* expr ::= expr IS DISTINCT FROM expr */
     => {
//line 1008 "src/parser/parse.y"
{
  self[-4] .minor= YYMINORTYPE::yy480( Expr::binary(self.yy_move(-4).yy480(), TokenType::TK_NOT as YYCODETYPE, self.yy_move(0).yy480())); /*A-overwrites-X*/
}
        }
      232 /* expr ::= NOT expr */
     | 233 /* expr ::= BITNOT expr */
     => {
//line 1013 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy480( Expr::unary(UnaryOperator::from(self[-1].major), self.yy_move(0).yy480()));/*A-overwrites-B*/}
        }
      234 /* expr ::= PLUS|MINUS expr */
     => {
//line 1016 "src/parser/parse.y"
{
  self[-1] .minor= YYMINORTYPE::yy480( Expr::unary(UnaryOperator::from(self[-1].major), self.yy_move(0).yy480()));/*A-overwrites-B*/
}
        }
      235 /* expr ::= expr PTR expr */
     => {
//line 1020 "src/parser/parse.y"
{
  yylhsminor = YYMINORTYPE::yy480( Expr::ptr(self.yy_move(-2).yy480(), self.yy_move(-1).yy0(), self.yy_move(0).yy480()));
}
  self[-2].minor = yylhsminor;
        }
      236 /* between_op ::= BETWEEN */
     | 239 /* in_op ::= IN */
     => {
//line 1025 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy173( false);}
        }
      238 /* expr ::= expr between_op expr AND expr */
     => {
//line 1027 "src/parser/parse.y"
{
  self[-4] .minor= YYMINORTYPE::yy480( Expr::between(self.yy_move(-4).yy480(), self.yy_move(-3).yy173(), self.yy_move(-2).yy480(), self.yy_move(0).yy480()));/*A-overwrites-B*/
}
        }
      241 /* expr ::= expr in_op LP exprlist RP */
     => {
//line 1034 "src/parser/parse.y"
{
    self[-4] .minor= YYMINORTYPE::yy480( Expr::in_list(self.yy_move(-4).yy480(), self.yy_move(-3).yy173(), self.yy_move(-1).yy419()));/*A-overwrites-X*/
  }
        }
      242 /* expr ::= LP select RP */
     => {
//line 1037 "src/parser/parse.y"
{
    self[-2] .minor= YYMINORTYPE::yy480( Expr::sub_query(self.yy_move(-1).yy213()));
  }
        }
      243 /* expr ::= expr in_op LP select RP */
     => {
//line 1040 "src/parser/parse.y"
{
    self[-4] .minor= YYMINORTYPE::yy480( Expr::in_select(self.yy_move(-4).yy480(), self.yy_move(-3).yy173(), self.yy_move(-1).yy213()));/*A-overwrites-X*/
  }
        }
      244 /* expr ::= expr in_op fullname paren_exprlist */
     => {
//line 1043 "src/parser/parse.y"
{
    self[-3] .minor= YYMINORTYPE::yy480( Expr::in_table(self.yy_move(-3).yy480(), self.yy_move(-2).yy173(), self.yy_move(-1).yy474(), self.yy_move(0).yy419()));/*A-overwrites-X*/
  }
        }
      245 /* expr ::= EXISTS LP select RP */
     => {
//line 1046 "src/parser/parse.y"
{
    self[-3] .minor= YYMINORTYPE::yy480( Expr::Exists(Box::new(self.yy_move(-1).yy213())));
  }
        }
      246 /* expr ::= CASE case_operand case_exprlist case_else END */
     => {
//line 1052 "src/parser/parse.y"
{
  self[-4] .minor= YYMINORTYPE::yy480( Expr::Case{ base: self.yy_move(-3).yy479().map(Box::new), when_then_pairs: self.yy_move(-2).yy473(), else_expr: self.yy_move(-1).yy479().map(Box::new)});
}
        }
      247 /* case_exprlist ::= case_exprlist WHEN expr THEN expr */
     => {
//line 1056 "src/parser/parse.y"
{
  let pair = (self.yy_move(-2).yy480(), self.yy_move(0).yy480());
  self[-4].yy473_ref().push(pair);
}
        }
      248 /* case_exprlist ::= WHEN expr THEN expr */
     => {
//line 1060 "src/parser/parse.y"
{
  self[-3] .minor= YYMINORTYPE::yy473( vec![(self.yy_move(-2).yy480(), self.yy_move(0).yy480())]);
}
        }
      251 /* case_operand ::= expr */
     => {
//line 1067 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy479( Some(self.yy_move(0).yy480())); /*A-overwrites-X*/}
        }
      253 /* exprlist ::= nexprlist */
     => {
//line 1073 "src/parser/parse.y"
{yylhsminor = YYMINORTYPE::yy419( Some(self.yy_move(0).yy312()));}
  self[0].minor = yylhsminor;
        }
      254 /* exprlist ::= */
     | 257 /* paren_exprlist ::= */
     => {
//line 1074 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy419( None);}
        }
      255 /* nexprlist ::= nexprlist COMMA expr */
     => {
//line 1076 "src/parser/parse.y"
{ let expr = self.yy_move(0).yy480(); self[-2].yy312_ref().push(expr);}
        }
      256 /* nexprlist ::= expr */
     => {
//line 1078 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy312( vec![self.yy_move(0).yy480()]); /*A-overwrites-Y*/}
        }
      258 /* paren_exprlist ::= LP exprlist RP */
     => {
//line 1085 "src/parser/parse.y"
{self[-2] .minor= YYMINORTYPE::yy419( self.yy_move(-1).yy419());}
        }
      259 /* cmd ::= createkw uniqueflag INDEX ifnotexists fullname ON nm LP sortlist RP where_opt */
     => {
//line 1092 "src/parser/parse.y"
{
  self.ctx.stmt = Some(Stmt::CreateIndex { unique: self.yy_move(-9).yy173(), if_not_exists: self.yy_move(-7).yy173(), idx_name: self.yy_move(-6).yy474(),
                                            tbl_name: self.yy_move(-4).yy222(), columns: self.yy_move(-2).yy536(), where_clause: self.yy_move(0).yy479() });
}
        }
      262 /* eidlist_opt ::= */
     => {
//line 1123 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy143( None);}
        }
      263 /* eidlist_opt ::= LP eidlist RP */
     => {
//line 1124 "src/parser/parse.y"
{self[-2] .minor= YYMINORTYPE::yy143( Some(self.yy_move(-1).yy360()));}
        }
      264 /* eidlist ::= eidlist COMMA nm collate sortorder */
     => {
//line 1125 "src/parser/parse.y"
{
  let ic = IndexedColumn{ col_name: self.yy_move(-2).yy222(), collation_name: self.yy_move(-1).yy329(), order: self.yy_move(0).yy296() };
  self[-4].yy360_ref().push(ic);
}
        }
      265 /* eidlist ::= nm collate sortorder */
     => {
//line 1129 "src/parser/parse.y"
{
  self[-2] .minor= YYMINORTYPE::yy360( vec![IndexedColumn{ col_name: self.yy_move(-2).yy222(), collation_name: self.yy_move(-1).yy329(), order: self.yy_move(0).yy296() }]); /*A-overwrites-Y*/
}
        }
      267 /* collate ::= COLLATE ID|STRING */
     => {
//line 1135 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy329( Some(Name::from_token(self[0].major, self.yy_move(0).yy0())));}
        }
      268 /* cmd ::= DROP INDEX ifexists fullname */
     => {
//line 1140 "src/parser/parse.y"
{self.ctx.stmt = Some(Stmt::DropIndex{if_exists: self.yy_move(-1).yy173(), idx_name: self.yy_move(0).yy474()});}
        }
      269 /* cmd ::= VACUUM vinto */
     => {
//line 1146 "src/parser/parse.y"
{self.ctx.stmt = Some(Stmt::Vacuum(None, self.yy_move(0).yy479()));}
        }
      270 /* cmd ::= VACUUM nm vinto */
     => {
//line 1147 "src/parser/parse.y"
{self.ctx.stmt = Some(Stmt::Vacuum(Some(self.yy_move(-1).yy222()), self.yy_move(0).yy479()));}
        }
      273 /* cmd ::= PRAGMA fullname */
     => {
//line 1155 "src/parser/parse.y"
{self.ctx.stmt = Some(Stmt::Pragma(self.yy_move(0).yy474(), None));}
        }
      274 /* cmd ::= PRAGMA fullname EQ nmnum */
     | 276 /* cmd ::= PRAGMA fullname EQ minus_num */
     => {
//line 1156 "src/parser/parse.y"
{self.ctx.stmt = Some(Stmt::Pragma(self.yy_move(-2).yy474(), Some(PragmaBody::Equals(self.yy_move(0).yy480()))));}
        }
      275 /* cmd ::= PRAGMA fullname LP nmnum RP */
     | 277 /* cmd ::= PRAGMA fullname LP minus_num RP */
     => {
//line 1157 "src/parser/parse.y"
{self.ctx.stmt = Some(Stmt::Pragma(self.yy_move(-3).yy474(), Some(PragmaBody::Call(self.yy_move(-1).yy480()))));}
        }
      278 /* nmnum ::= nm */
     => {
//line 1165 "src/parser/parse.y"
{yylhsminor = YYMINORTYPE::yy480( Expr::Name(self.yy_move(0).yy222()));}
  self[0].minor = yylhsminor;
        }
      279 /* nmnum ::= ON */
     | 280 /* nmnum ::= DELETE */
     | 281 /* nmnum ::= DEFAULT */
     => {
//line 1166 "src/parser/parse.y"
{yylhsminor = YYMINORTYPE::yy480( Expr::Literal(Literal::Keyword(from_token(self[0].major, self.yy_move(0).yy0()))));}
  self[0].minor = yylhsminor;
        }
      282 /* plus_num ::= PLUS INTEGER|FLOAT */
     => {
//line 1172 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy480( Expr::unary(UnaryOperator::Positive, Expr::Literal(Literal::Numeric(self.yy_move(0).yy0().unwrap()))));}
        }
      283 /* plus_num ::= INTEGER|FLOAT */
     => {
//line 1173 "src/parser/parse.y"
{yylhsminor = YYMINORTYPE::yy480( Expr::Literal(Literal::Numeric(self.yy_move(0).yy0().unwrap())));}
  self[0].minor = yylhsminor;
        }
      284 /* minus_num ::= MINUS INTEGER|FLOAT */
     => {
//line 1175 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy480( Expr::unary(UnaryOperator::Negative, Expr::Literal(Literal::Numeric(self.yy_move(0).yy0().unwrap()))));}
        }
      285 /* cmd ::= createkw temp TRIGGER ifnotexists fullname trigger_time trigger_event ON fullname foreach_clause when_clause BEGIN trigger_cmd_list END */
     => {
//line 1181 "src/parser/parse.y"
{
  self.ctx.stmt = Some(Stmt::CreateTrigger{
    temporary: self.yy_move(-12).yy173(), if_not_exists: self.yy_move(-10).yy173(), trigger_name: self.yy_move(-9).yy474(), time: self.yy_move(-8).yy59(), event: self.yy_move(-7).yy47(), tbl_name: self.yy_move(-5).yy474(),
    for_each_row: self.yy_move(-4).yy173(), when_clause: self.yy_move(-3).yy479(), commands: self.yy_move(-1).yy33()
  });
}
        }
      286 /* trigger_time ::= BEFORE */
     => {
//line 1189 "src/parser/parse.y"
{ self[0] .minor= YYMINORTYPE::yy59( Some(TriggerTime::Before)); }
        }
      287 /* trigger_time ::= AFTER */
     => {
//line 1190 "src/parser/parse.y"
{ self[0] .minor= YYMINORTYPE::yy59( Some(TriggerTime::After)); }
        }
      288 /* trigger_time ::= INSTEAD OF */
     => {
//line 1191 "src/parser/parse.y"
{ self[-1] .minor= YYMINORTYPE::yy59( Some(TriggerTime::InsteadOf));}
        }
      289 /* trigger_time ::= */
     => {
//line 1192 "src/parser/parse.y"
{ self[1] .minor= YYMINORTYPE::yy59( None); }
        }
      290 /* trigger_event ::= DELETE */
     => {
//line 1195 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy47( TriggerEvent::Delete);}
        }
      291 /* trigger_event ::= INSERT */
     => {
//line 1196 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy47( TriggerEvent::Insert);}
        }
      292 /* trigger_event ::= UPDATE */
     => {
//line 1197 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy47( TriggerEvent::Update);}
        }
      293 /* trigger_event ::= UPDATE OF idlist */
     => {
//line 1198 "src/parser/parse.y"
{self[-2] .minor= YYMINORTYPE::yy47( TriggerEvent::UpdateOf(self.yy_move(0).yy162()));}
        }
      294 /* foreach_clause ::= */
     => {
//line 1201 "src/parser/parse.y"
{ self[1] .minor= YYMINORTYPE::yy173( false); }
        }
      295 /* foreach_clause ::= FOR EACH ROW */
     => {
//line 1202 "src/parser/parse.y"
{ self[-2] .minor= YYMINORTYPE::yy173( true);  }
        }
      296 /* when_clause ::= */
     | 315 /* key_opt ::= */
     => {
//line 1205 "src/parser/parse.y"
{ self[1] .minor= YYMINORTYPE::yy479( None); }
        }
      297 /* when_clause ::= WHEN expr */
     | 316 /* key_opt ::= KEY expr */
     => {
//line 1206 "src/parser/parse.y"
{ self[-1] .minor= YYMINORTYPE::yy479( Some(self.yy_move(0).yy480())); }
        }
      298 /* trigger_cmd_list ::= trigger_cmd_list trigger_cmd SEMI */
     => {
//line 1209 "src/parser/parse.y"
{
  let tc = self.yy_move(-1).yy569();
  self[-2].yy33_ref().push(tc);
}
        }
      299 /* trigger_cmd_list ::= trigger_cmd SEMI */
     => {
//line 1213 "src/parser/parse.y"
{
  yylhsminor = YYMINORTYPE::yy33( vec![self.yy_move(-1).yy569()]);
}
  self[-1].minor = yylhsminor;
        }
      300 /* trnm ::= nm DOT nm */
     => {
//line 1223 "src/parser/parse.y"
{
  self[-2] .minor= YYMINORTYPE::yy222( self.yy_move(0).yy222());
  self.ctx.sqlite3_error_msg(
        "qualified table names are not allowed on INSERT, UPDATE, and DELETE \
         statements within triggers");
}
        }
      301 /* tridxby ::= INDEXED BY nm */
     => {
//line 1235 "src/parser/parse.y"
{
  self.ctx.sqlite3_error_msg(
        "the INDEXED BY clause is not allowed on UPDATE or DELETE statements \
         within triggers");
}
        }
      302 /* tridxby ::= NOT INDEXED */
     => {
//line 1240 "src/parser/parse.y"
{
  self.ctx.sqlite3_error_msg(
        "the NOT INDEXED clause is not allowed on UPDATE or DELETE statements \
         within triggers");
}
        }
      303 /* trigger_cmd ::= UPDATE orconf trnm tridxby SET setlist from where_opt */
     => {
//line 1252 "src/parser/parse.y"
{self[-7] .minor= YYMINORTYPE::yy569( TriggerCmd::Update{ or_conflict: self.yy_move(-6).yy254(), tbl_name: self.yy_move(-5).yy222(), sets: self.yy_move(-2).yy517(), from: self.yy_move(-1).yy485(), where_clause: self.yy_move(0).yy479() });}
        }
      304 /* trigger_cmd ::= insert_cmd INTO trnm idlist_opt select upsert */
     => {
//line 1256 "src/parser/parse.y"
{
  let (upsert, returning) = self.yy_move(0).yy504();
   self[-5] .minor= YYMINORTYPE::yy569( TriggerCmd::Insert{ or_conflict: self.yy_move(-5).yy254(), tbl_name: self.yy_move(-3).yy222(), col_names: self.yy_move(-2).yy25(), select: self.yy_move(-1).yy213(), upsert, returning });/*A-overwrites-R*/
}
        }
      305 /* trigger_cmd ::= DELETE FROM trnm tridxby where_opt */
     => {
//line 1262 "src/parser/parse.y"
{self[-4] .minor= YYMINORTYPE::yy569( TriggerCmd::Delete{ tbl_name: self.yy_move(-2).yy222(), where_clause: self.yy_move(0).yy479() });}
        }
      306 /* trigger_cmd ::= select */
     => {
//line 1266 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy569( TriggerCmd::Select(self.yy_move(0).yy213())); /*A-overwrites-X*/}
        }
      307 /* expr ::= RAISE LP IGNORE RP */
     => {
//line 1269 "src/parser/parse.y"
{
  self[-3] .minor= YYMINORTYPE::yy480( Expr::Raise(ResolveType::Ignore, None));
}
        }
      308 /* expr ::= RAISE LP raisetype COMMA nm RP */
     => {
//line 1272 "src/parser/parse.y"
{
  self[-5] .minor= YYMINORTYPE::yy480( Expr::Raise(self.yy_move(-3).yy207(), Some(self.yy_move(-1).yy222())));
}
        }
      309 /* raisetype ::= ROLLBACK */
     => {
//line 1278 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy207( ResolveType::Rollback);}
        }
      310 /* raisetype ::= ABORT */
     => {
//line 1279 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy207( ResolveType::Abort);}
        }
      311 /* raisetype ::= FAIL */
     => {
//line 1280 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy207( ResolveType::Fail);}
        }
      312 /* cmd ::= DROP TRIGGER ifexists fullname */
     => {
//line 1285 "src/parser/parse.y"
{
  self.ctx.stmt = Some(Stmt::DropTrigger{ if_exists: self.yy_move(-1).yy173(), trigger_name: self.yy_move(0).yy474()});
}
        }
      313 /* cmd ::= ATTACH database_kw_opt expr AS expr key_opt */
     => {
//line 1292 "src/parser/parse.y"
{
  self.ctx.stmt = Some(Stmt::Attach{ expr: self.yy_move(-3).yy480(), db_name: self.yy_move(-1).yy480(), key: self.yy_move(0).yy479() });
}
        }
      314 /* cmd ::= DETACH database_kw_opt expr */
     => {
//line 1295 "src/parser/parse.y"
{
  self.ctx.stmt = Some(Stmt::Detach(self.yy_move(0).yy480()));
}
        }
      317 /* cmd ::= REINDEX */
     => {
//line 1309 "src/parser/parse.y"
{self.ctx.stmt = Some(Stmt::Reindex { obj_name: None });}
        }
      318 /* cmd ::= REINDEX fullname */
     => {
//line 1310 "src/parser/parse.y"
{self.ctx.stmt = Some(Stmt::Reindex { obj_name: Some(self.yy_move(0).yy474()) });}
        }
      319 /* cmd ::= ANALYZE */
     => {
//line 1315 "src/parser/parse.y"
{self.ctx.stmt = Some(Stmt::Analyze(None));}
        }
      320 /* cmd ::= ANALYZE fullname */
     => {
//line 1316 "src/parser/parse.y"
{self.ctx.stmt = Some(Stmt::Analyze(Some(self.yy_move(0).yy474())));}
        }
      321 /* cmd ::= ALTER TABLE fullname RENAME TO nm */
     => {
//line 1321 "src/parser/parse.y"
{
  self.ctx.stmt = Some(Stmt::AlterTable(self.yy_move(-3).yy474(), AlterTableBody::RenameTo(self.yy_move(0).yy222())));
}
        }
      322 /* cmd ::= ALTER TABLE fullname ADD kwcolumn_opt columnname carglist */
     => {
//line 1325 "src/parser/parse.y"
{
  let (col_name, col_type) = self.yy_move(-1).yy444();
  let cd = ColumnDefinition{ col_name, col_type, constraints: self.yy_move(0).yy305() };
  self.ctx.stmt = Some(Stmt::AlterTable(self.yy_move(-4).yy474(), AlterTableBody::AddColumn(cd)));
}
        }
      323 /* cmd ::= ALTER TABLE fullname RENAME kwcolumn_opt nm TO nm */
     => {
//line 1330 "src/parser/parse.y"
{
  self.ctx.stmt = Some(Stmt::AlterTable(self.yy_move(-5).yy474(), AlterTableBody::RenameColumn{ old: self.yy_move(-2).yy222(), new: self.yy_move(0).yy222() }));
}
        }
      324 /* cmd ::= ALTER TABLE fullname DROP kwcolumn_opt nm */
     => {
//line 1333 "src/parser/parse.y"
{
  self.ctx.stmt = Some(Stmt::AlterTable(self.yy_move(-3).yy474(), AlterTableBody::DropColumn(self.yy_move(0).yy222())));
}
        }
      325 /* cmd ::= ALTER TABLE fullname ALTER COLUMNKW columnname TO columnname carglist */
     => {
//line 1337 "src/parser/parse.y"
{
  let (colfrom_name, _) = self.yy_move(-3).yy444();
  let (col_name, col_type) = self.yy_move(-1).yy444();
  let cd = ColumnDefinition{ col_name, col_type, constraints: self.yy_move(0).yy305() };
  self.ctx.stmt = Some(Stmt::AlterTable(self.yy_move(-6).yy474(), AlterTableBody::AlterColumn{ old: colfrom_name, cd }));
}
        }
      326 /* cmd ::= create_vtab */
     => {
//line 1350 "src/parser/parse.y"
{self.ctx.stmt = Some(self.yy_move(0).yy77());}
        }
      327 /* cmd ::= create_vtab LP vtabarglist RP */
     => {
//line 1351 "src/parser/parse.y"
{
  let mut stmt = self.yy_move(-3).yy77();
  if let Stmt::CreateVirtualTable{ ref mut args, .. } = stmt {
    *args = self.ctx.module_args();
  }
  self.ctx.stmt = Some(stmt);
}
        }
      328 /* create_vtab ::= createkw VIRTUAL TABLE ifnotexists fullname USING nm */
     => {
//line 1360 "src/parser/parse.y"
{
    self[-6] .minor= YYMINORTYPE::yy77( Stmt::CreateVirtualTable{ if_not_exists: self.yy_move(-3).yy173(), tbl_name: self.yy_move(-2).yy474(), module_name: self.yy_move(0).yy222(), args: None });
}
        }
      329 /* vtabarg ::= */
     => {
//line 1365 "src/parser/parse.y"
{self.ctx.vtab_arg_init();}
        }
      330 /* vtabargtoken ::= ANY */
     => {
//line 1367 "src/parser/parse.y"
{ let x = self.yy_move(0).yy0(); self.ctx.vtab_arg_extend(x);}
        }
      331 /* vtabargtoken ::= lp anylist RP */
     | 332 /* lp ::= LP */
     => {
//line 1368 "src/parser/parse.y"
{let x = self.yy_move(0).yy0(); self.ctx.vtab_arg_extend(x);}
        }
      333 /* with ::= */
     => {
//line 1382 "src/parser/parse.y"
{ self[1] .minor= YYMINORTYPE::yy280( None); }
        }
      334 /* with ::= WITH wqlist */
     => {
//line 1384 "src/parser/parse.y"
{ self[-1] .minor= YYMINORTYPE::yy280( Some(With{ recursive: false, ctes: self.yy_move(0).yy97() })); }
        }
      335 /* with ::= WITH RECURSIVE wqlist */
     => {
//line 1385 "src/parser/parse.y"
{ self[-2] .minor= YYMINORTYPE::yy280( Some(With{ recursive: true, ctes: self.yy_move(0).yy97() })); }
        }
      336 /* wqas ::= AS */
     => {
//line 1388 "src/parser/parse.y"
{self[0] .minor= YYMINORTYPE::yy520( Materialized::Any);}
        }
      337 /* wqas ::= AS MATERIALIZED */
     => {
//line 1389 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy520( Materialized::Yes);}
        }
      338 /* wqas ::= AS NOT MATERIALIZED */
     => {
//line 1390 "src/parser/parse.y"
{self[-2] .minor= YYMINORTYPE::yy520( Materialized::No);}
        }
      339 /* wqitem ::= nm eidlist_opt wqas LP select RP */
     => {
//line 1391 "src/parser/parse.y"
{
  self[-5] .minor= YYMINORTYPE::yy557( CommonTableExpr{ tbl_name: self.yy_move(-5).yy222(), columns: self.yy_move(-4).yy143(), materialized: self.yy_move(-3).yy520(), select: self.yy_move(-1).yy213() }); /*A-overwrites-X*/
}
        }
      340 /* wqlist ::= wqitem */
     => {
//line 1394 "src/parser/parse.y"
{
  self[0] .minor= YYMINORTYPE::yy97( vec![self.yy_move(0).yy557()]); /*A-overwrites-X*/
}
        }
      341 /* wqlist ::= wqlist COMMA wqitem */
     => {
//line 1397 "src/parser/parse.y"
{
  let cte = self.yy_move(0).yy557();
  CommonTableExpr::add_cte(self[-2].yy97_ref(), cte)?;
}
        }
      342 /* windowdefn_list ::= windowdefn */
     => {
//line 1411 "src/parser/parse.y"
{ yylhsminor = YYMINORTYPE::yy472( vec![self.yy_move(0).yy256()]); }
  self[0].minor = yylhsminor;
        }
      343 /* windowdefn_list ::= windowdefn_list COMMA windowdefn */
     => {
//line 1412 "src/parser/parse.y"
{
  let w = self.yy_move(0).yy256();
  self[-2].yy472_ref().push(w);
}
        }
      344 /* windowdefn ::= nm AS LP window RP */
     => {
//line 1418 "src/parser/parse.y"
{
  yylhsminor = YYMINORTYPE::yy256( WindowDef { name: self.yy_move(-4).yy222(), window: self.yy_move(-1).yy13()});
}
  self[-4].minor = yylhsminor;
        }
      345 /* window ::= PARTITION BY nexprlist orderby_opt frame_opt */
     => {
//line 1438 "src/parser/parse.y"
{
  self[-4] .minor= YYMINORTYPE::yy13( Window{ base: None,  partition_by: Some(self.yy_move(-2).yy312()), order_by: self.yy_move(-1).yy19(), frame_clause: self.yy_move(0).yy4()});
}
        }
      346 /* window ::= nm PARTITION BY nexprlist orderby_opt frame_opt */
     => {
//line 1441 "src/parser/parse.y"
{
  yylhsminor = YYMINORTYPE::yy13( Window{ base: Some(self.yy_move(-5).yy222()),  partition_by: Some(self.yy_move(-2).yy312()), order_by: self.yy_move(-1).yy19(), frame_clause: self.yy_move(0).yy4()});
}
  self[-5].minor = yylhsminor;
        }
      347 /* window ::= ORDER BY sortlist frame_opt */
     => {
//line 1444 "src/parser/parse.y"
{
  self[-3] .minor= YYMINORTYPE::yy13( Window{ base: None,  partition_by: None, order_by: Some(self.yy_move(-1).yy536()), frame_clause: self.yy_move(0).yy4()});
}
        }
      348 /* window ::= nm ORDER BY sortlist frame_opt */
     => {
//line 1447 "src/parser/parse.y"
{
  yylhsminor = YYMINORTYPE::yy13( Window{ base: Some(self.yy_move(-4).yy222()),  partition_by: None, order_by: Some(self.yy_move(-1).yy536()), frame_clause: self.yy_move(0).yy4()});
}
  self[-4].minor = yylhsminor;
        }
      349 /* window ::= frame_opt */
     => {
//line 1450 "src/parser/parse.y"
{
  yylhsminor = YYMINORTYPE::yy13( Window{ base: None,  partition_by: None, order_by: None, frame_clause: self.yy_move(0).yy4()});
}
  self[0].minor = yylhsminor;
        }
      350 /* window ::= nm frame_opt */
     => {
//line 1453 "src/parser/parse.y"
{
  yylhsminor = YYMINORTYPE::yy13( Window{ base: Some(self.yy_move(-1).yy222()),  partition_by: None, order_by: None, frame_clause: self.yy_move(0).yy4()});
}
  self[-1].minor = yylhsminor;
        }
      351 /* frame_opt ::= */
     => {
//line 1457 "src/parser/parse.y"
{
  self[1] .minor= YYMINORTYPE::yy4( None);
}
        }
      352 /* frame_opt ::= range_or_rows frame_bound_s frame_exclude_opt */
     => {
//line 1460 "src/parser/parse.y"
{
  yylhsminor = YYMINORTYPE::yy4( Some(FrameClause{ mode: self.yy_move(-2).yy221(), start: self.yy_move(-1).yy216(), end: None, exclude: self.yy_move(0).yy105() }));
}
  self[-2].minor = yylhsminor;
        }
      353 /* frame_opt ::= range_or_rows BETWEEN frame_bound_s AND frame_bound_e frame_exclude_opt */
     => {
//line 1464 "src/parser/parse.y"
{
  yylhsminor = YYMINORTYPE::yy4( Some(FrameClause{ mode: self.yy_move(-5).yy221(), start: self.yy_move(-3).yy216(), end: Some(self.yy_move(-1).yy216()), exclude: self.yy_move(0).yy105() }));
}
  self[-5].minor = yylhsminor;
        }
      354 /* range_or_rows ::= RANGE */
     => {
//line 1468 "src/parser/parse.y"
{ self[0] .minor= YYMINORTYPE::yy221( FrameMode::Range); }
        }
      355 /* range_or_rows ::= ROWS */
     => {
//line 1469 "src/parser/parse.y"
{ self[0] .minor= YYMINORTYPE::yy221( FrameMode::Rows); }
        }
      356 /* range_or_rows ::= GROUPS */
     => {
//line 1470 "src/parser/parse.y"
{ self[0] .minor= YYMINORTYPE::yy221( FrameMode::Groups); }
        }
      357 /* frame_bound_s ::= frame_bound */
     | 359 /* frame_bound_e ::= frame_bound */
     => {
//line 1473 "src/parser/parse.y"
{yylhsminor = YYMINORTYPE::yy216( self.yy_move(0).yy216());}
  self[0].minor = yylhsminor;
        }
      358 /* frame_bound_s ::= UNBOUNDED PRECEDING */
     => {
//line 1474 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy216( FrameBound::UnboundedPreceding);}
        }
      360 /* frame_bound_e ::= UNBOUNDED FOLLOWING */
     => {
//line 1476 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy216( FrameBound::UnboundedFollowing);}
        }
      361 /* frame_bound ::= expr PRECEDING */
     => {
//line 1478 "src/parser/parse.y"
{ yylhsminor = YYMINORTYPE::yy216( FrameBound::Preceding(self.yy_move(-1).yy480())); }
  self[-1].minor = yylhsminor;
        }
      362 /* frame_bound ::= CURRENT ROW */
     => {
//line 1479 "src/parser/parse.y"
{ self[-1] .minor= YYMINORTYPE::yy216( FrameBound::CurrentRow); }
        }
      363 /* frame_bound ::= expr FOLLOWING */
     => {
//line 1480 "src/parser/parse.y"
{ yylhsminor = YYMINORTYPE::yy216( FrameBound::Following(self.yy_move(-1).yy480())); }
  self[-1].minor = yylhsminor;
        }
      364 /* frame_exclude_opt ::= */
     => {
//line 1483 "src/parser/parse.y"
{self[1] .minor= YYMINORTYPE::yy105( None);}
        }
      365 /* frame_exclude_opt ::= EXCLUDE frame_exclude */
     => {
//line 1484 "src/parser/parse.y"
{self[-1] .minor= YYMINORTYPE::yy105( Some(self.yy_move(0).yy214()));}
        }
      366 /* frame_exclude ::= NO OTHERS */
     => {
//line 1487 "src/parser/parse.y"
{ self[-1] .minor= YYMINORTYPE::yy214( FrameExclude::NoOthers); }
        }
      367 /* frame_exclude ::= CURRENT ROW */
     => {
//line 1488 "src/parser/parse.y"
{ self[-1] .minor= YYMINORTYPE::yy214( FrameExclude::CurrentRow); }
        }
      368 /* frame_exclude ::= GROUP */
     => {
//line 1489 "src/parser/parse.y"
{ self[0] .minor= YYMINORTYPE::yy214( FrameExclude::Group); }
        }
      369 /* frame_exclude ::= TIES */
     => {
//line 1490 "src/parser/parse.y"
{ self[0] .minor= YYMINORTYPE::yy214( FrameExclude::Ties); }
        }
      370 /* window_clause ::= WINDOW windowdefn_list */
     => {
//line 1493 "src/parser/parse.y"
{ self[-1] .minor= YYMINORTYPE::yy472( self.yy_move(0).yy472()); }
        }
      371 /* filter_over ::= filter_clause over_clause */
     => {
//line 1495 "src/parser/parse.y"
{
  yylhsminor = YYMINORTYPE::yy353( FunctionTail{ filter_clause: Some(Box::new(self.yy_move(-1).yy480())), over_clause: Some(Box::new(self.yy_move(0).yy113())) });
}
  self[-1].minor = yylhsminor;
        }
      372 /* filter_over ::= over_clause */
     => {
//line 1498 "src/parser/parse.y"
{
  yylhsminor = YYMINORTYPE::yy353( FunctionTail{ filter_clause: None, over_clause: Some(Box::new(self.yy_move(0).yy113())) });
}
  self[0].minor = yylhsminor;
        }
      373 /* filter_over ::= filter_clause */
     => {
//line 1501 "src/parser/parse.y"
{
  yylhsminor = YYMINORTYPE::yy353( FunctionTail{ filter_clause: Some(Box::new(self.yy_move(0).yy480())), over_clause: None });
}
  self[0].minor = yylhsminor;
        }
      374 /* over_clause ::= OVER LP window RP */
     => {
//line 1505 "src/parser/parse.y"
{
  self[-3] .minor= YYMINORTYPE::yy113( Over::Window(self.yy_move(-1).yy13()));
}
        }
      375 /* over_clause ::= OVER nm */
     => {
//line 1508 "src/parser/parse.y"
{
  self[-1] .minor= YYMINORTYPE::yy113( Over::Name(self.yy_move(0).yy222()));
}
        }
      376 /* filter_clause ::= FILTER LP WHERE expr RP */
     => {
//line 1512 "src/parser/parse.y"
{ self[-4] .minor= YYMINORTYPE::yy480( self.yy_move(-1).yy480()); }
        }
      _ => {
      /* (377) input ::= cmdlist * debug_assert_eq!(yyruleno, 377); */
      /* (378) cmdlist ::= cmdlist ecmd * debug_assert_eq!(yyruleno, 378); */
      /* (379) cmdlist ::= ecmd (OPTIMIZED OUT) */ debug_assert_ne!(yyruleno, 379);
      /* (380) ecmd ::= SEMI * debug_assert_eq!(yyruleno, 380); */
      /* (381) ecmd ::= cmdx SEMI * debug_assert_eq!(yyruleno, 381); */
      /* (382) ecmd ::= explain cmdx SEMI (NEVER REDUCES) * debug_assert_ne!(yyruleno, 382); FIXME */
      /* (383) savepoint_opt ::= SAVEPOINT * debug_assert_eq!(yyruleno, 383); */
      /* (384) savepoint_opt ::= * debug_assert_eq!(yyruleno, 384); */
      /* (385) createkw ::= CREATE * debug_assert_eq!(yyruleno, 385); */
      /* (386) table_option_set ::= table_option (OPTIMIZED OUT) */ debug_assert_ne!(yyruleno, 386);
      /* (387) signed ::= plus_num (OPTIMIZED OUT) */ debug_assert_ne!(yyruleno, 387);
      /* (388) signed ::= minus_num (OPTIMIZED OUT) */ debug_assert_ne!(yyruleno, 388);
      /* (389) tconscomma ::= * debug_assert_eq!(yyruleno, 389); */
      /* (390) resolvetype ::= raisetype (OPTIMIZED OUT) */ debug_assert_ne!(yyruleno, 390);
      /* (391) sclp ::= selcollist COMMA * debug_assert_eq!(yyruleno, 391); */
      /* (392) expr ::= term (OPTIMIZED OUT) */ debug_assert_ne!(yyruleno, 392);
      /* (393) nmnum ::= plus_num (OPTIMIZED OUT) */ debug_assert_ne!(yyruleno, 393);
      /* (394) trnm ::= nm * debug_assert_eq!(yyruleno, 394); */
      /* (395) tridxby ::= * debug_assert_eq!(yyruleno, 395); */
      /* (396) database_kw_opt ::= DATABASE * debug_assert_eq!(yyruleno, 396); */
      /* (397) database_kw_opt ::= * debug_assert_eq!(yyruleno, 397); */
      /* (398) kwcolumn_opt ::= * debug_assert_eq!(yyruleno, 398); */
      /* (399) kwcolumn_opt ::= COLUMNKW * debug_assert_eq!(yyruleno, 399); */
      /* (400) vtabarglist ::= vtabarg * debug_assert_eq!(yyruleno, 400); */
      /* (401) vtabarglist ::= vtabarglist COMMA vtabarg * debug_assert_eq!(yyruleno, 401); */
      /* (402) vtabarg ::= vtabarg vtabargtoken * debug_assert_eq!(yyruleno, 402); */
      /* (403) anylist ::= * debug_assert_eq!(yyruleno, 403); */
      /* (404) anylist ::= anylist LP anylist RP * debug_assert_eq!(yyruleno, 404); */
      /* (405) anylist ::= anylist ANY * debug_assert_eq!(yyruleno, 405); */
        }
/********** End reduce actions ************************************************/
        };
        let yygoto: YYCODETYPE = yyRuleInfoLhs[yyruleno as usize]; /* The next state */
        let yysize: i8 = yyRuleInfoNRhs[yyruleno as usize];  /* Amount to pop the stack */
        let yyact: YYACTIONTYPE = yy_find_reduce_action(self[yysize].stateno, yygoto); /* The next action */

        /* There are no SHIFTREDUCE actions on nonterminals because the table
         ** generator has simplified them to pure REDUCE actions. */
        assert!(!(yyact > YY_MAX_SHIFT && yyact <= YY_MAX_SHIFTREDUCE));

        /* It is not possible for a REDUCE to be followed by an error */
        assert_ne!(yyact, YY_ERROR_ACTION);

        self.yyidx_shift(yysize + 1);
        {
            let yymsp = &mut self[0];
            yymsp.stateno = yyact;
            yymsp.major = yygoto;
        }
        self.yyTraceShift(yyact, "... then shift");
        Ok(yyact)
    }
}

/*
** The following code executes when the parse fails
*/
impl yyParser<'_> {
    #[cfg(not(feature = "YYNOERRORRECOVERY"))]
    fn yy_parse_failed(&mut self) {
        #[cfg(not(feature = "NDEBUG"))]
        {
            error!(target: TARGET, "Fail!");
        }
        while self.yyidx > 0 {
            self.yy_pop_parser_stack();
        }
        /* Here code is inserted which will be executed whenever the
         ** parser fails */
        /************ Begin %parse_failure code ***************************************/
        /************ End %parse_failure code *****************************************/
    }
    #[cfg(feature = "YYNOERRORRECOVERY")]
    fn yy_parse_failed(&mut self) {}
}

/*
** The following code executes when a syntax error first occurs.
*/
impl yyParser<'_> {
    fn yy_syntax_error(
        &mut self,
        yymajor: YYCODETYPE,              /* The major type of the error token */
        yyminor: &sqlite3ParserTOKENTYPE, /* The minor type of the error token */
    ) {
        /************ Begin %syntax_error code ****************************************/
//line 39 "src/parser/parse.y"

  if TokenType::TK_EOF as YYCODETYPE == yymajor {
    error!(target: TARGET, "incomplete input");
    self.ctx.error = Some(ParserError::UnexpectedEof);
  } else {
    error!(target: TARGET, "near {}, \"{:?}\": syntax error", yyTokenName[yymajor as usize], yyminor);
    self.ctx.error = Some(ParserError::SyntaxError {
        token_type: yyTokenName[yymajor as usize],
        found: yyminor.1.clone(),
    });
  }
        /************ End %syntax_error code ******************************************/
    }
}

/*
** The following is executed when the parser accepts
*/
impl yyParser<'_> {
    fn yy_accept(&mut self) {
        #[cfg(not(feature = "NDEBUG"))]
        {
            trace!(target: TARGET, "Accept!");
        }
        if cfg!(not(feature = "YYNOERRORRECOVERY")) {
            self.yyerrcnt = -1;
        }
        assert_eq!(self.yyidx, 0);
        /* Here code is inserted which will be executed whenever the
         ** parser accepts */
        /*********** Begin %parse_accept code *****************************************/
        /*********** End %parse_accept code *******************************************/
    }
}

/* The main parser program.
** The first argument is a pointer to a structure obtained from
** "sqlite3ParserAlloc" which describes the current state of the parser.
** The second argument is the major token number.  The third is
** the minor token.  The fourth optional argument is whatever the
** user wants (and specified in the grammar) and is available for
** use by the action routines.
**
** Inputs:
** <ul>
** <li> A pointer to the parser (an opaque structure.)
** <li> The major token number.
** <li> The minor token number.
** <li> An option argument of a grammar-specified type.
** </ul>
**
** Outputs:
** None.
*/
impl yyParser<'_> {
    #[allow(non_snake_case)]
    pub fn sqlite3Parser(
        &mut self,
        yymajor: TokenType,                  /* The major token code number */
        mut yyminor: sqlite3ParserTOKENTYPE, /* The value for the token */
    ) -> Result<(), sqlite3ParserError> {
        let mut yymajor = yymajor as YYCODETYPE;
        //#[cfg(all(not(feature = "YYERRORSYMBOL"), not(feature = "YYNOERRORRECOVERY")))]
        let mut yyendofinput: bool = false; /* True if we are at the end of input */
        //#[cfg(feature = "YYERRORSYMBOL")]
        let mut yyerrorhit: bool = false; /* True if yymajor has invoked an error */

        //assert_ne!( self[0], null );
        if YYERRORSYMBOL == 0 && cfg!(not(feature = "YYNOERRORRECOVERY")) {
            yyendofinput = yymajor == 0;
        }

        let mut yyact: YYACTIONTYPE = self[0].stateno; /* The parser action. */
        #[cfg(not(feature = "NDEBUG"))]
        {
            if yyact < YY_MIN_REDUCE {
                trace!(
                    target: TARGET,
                    "Input '{}' in state {}", yyTokenName[yymajor as usize], yyact
                );
            } else {
                trace!(
                    target: TARGET,
                    "Input '{}' with pending reduce {}",
                    yyTokenName[yymajor as usize],
                    yyact - YY_MIN_REDUCE
                );
            }
        }

        loop {
            assert_eq!(yyact, self[0].stateno);
            yyact = yy_find_shift_action(yymajor, yyact);
            if yyact >= YY_MIN_REDUCE {
                let yyruleno = yyact - YY_MIN_REDUCE; /* Reduce by this rule */
                #[cfg(not(feature = "NDEBUG"))]
                    {
                        assert!((yyruleno as usize) < yyRuleName.len());
                        let yysize = yyRuleInfoNRhs[yyruleno as usize];
                        let action = if yyruleno < YYNRULE_WITH_ACTION {
                            ""
                        } else {
                            " without external action"
                        };
                        if yysize != 0 {
                            trace!(
                                target: TARGET,
                                "Reduce {} [{}]{}, pop back to state {}.",
                                yyruleno,
                                yyRuleName[yyruleno as usize],
                                action,
                                self[yysize].stateno
                            );
                        } else {
                            trace!(
                                target: TARGET,
                                "Reduce {} [{}]{}.", yyruleno, yyRuleName[yyruleno as usize], action
                            );
                        }
                    }
                /* Check that the stack is large enough to grow by a single entry
                 ** if the RHS of the rule is empty.  This ensures that there is room
                 ** enough on the stack to push the LHS value */
                if yyRuleInfoNRhs[yyruleno as usize] == 0 {
                    self.yyhwm_incr();
                    if self.yy_grow_stack_for_push() {
                        break;
                    }
                }
                yyact = self.yy_reduce(yyruleno, yymajor, &yyminor)?;
            } else if yyact <= YY_MAX_SHIFTREDUCE {
                self.yy_shift(yyact, yymajor, yyminor.take());
                if cfg!(not(feature = "YYNOERRORRECOVERY")) {
                    self.yyerrcnt -= 1;
                }
                break;
            } else if yyact == YY_ACCEPT_ACTION {
                self.yyidx_shift(-1);
                self.yy_accept();
                return Ok(());
            } else {
                assert_eq!(yyact, YY_ERROR_ACTION);
                #[cfg(not(feature = "NDEBUG"))]
                {
                    trace!(target: TARGET, "Syntax Error!");
                }
                if YYERRORSYMBOL > 0 {
                    /* A syntax error has occurred.
                     ** The response to an error depends upon whether or not the
                     ** grammar defines an error token "ERROR".
                     **
                     ** This is what we do if the grammar does define ERROR:
                     **
                     **  * Call the %syntax_error function.
                     **
                     **  * Begin popping the stack until we enter a state where
                     **    it is legal to shift the error symbol, then shift
                     **    the error symbol.
                     **
                     **  * Set the error count to three.
                     **
                     **  * Begin accepting and shifting new tokens.  No new error
                     **    processing will occur until three tokens have been
                     **    shifted successfully.
                     **
                     */
                    if self.yyerrcnt < 0 {
                        self.yy_syntax_error(yymajor, &yyminor);
                    }
                    let yymx = self[0].major;
                    if yymx == YYERRORSYMBOL || yyerrorhit {
                        #[cfg(not(feature = "NDEBUG"))]
                        {
                            trace!(
                                target: TARGET,
                                "Discard input token {}", yyTokenName[yymajor as usize]
                            );
                        }
                        yymajor = YYNOCODE;
                    } else {
                        while self.yyidx > 0 {
                            yyact = yy_find_reduce_action(self[0].stateno, YYERRORSYMBOL);
                            if yyact <= YY_MAX_SHIFTREDUCE {
                                break;
                            }
                            self.yy_pop_parser_stack();
                        }
                        if self.yyidx <= 0 || yymajor == 0 {
                            self.yy_parse_failed();
                            if cfg!(not(feature = "YYNOERRORRECOVERY")) {
                                self.yyerrcnt = -1;
                            }
                            yymajor = YYNOCODE;
                        } else if yymx != YYERRORSYMBOL {
                            self.yy_shift(yyact, YYERRORSYMBOL, yyminor.take());
                        }
                    }
                    self.yyerrcnt = 3;
                    yyerrorhit = true;
                    if yymajor == YYNOCODE {
                        break;
                    }
                    yyact = self[0].stateno;
                } else if cfg!(feature = "YYNOERRORRECOVERY") {
                    /* If the YYNOERRORRECOVERY macro is defined, then do not attempt to
                     ** do any kind of error recovery.  Instead, simply invoke the syntax
                     ** error routine and continue going as if nothing had happened.
                     **
                     ** Applications can set this macro (for example inside %include) if
                     ** they intend to abandon the parse upon the first syntax error seen.
                     */
                    self.yy_syntax_error(yymajor, &yyminor);
                    break;
                } else {
                    /* YYERRORSYMBOL is not defined */
                    /* This is what we do if the grammar does not define ERROR:
                     **
                     **  * Report an error message, and throw away the input token.
                     **
                     **  * If the input token is $, then fail the parse.
                     **
                     ** As before, subsequent error messages are suppressed until
                     ** three input tokens have been successfully shifted.
                     */
                    if self.yyerrcnt <= 0 {
                        self.yy_syntax_error(yymajor, &yyminor);
                    }
                    self.yyerrcnt = 3;
                    if yyendofinput {
                        self.yy_parse_failed();
                        if cfg!(not(feature = "YYNOERRORRECOVERY")) {
                            self.yyerrcnt = -1;
                        }
                    }
                    break;
                }
            }
            if self.yyidx <= 0 {
                break;
            }
        }
        #[cfg(not(feature = "NDEBUG"))]
        {
            if log_enabled!(target: TARGET, Debug) {
                let msg = self.yystack[1..=self.yyidx]
                    .iter()
                    .map(|entry| yyTokenName[entry.major as usize])
                    .collect::<Vec<&str>>()
                    .join(" ");
                trace!(target: TARGET, "Return. Stack=[{}]", msg);
            }
        }
        return Ok(());
    }

    /*
     ** Return the fallback token corresponding to canonical token iToken, or
     ** 0 if iToken has no fallback.
     */
    pub fn parse_fallback(i_token: YYCODETYPE) -> YYCODETYPE {
        if YYFALLBACK {
            return yyFallback[i_token as usize];
        }
        0
    }
}
