{"rustc": 3062648155896360161, "features": "[]", "declared_features": "[\"__private_docs\", \"default\", \"form\", \"headers\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 12074263998246110377, "profile": 15657897354478470176, "path": 4342960589839802117, "deps": [[264090853244900308, "sync_wrapper", false, 8797264705234742812], [784494742817713399, "tower_service", false, 15712931872650505409], [1906322745568073236, "pin_project_lite", false, 6270632195488232726], [3601586811267292532, "tower", false, 5325913171506022586], [4405182208873388884, "http", false, 10314525203348988149], [6803352382179706244, "percent_encoding", false, 12292546504392908422], [7414427314941361239, "hyper", false, 17518961411716295968], [7695812897323945497, "itoa", false, 244585603876934921], [7712452662827335977, "tower_layer", false, 525772972084167676], [8915503303801890683, "http_body", false, 3165026523775083827], [9293824762099617471, "axum_core", false, 14087115976597436929], [9678799920983747518, "matchit", false, 10433341331932864038], [10229185211513642314, "mime", false, 17643779385371687159], [10435729446543529114, "bitflags", false, 12142199751111754387], [10629569228670356391, "futures_util", false, 6524679691479467602], [14851956875005785803, "serde", false, 11636289440761604009], [15932120279885307830, "memchr", false, 9127042864190067382], [16066129441945555748, "bytes", false, 8148076815200774068], [16244562316228021087, "build_script_build", false, 177244758078692035], [16611674984963787466, "async_trait", false, 11388465789347159649]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\axum-5e2f7dd7be5c338a\\dep-lib-axum", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}