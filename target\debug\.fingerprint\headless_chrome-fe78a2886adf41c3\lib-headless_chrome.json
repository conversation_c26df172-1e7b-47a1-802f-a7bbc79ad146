{"rustc": 3062648155896360161, "features": "[\"default\", \"offline\"]", "declared_features": "[\"default\", \"directories\", \"fetch\", \"native-tls\", \"nightly\", \"offline\", \"rustls\", \"ureq\", \"walkdir\", \"zip\"]", "target": 6765718376882695235, "profile": 15657897354478470176, "path": 9094339707360150481, "deps": [[503635761244294217, "regex", false, 13657818139005380310], [1852463361802237065, "anyhow", false, 8501704234503592444], [2186951887354108979, "which", false, 3398984457635711532], [4254813506600451364, "tungstenite", false, 3627751453479652823], [4352659168317596042, "tempfile", false, 3033050226290457303], [4537297827336760846, "thiserror", false, 2723179682894236067], [5404511084185685755, "url", false, 4759330461918687459], [6219554740863759696, "derive_builder", false, 3550066824083515099], [11916940916964035392, "rand", false, 2140904830931925307], [12832915883349295919, "serde_json", false, 18061623066020136761], [13066042571740262168, "log", false, 13344303879308748865], [13077212702700853852, "base64", false, 14897479820510372586], [14027278043718117867, "build_script_build", false, 12992320699231789136], [14851956875005785803, "serde", false, 11636289440761604009], [17735803425544754276, "winreg", false, 5994534223495471228]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\headless_chrome-fe78a2886adf41c3\\dep-lib-headless_chrome", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}