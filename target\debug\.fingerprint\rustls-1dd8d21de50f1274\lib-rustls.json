{"rustc": 3062648155896360161, "features": "[\"log\", \"logging\", \"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 16865373277404624699, "path": 6096654051730766952, "deps": [[2883436298747778685, "pki_types", false, 13477439550680226237], [3722963349756955755, "once_cell", false, 1159325417542651275], [5491919304041016563, "ring", false, 13205925803506095777], [6528079939221783635, "zeroize", false, 15614684255033312958], [10931517880218440090, "build_script_build", false, 11384502979281319206], [13016539751016587111, "<PERSON><PERSON><PERSON>", false, 4363738704913182305], [13066042571740262168, "log", false, 13344303879308748865], [17003143334332120809, "subtle", false, 15375928542258245401]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-1dd8d21de50f1274\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}