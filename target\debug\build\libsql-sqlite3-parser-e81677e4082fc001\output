OPT_LEVEL = Some(0)
TARGET = Some(x86_64-pc-windows-msvc)
OUT_DIR = Some(C:\Users\<USER>\Desktop\web-scrapper-rs\target\debug\build\libsql-sqlite3-parser-e81677e4082fc001\out)
cargo:rerun-if-env-changed=VCINSTALLDIR
VCINSTALLDIR = None
cargo:rerun-if-env-changed=VSTEL_MSBuildProjectFullPath
VSTEL_MSBuildProjectFullPath = None
cargo:rerun-if-env-changed=VSCMD_ARG_VCVARS_SPECTRE
VSCMD_ARG_VCVARS_SPECTRE = None
cargo:rerun-if-env-changed=WindowsSdkDir
WindowsSdkDir = None
cargo:rerun-if-env-changed=WindowsSDKVersion
WindowsSDKVersion = None
cargo:rerun-if-env-changed=LIB
LIB = None
PATH = Some(C:\Users\<USER>\Desktop\web-scrapper-rs\target\debug\deps;C:\Users\<USER>\Desktop\web-scrapper-rs\target\debug;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;C:\Program Files\PowerShell\7;C:\Program Files\Python313\Scripts\;C:\Program Files\Python313\;C:\Ruby33-x64\bin;C:\Program Files (x86)\VMware\VMware Workstation\bin\;C:\Program Files (x86)\Razer Chroma SDK\bin;C:\Program Files\Razer Chroma SDK\bin;C:\Program Files (x86)\Razer\ChromaBroadcast\bin;C:\Program Files\Razer\ChromaBroadcast\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\ProgramData\chocolatey\bin;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps;C:\Program Files\dotnet\;C:\Program Files\LOVE;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\LuaRocks;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Gradle\gradle-8.13\bin;C:\Program Files\WinMerge;C:\Program Files (x86)\cloudflared\;C:\Program Files\Git\cmd;C:\Program Files\PuTTY\;C:\Program Files\CMake\bin;C:\LLVM\bin;C:\Program Files\nodejs\;C:\Program Files\tracy\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\msys64\ucrt64\bin;C:\Program Files\Cloudflare\Cloudflare WARP\;C:\Program Files\PowerShell\7\;C:\Program Files\Go\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\JetBrains\IntelliJ IDEA 2022.2.1\bin;C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2022.2.1\bin;C:\Users\<USER>\go\bin;C:\Program Files\nim-1.6.8\bin;C:\Users\<USER>\.nimble\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\.deno\bin;C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2023.1\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe;C:\Program Files\JetBrains\RustRover 241.15989.101\bin;C:\Program Files (x86)\Nmap;C:\Users\<USER>\.bun\bin;C:\Users\<USER>\.nixpacks\bin;C:\Program Files\JetBrains\WebStorm 2024.2.4\bin;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\tools\dart-sdk\bin;C:\Users\<USER>\AppData\Local\Pub\Cache\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\SpacetimeDB;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Roaming\npm;D:\Apps;C:\Users\<USER>\go\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\bin)
cargo:rerun-if-env-changed=INCLUDE
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CC_x86_64-pc-windows-msvc
CC_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CC_x86_64_pc_windows_msvc
CC_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CC
HOST_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
DEBUG = Some(true)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
lemon.c
third_party\lemon\lemon.c(2897): warning C4244: '=': conversion from 'int' to 'char', possible loss of data
third_party\lemon\lemon.c(2936): warning C4996: 'fopen': This function or variable may be unsafe. Consider using fopen_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
third_party\lemon\lemon.c(3187): warning C4996: 'fopen': This function or variable may be unsafe. Consider using fopen_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
third_party\lemon\lemon.c(3505): warning C4996: 'getenv': This function or variable may be unsafe. Consider using _dupenv_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
third_party\lemon\lemon.c(3621): warning C4996: 'fopen': This function or variable may be unsafe. Consider using fopen_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
third_party\lemon\lemon.c(3650): warning C4996: 'fopen': This function or variable may be unsafe. Consider using fopen_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
third_party\lemon\lemon.c(3813): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
third_party\lemon\lemon.c(3816): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsql-sqlite3-parser-0.13.0\third_party\lemon\lemon.c(1789) : warning C4702: unreachable code
cargo:rerun-if-changed=third_party/lemon/lemon.c
cargo:rerun-if-changed=third_party/lemon/lempar.rs
cargo:rerun-if-changed=src/parser/parse.y
