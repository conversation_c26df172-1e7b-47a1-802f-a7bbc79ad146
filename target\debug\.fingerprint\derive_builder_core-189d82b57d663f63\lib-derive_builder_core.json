{"rustc": 3062648155896360161, "features": "[\"lib_has_std\"]", "declared_features": "[\"alloc\", \"clippy\", \"lib_has_std\"]", "target": 15805722739128704647, "profile": 2225463790103693989, "path": 14156286662762420055, "deps": [[373107762698212489, "proc_macro2", false, 16965594641470688371], [496455418292392305, "darling", false, 7636063048463130181], [17332570067994900305, "syn", false, 3020629385477819389], [17990358020177143287, "quote", false, 4770973289583101107]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\derive_builder_core-189d82b57d663f63\\dep-lib-derive_builder_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}