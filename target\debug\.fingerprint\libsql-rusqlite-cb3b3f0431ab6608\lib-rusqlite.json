{"rustc": 3062648155896360161, "features": "[\"column_decltype\", \"functions\", \"hooks\", \"libsql-experimental\", \"limits\", \"load_extension\", \"modern_sqlite\", \"trace\"]", "declared_features": "[\"array\", \"backup\", \"blob\", \"bundled\", \"chrono\", \"collation\", \"column_decltype\", \"csv\", \"csvtab\", \"extra_check\", \"functions\", \"hooks\", \"i128_blob\", \"libsql-experimental\", \"libsql-wasm-experimental\", \"limits\", \"load_extension\", \"modern-full\", \"modern_sqlite\", \"release_memory\", \"serde_json\", \"series\", \"session\", \"sqlcipher\", \"time\", \"trace\", \"unlock_notify\", \"url\", \"uuid\", \"vtab\", \"window\"]", "target": 1382081207361365847, "profile": 15657897354478470176, "path": 1905426578159958838, "deps": [[3405817021026194662, "hashlink", false, 16279416805071878220], [3666196340704888985, "smallvec", false, 12079267887756435790], [5510864063823219921, "fallible_streaming_iterator", false, 4005032442892814087], [6201363611984926128, "libsql_ffi", false, 17645891351183360060], [12848154260885479101, "bitflags", false, 1986056572718609536], [17725626451704002459, "fallible_iterator", false, 9130225165479160440]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\libsql-rusqlite-cb3b3f0431ab6608\\dep-lib-rusqlite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}