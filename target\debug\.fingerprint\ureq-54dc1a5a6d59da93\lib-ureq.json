{"rustc": 3062648155896360161, "features": "[\"default\", \"gzip\", \"socks-proxy\", \"tls\"]", "declared_features": "[\"brotli\", \"charset\", \"cookies\", \"default\", \"gzip\", \"http-crate\", \"http-interop\", \"json\", \"native-certs\", \"native-tls\", \"proxy-from-env\", \"socks-proxy\", \"testdeps\", \"tls\"]", "target": 2636997325719059094, "profile": 2225463790103693989, "path": 17798109830505810955, "deps": [[1129418674938548854, "socks", false, 17531133972650264582], [2883436298747778685, "rustls_pki_types", false, 13477439550680226237], [3722963349756955755, "once_cell", false, 1159325417542651275], [5404511084185685755, "url", false, 14332183246526066693], [8156804143951879168, "webpki_roots", false, 13111801084769707787], [10931517880218440090, "rustls", false, 5317182225374592455], [13066042571740262168, "log", false, 13344303879308748865], [13077212702700853852, "base64", false, 14897479820510372586], [17772299992546037086, "flate2", false, 10122264561879679491]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ureq-54dc1a5a6d59da93\\dep-lib-ureq", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}