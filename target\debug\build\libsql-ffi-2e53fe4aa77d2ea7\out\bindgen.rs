/* automatically generated by rust-bindgen 0.66.1 */

extern "C" {
    pub fn sqlite3_auto_extension(
        xEntryPoint: ::std::option::Option<
            unsafe extern "C" fn(
                db: *mut sqlite3,
                pzErrMsg: *mut *const ::std::os::raw::c_char,
                pThunk: *const sqlite3_api_routines,
            ) -> ::std::os::raw::c_int,
        >,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_cancel_auto_extension(
        xEntryPoint: ::std::option::Option<
            unsafe extern "C" fn(
                db: *mut sqlite3,
                pzErrMsg: *mut *const ::std::os::raw::c_char,
                pThunk: *const sqlite3_api_routines,
            ) -> ::std::os::raw::c_int,
        >,
    ) -> ::std::os::raw::c_int;
}

pub const __GNUC_VA_LIST: i32 = 1;
pub const SQLITE_VERSION: &[u8; 7] = b"3.45.1\0";
pub const SQLITE_VERSION_NUMBER: i32 = 3045001;
pub const SQLITE_SOURCE_ID: &[u8; 85] =
    b"2024-01-30 16:01:20 e876e51a0ed5c5b3126f52e532044363a014bc594cfefa87ffb5b82257ccalt1\0";
pub const LIBSQL_VERSION: &[u8; 6] = b"0.2.3\0";
pub const SQLITE_OK: i32 = 0;
pub const SQLITE_ERROR: i32 = 1;
pub const SQLITE_INTERNAL: i32 = 2;
pub const SQLITE_PERM: i32 = 3;
pub const SQLITE_ABORT: i32 = 4;
pub const SQLITE_BUSY: i32 = 5;
pub const SQLITE_LOCKED: i32 = 6;
pub const SQLITE_NOMEM: i32 = 7;
pub const SQLITE_READONLY: i32 = 8;
pub const SQLITE_INTERRUPT: i32 = 9;
pub const SQLITE_IOERR: i32 = 10;
pub const SQLITE_CORRUPT: i32 = 11;
pub const SQLITE_NOTFOUND: i32 = 12;
pub const SQLITE_FULL: i32 = 13;
pub const SQLITE_CANTOPEN: i32 = 14;
pub const SQLITE_PROTOCOL: i32 = 15;
pub const SQLITE_EMPTY: i32 = 16;
pub const SQLITE_SCHEMA: i32 = 17;
pub const SQLITE_TOOBIG: i32 = 18;
pub const SQLITE_CONSTRAINT: i32 = 19;
pub const SQLITE_MISMATCH: i32 = 20;
pub const SQLITE_MISUSE: i32 = 21;
pub const SQLITE_NOLFS: i32 = 22;
pub const SQLITE_AUTH: i32 = 23;
pub const SQLITE_FORMAT: i32 = 24;
pub const SQLITE_RANGE: i32 = 25;
pub const SQLITE_NOTADB: i32 = 26;
pub const SQLITE_NOTICE: i32 = 27;
pub const SQLITE_WARNING: i32 = 28;
pub const SQLITE_ROW: i32 = 100;
pub const SQLITE_DONE: i32 = 101;
pub const SQLITE_ERROR_MISSING_COLLSEQ: i32 = 257;
pub const SQLITE_ERROR_RETRY: i32 = 513;
pub const SQLITE_ERROR_SNAPSHOT: i32 = 769;
pub const SQLITE_IOERR_READ: i32 = 266;
pub const SQLITE_IOERR_SHORT_READ: i32 = 522;
pub const SQLITE_IOERR_WRITE: i32 = 778;
pub const SQLITE_IOERR_FSYNC: i32 = 1034;
pub const SQLITE_IOERR_DIR_FSYNC: i32 = 1290;
pub const SQLITE_IOERR_TRUNCATE: i32 = 1546;
pub const SQLITE_IOERR_FSTAT: i32 = 1802;
pub const SQLITE_IOERR_UNLOCK: i32 = 2058;
pub const SQLITE_IOERR_RDLOCK: i32 = 2314;
pub const SQLITE_IOERR_DELETE: i32 = 2570;
pub const SQLITE_IOERR_BLOCKED: i32 = 2826;
pub const SQLITE_IOERR_NOMEM: i32 = 3082;
pub const SQLITE_IOERR_ACCESS: i32 = 3338;
pub const SQLITE_IOERR_CHECKRESERVEDLOCK: i32 = 3594;
pub const SQLITE_IOERR_LOCK: i32 = 3850;
pub const SQLITE_IOERR_CLOSE: i32 = 4106;
pub const SQLITE_IOERR_DIR_CLOSE: i32 = 4362;
pub const SQLITE_IOERR_SHMOPEN: i32 = 4618;
pub const SQLITE_IOERR_SHMSIZE: i32 = 4874;
pub const SQLITE_IOERR_SHMLOCK: i32 = 5130;
pub const SQLITE_IOERR_SHMMAP: i32 = 5386;
pub const SQLITE_IOERR_SEEK: i32 = 5642;
pub const SQLITE_IOERR_DELETE_NOENT: i32 = 5898;
pub const SQLITE_IOERR_MMAP: i32 = 6154;
pub const SQLITE_IOERR_GETTEMPPATH: i32 = 6410;
pub const SQLITE_IOERR_CONVPATH: i32 = 6666;
pub const SQLITE_IOERR_VNODE: i32 = 6922;
pub const SQLITE_IOERR_AUTH: i32 = 7178;
pub const SQLITE_IOERR_BEGIN_ATOMIC: i32 = 7434;
pub const SQLITE_IOERR_COMMIT_ATOMIC: i32 = 7690;
pub const SQLITE_IOERR_ROLLBACK_ATOMIC: i32 = 7946;
pub const SQLITE_IOERR_DATA: i32 = 8202;
pub const SQLITE_IOERR_CORRUPTFS: i32 = 8458;
pub const SQLITE_IOERR_IN_PAGE: i32 = 8714;
pub const SQLITE_LOCKED_SHAREDCACHE: i32 = 262;
pub const SQLITE_LOCKED_VTAB: i32 = 518;
pub const SQLITE_BUSY_RECOVERY: i32 = 261;
pub const SQLITE_BUSY_SNAPSHOT: i32 = 517;
pub const SQLITE_BUSY_TIMEOUT: i32 = 773;
pub const SQLITE_CANTOPEN_NOTEMPDIR: i32 = 270;
pub const SQLITE_CANTOPEN_ISDIR: i32 = 526;
pub const SQLITE_CANTOPEN_FULLPATH: i32 = 782;
pub const SQLITE_CANTOPEN_CONVPATH: i32 = 1038;
pub const SQLITE_CANTOPEN_DIRTYWAL: i32 = 1294;
pub const SQLITE_CANTOPEN_SYMLINK: i32 = 1550;
pub const SQLITE_CORRUPT_VTAB: i32 = 267;
pub const SQLITE_CORRUPT_SEQUENCE: i32 = 523;
pub const SQLITE_CORRUPT_INDEX: i32 = 779;
pub const SQLITE_READONLY_RECOVERY: i32 = 264;
pub const SQLITE_READONLY_CANTLOCK: i32 = 520;
pub const SQLITE_READONLY_ROLLBACK: i32 = 776;
pub const SQLITE_READONLY_DBMOVED: i32 = 1032;
pub const SQLITE_READONLY_CANTINIT: i32 = 1288;
pub const SQLITE_READONLY_DIRECTORY: i32 = 1544;
pub const SQLITE_ABORT_ROLLBACK: i32 = 516;
pub const SQLITE_CONSTRAINT_CHECK: i32 = 275;
pub const SQLITE_CONSTRAINT_COMMITHOOK: i32 = 531;
pub const SQLITE_CONSTRAINT_FOREIGNKEY: i32 = 787;
pub const SQLITE_CONSTRAINT_FUNCTION: i32 = 1043;
pub const SQLITE_CONSTRAINT_NOTNULL: i32 = 1299;
pub const SQLITE_CONSTRAINT_PRIMARYKEY: i32 = 1555;
pub const SQLITE_CONSTRAINT_TRIGGER: i32 = 1811;
pub const SQLITE_CONSTRAINT_UNIQUE: i32 = 2067;
pub const SQLITE_CONSTRAINT_VTAB: i32 = 2323;
pub const SQLITE_CONSTRAINT_ROWID: i32 = 2579;
pub const SQLITE_CONSTRAINT_PINNED: i32 = 2835;
pub const SQLITE_CONSTRAINT_DATATYPE: i32 = 3091;
pub const SQLITE_NOTICE_RECOVER_WAL: i32 = 283;
pub const SQLITE_NOTICE_RECOVER_ROLLBACK: i32 = 539;
pub const SQLITE_NOTICE_RBU: i32 = 795;
pub const SQLITE_WARNING_AUTOINDEX: i32 = 284;
pub const SQLITE_AUTH_USER: i32 = 279;
pub const SQLITE_OK_LOAD_PERMANENTLY: i32 = 256;
pub const SQLITE_OK_SYMLINK: i32 = 512;
pub const SQLITE_OPEN_READONLY: i32 = 1;
pub const SQLITE_OPEN_READWRITE: i32 = 2;
pub const SQLITE_OPEN_CREATE: i32 = 4;
pub const SQLITE_OPEN_DELETEONCLOSE: i32 = 8;
pub const SQLITE_OPEN_EXCLUSIVE: i32 = 16;
pub const SQLITE_OPEN_AUTOPROXY: i32 = 32;
pub const SQLITE_OPEN_URI: i32 = 64;
pub const SQLITE_OPEN_MEMORY: i32 = 128;
pub const SQLITE_OPEN_MAIN_DB: i32 = 256;
pub const SQLITE_OPEN_TEMP_DB: i32 = 512;
pub const SQLITE_OPEN_TRANSIENT_DB: i32 = 1024;
pub const SQLITE_OPEN_MAIN_JOURNAL: i32 = 2048;
pub const SQLITE_OPEN_TEMP_JOURNAL: i32 = 4096;
pub const SQLITE_OPEN_SUBJOURNAL: i32 = 8192;
pub const SQLITE_OPEN_SUPER_JOURNAL: i32 = 16384;
pub const SQLITE_OPEN_NOMUTEX: i32 = 32768;
pub const SQLITE_OPEN_FULLMUTEX: i32 = 65536;
pub const SQLITE_OPEN_SHAREDCACHE: i32 = 131072;
pub const SQLITE_OPEN_PRIVATECACHE: i32 = 262144;
pub const SQLITE_OPEN_WAL: i32 = 524288;
pub const SQLITE_OPEN_NOFOLLOW: i32 = 16777216;
pub const SQLITE_OPEN_EXRESCODE: i32 = 33554432;
pub const SQLITE_OPEN_MASTER_JOURNAL: i32 = 16384;
pub const SQLITE_IOCAP_ATOMIC: i32 = 1;
pub const SQLITE_IOCAP_ATOMIC512: i32 = 2;
pub const SQLITE_IOCAP_ATOMIC1K: i32 = 4;
pub const SQLITE_IOCAP_ATOMIC2K: i32 = 8;
pub const SQLITE_IOCAP_ATOMIC4K: i32 = 16;
pub const SQLITE_IOCAP_ATOMIC8K: i32 = 32;
pub const SQLITE_IOCAP_ATOMIC16K: i32 = 64;
pub const SQLITE_IOCAP_ATOMIC32K: i32 = 128;
pub const SQLITE_IOCAP_ATOMIC64K: i32 = 256;
pub const SQLITE_IOCAP_SAFE_APPEND: i32 = 512;
pub const SQLITE_IOCAP_SEQUENTIAL: i32 = 1024;
pub const SQLITE_IOCAP_UNDELETABLE_WHEN_OPEN: i32 = 2048;
pub const SQLITE_IOCAP_POWERSAFE_OVERWRITE: i32 = 4096;
pub const SQLITE_IOCAP_IMMUTABLE: i32 = 8192;
pub const SQLITE_IOCAP_BATCH_ATOMIC: i32 = 16384;
pub const SQLITE_LOCK_NONE: i32 = 0;
pub const SQLITE_LOCK_SHARED: i32 = 1;
pub const SQLITE_LOCK_RESERVED: i32 = 2;
pub const SQLITE_LOCK_PENDING: i32 = 3;
pub const SQLITE_LOCK_EXCLUSIVE: i32 = 4;
pub const SQLITE_SYNC_NORMAL: i32 = 2;
pub const SQLITE_SYNC_FULL: i32 = 3;
pub const SQLITE_SYNC_DATAONLY: i32 = 16;
pub const SQLITE_FCNTL_LOCKSTATE: i32 = 1;
pub const SQLITE_FCNTL_GET_LOCKPROXYFILE: i32 = 2;
pub const SQLITE_FCNTL_SET_LOCKPROXYFILE: i32 = 3;
pub const SQLITE_FCNTL_LAST_ERRNO: i32 = 4;
pub const SQLITE_FCNTL_SIZE_HINT: i32 = 5;
pub const SQLITE_FCNTL_CHUNK_SIZE: i32 = 6;
pub const SQLITE_FCNTL_FILE_POINTER: i32 = 7;
pub const SQLITE_FCNTL_SYNC_OMITTED: i32 = 8;
pub const SQLITE_FCNTL_WIN32_AV_RETRY: i32 = 9;
pub const SQLITE_FCNTL_PERSIST_WAL: i32 = 10;
pub const SQLITE_FCNTL_OVERWRITE: i32 = 11;
pub const SQLITE_FCNTL_VFSNAME: i32 = 12;
pub const SQLITE_FCNTL_POWERSAFE_OVERWRITE: i32 = 13;
pub const SQLITE_FCNTL_PRAGMA: i32 = 14;
pub const SQLITE_FCNTL_BUSYHANDLER: i32 = 15;
pub const SQLITE_FCNTL_TEMPFILENAME: i32 = 16;
pub const SQLITE_FCNTL_MMAP_SIZE: i32 = 18;
pub const SQLITE_FCNTL_TRACE: i32 = 19;
pub const SQLITE_FCNTL_HAS_MOVED: i32 = 20;
pub const SQLITE_FCNTL_SYNC: i32 = 21;
pub const SQLITE_FCNTL_COMMIT_PHASETWO: i32 = 22;
pub const SQLITE_FCNTL_WIN32_SET_HANDLE: i32 = 23;
pub const SQLITE_FCNTL_WAL_BLOCK: i32 = 24;
pub const SQLITE_FCNTL_ZIPVFS: i32 = 25;
pub const SQLITE_FCNTL_RBU: i32 = 26;
pub const SQLITE_FCNTL_VFS_POINTER: i32 = 27;
pub const SQLITE_FCNTL_JOURNAL_POINTER: i32 = 28;
pub const SQLITE_FCNTL_WIN32_GET_HANDLE: i32 = 29;
pub const SQLITE_FCNTL_PDB: i32 = 30;
pub const SQLITE_FCNTL_BEGIN_ATOMIC_WRITE: i32 = 31;
pub const SQLITE_FCNTL_COMMIT_ATOMIC_WRITE: i32 = 32;
pub const SQLITE_FCNTL_ROLLBACK_ATOMIC_WRITE: i32 = 33;
pub const SQLITE_FCNTL_LOCK_TIMEOUT: i32 = 34;
pub const SQLITE_FCNTL_DATA_VERSION: i32 = 35;
pub const SQLITE_FCNTL_SIZE_LIMIT: i32 = 36;
pub const SQLITE_FCNTL_CKPT_DONE: i32 = 37;
pub const SQLITE_FCNTL_RESERVE_BYTES: i32 = 38;
pub const SQLITE_FCNTL_CKPT_START: i32 = 39;
pub const SQLITE_FCNTL_EXTERNAL_READER: i32 = 40;
pub const SQLITE_FCNTL_CKSM_FILE: i32 = 41;
pub const SQLITE_FCNTL_RESET_CACHE: i32 = 42;
pub const SQLITE_FCNTL_WAL_METHODS_POINTER: i32 = 129;
pub const SQLITE_GET_LOCKPROXYFILE: i32 = 2;
pub const SQLITE_SET_LOCKPROXYFILE: i32 = 3;
pub const SQLITE_LAST_ERRNO: i32 = 4;
pub const SQLITE_ACCESS_EXISTS: i32 = 0;
pub const SQLITE_ACCESS_READWRITE: i32 = 1;
pub const SQLITE_ACCESS_READ: i32 = 2;
pub const SQLITE_SHM_UNLOCK: i32 = 1;
pub const SQLITE_SHM_LOCK: i32 = 2;
pub const SQLITE_SHM_SHARED: i32 = 4;
pub const SQLITE_SHM_EXCLUSIVE: i32 = 8;
pub const SQLITE_SHM_NLOCK: i32 = 8;
pub const SQLITE_CONFIG_SINGLETHREAD: i32 = 1;
pub const SQLITE_CONFIG_MULTITHREAD: i32 = 2;
pub const SQLITE_CONFIG_SERIALIZED: i32 = 3;
pub const SQLITE_CONFIG_MALLOC: i32 = 4;
pub const SQLITE_CONFIG_GETMALLOC: i32 = 5;
pub const SQLITE_CONFIG_SCRATCH: i32 = 6;
pub const SQLITE_CONFIG_PAGECACHE: i32 = 7;
pub const SQLITE_CONFIG_HEAP: i32 = 8;
pub const SQLITE_CONFIG_MEMSTATUS: i32 = 9;
pub const SQLITE_CONFIG_MUTEX: i32 = 10;
pub const SQLITE_CONFIG_GETMUTEX: i32 = 11;
pub const SQLITE_CONFIG_LOOKASIDE: i32 = 13;
pub const SQLITE_CONFIG_PCACHE: i32 = 14;
pub const SQLITE_CONFIG_GETPCACHE: i32 = 15;
pub const SQLITE_CONFIG_LOG: i32 = 16;
pub const SQLITE_CONFIG_URI: i32 = 17;
pub const SQLITE_CONFIG_PCACHE2: i32 = 18;
pub const SQLITE_CONFIG_GETPCACHE2: i32 = 19;
pub const SQLITE_CONFIG_COVERING_INDEX_SCAN: i32 = 20;
pub const SQLITE_CONFIG_SQLLOG: i32 = 21;
pub const SQLITE_CONFIG_MMAP_SIZE: i32 = 22;
pub const SQLITE_CONFIG_WIN32_HEAPSIZE: i32 = 23;
pub const SQLITE_CONFIG_PCACHE_HDRSZ: i32 = 24;
pub const SQLITE_CONFIG_PMASZ: i32 = 25;
pub const SQLITE_CONFIG_STMTJRNL_SPILL: i32 = 26;
pub const SQLITE_CONFIG_SMALL_MALLOC: i32 = 27;
pub const SQLITE_CONFIG_SORTERREF_SIZE: i32 = 28;
pub const SQLITE_CONFIG_MEMDB_MAXSIZE: i32 = 29;
pub const SQLITE_DBCONFIG_MAINDBNAME: i32 = 1000;
pub const SQLITE_DBCONFIG_LOOKASIDE: i32 = 1001;
pub const SQLITE_DBCONFIG_ENABLE_FKEY: i32 = 1002;
pub const SQLITE_DBCONFIG_ENABLE_TRIGGER: i32 = 1003;
pub const SQLITE_DBCONFIG_ENABLE_FTS3_TOKENIZER: i32 = 1004;
pub const SQLITE_DBCONFIG_ENABLE_LOAD_EXTENSION: i32 = 1005;
pub const SQLITE_DBCONFIG_NO_CKPT_ON_CLOSE: i32 = 1006;
pub const SQLITE_DBCONFIG_ENABLE_QPSG: i32 = 1007;
pub const SQLITE_DBCONFIG_TRIGGER_EQP: i32 = 1008;
pub const SQLITE_DBCONFIG_RESET_DATABASE: i32 = 1009;
pub const SQLITE_DBCONFIG_DEFENSIVE: i32 = 1010;
pub const SQLITE_DBCONFIG_WRITABLE_SCHEMA: i32 = 1011;
pub const SQLITE_DBCONFIG_LEGACY_ALTER_TABLE: i32 = 1012;
pub const SQLITE_DBCONFIG_DQS_DML: i32 = 1013;
pub const SQLITE_DBCONFIG_DQS_DDL: i32 = 1014;
pub const SQLITE_DBCONFIG_ENABLE_VIEW: i32 = 1015;
pub const SQLITE_DBCONFIG_LEGACY_FILE_FORMAT: i32 = 1016;
pub const SQLITE_DBCONFIG_TRUSTED_SCHEMA: i32 = 1017;
pub const SQLITE_DBCONFIG_STMT_SCANSTATUS: i32 = 1018;
pub const SQLITE_DBCONFIG_REVERSE_SCANORDER: i32 = 1019;
pub const SQLITE_DBCONFIG_MAX: i32 = 1019;
pub const SQLITE_DENY: i32 = 1;
pub const SQLITE_IGNORE: i32 = 2;
pub const SQLITE_CREATE_INDEX: i32 = 1;
pub const SQLITE_CREATE_TABLE: i32 = 2;
pub const SQLITE_CREATE_TEMP_INDEX: i32 = 3;
pub const SQLITE_CREATE_TEMP_TABLE: i32 = 4;
pub const SQLITE_CREATE_TEMP_TRIGGER: i32 = 5;
pub const SQLITE_CREATE_TEMP_VIEW: i32 = 6;
pub const SQLITE_CREATE_TRIGGER: i32 = 7;
pub const SQLITE_CREATE_VIEW: i32 = 8;
pub const SQLITE_DELETE: i32 = 9;
pub const SQLITE_DROP_INDEX: i32 = 10;
pub const SQLITE_DROP_TABLE: i32 = 11;
pub const SQLITE_DROP_TEMP_INDEX: i32 = 12;
pub const SQLITE_DROP_TEMP_TABLE: i32 = 13;
pub const SQLITE_DROP_TEMP_TRIGGER: i32 = 14;
pub const SQLITE_DROP_TEMP_VIEW: i32 = 15;
pub const SQLITE_DROP_TRIGGER: i32 = 16;
pub const SQLITE_DROP_VIEW: i32 = 17;
pub const SQLITE_INSERT: i32 = 18;
pub const SQLITE_PRAGMA: i32 = 19;
pub const SQLITE_READ: i32 = 20;
pub const SQLITE_SELECT: i32 = 21;
pub const SQLITE_TRANSACTION: i32 = 22;
pub const SQLITE_UPDATE: i32 = 23;
pub const SQLITE_ATTACH: i32 = 24;
pub const SQLITE_DETACH: i32 = 25;
pub const SQLITE_ALTER_TABLE: i32 = 26;
pub const SQLITE_REINDEX: i32 = 27;
pub const SQLITE_ANALYZE: i32 = 28;
pub const SQLITE_CREATE_VTABLE: i32 = 29;
pub const SQLITE_DROP_VTABLE: i32 = 30;
pub const SQLITE_FUNCTION: i32 = 31;
pub const SQLITE_SAVEPOINT: i32 = 32;
pub const SQLITE_COPY: i32 = 0;
pub const SQLITE_RECURSIVE: i32 = 33;
pub const SQLITE_TRACE_STMT: i32 = 1;
pub const SQLITE_TRACE_PROFILE: i32 = 2;
pub const SQLITE_TRACE_ROW: i32 = 4;
pub const SQLITE_TRACE_CLOSE: i32 = 8;
pub const SQLITE_LIMIT_LENGTH: i32 = 0;
pub const SQLITE_LIMIT_SQL_LENGTH: i32 = 1;
pub const SQLITE_LIMIT_COLUMN: i32 = 2;
pub const SQLITE_LIMIT_EXPR_DEPTH: i32 = 3;
pub const SQLITE_LIMIT_COMPOUND_SELECT: i32 = 4;
pub const SQLITE_LIMIT_VDBE_OP: i32 = 5;
pub const SQLITE_LIMIT_FUNCTION_ARG: i32 = 6;
pub const SQLITE_LIMIT_ATTACHED: i32 = 7;
pub const SQLITE_LIMIT_LIKE_PATTERN_LENGTH: i32 = 8;
pub const SQLITE_LIMIT_VARIABLE_NUMBER: i32 = 9;
pub const SQLITE_LIMIT_TRIGGER_DEPTH: i32 = 10;
pub const SQLITE_LIMIT_WORKER_THREADS: i32 = 11;
pub const SQLITE_PREPARE_PERSISTENT: i32 = 1;
pub const SQLITE_PREPARE_NORMALIZE: i32 = 2;
pub const SQLITE_PREPARE_NO_VTAB: i32 = 4;
pub const SQLITE_INTEGER: i32 = 1;
pub const SQLITE_FLOAT: i32 = 2;
pub const SQLITE_BLOB: i32 = 4;
pub const SQLITE_NULL: i32 = 5;
pub const SQLITE_TEXT: i32 = 3;
pub const SQLITE3_TEXT: i32 = 3;
pub const SQLITE_UTF8: i32 = 1;
pub const SQLITE_UTF16LE: i32 = 2;
pub const SQLITE_UTF16BE: i32 = 3;
pub const SQLITE_UTF16: i32 = 4;
pub const SQLITE_ANY: i32 = 5;
pub const SQLITE_UTF16_ALIGNED: i32 = 8;
pub const SQLITE_DETERMINISTIC: i32 = 2048;
pub const SQLITE_DIRECTONLY: i32 = 524288;
pub const SQLITE_SUBTYPE: i32 = 1048576;
pub const SQLITE_INNOCUOUS: i32 = 2097152;
pub const SQLITE_RESULT_SUBTYPE: i32 = 16777216;
pub const SQLITE_WIN32_DATA_DIRECTORY_TYPE: i32 = 1;
pub const SQLITE_WIN32_TEMP_DIRECTORY_TYPE: i32 = 2;
pub const SQLITE_TXN_NONE: i32 = 0;
pub const SQLITE_TXN_READ: i32 = 1;
pub const SQLITE_TXN_WRITE: i32 = 2;
pub const SQLITE_INDEX_SCAN_UNIQUE: i32 = 1;
pub const SQLITE_INDEX_CONSTRAINT_EQ: i32 = 2;
pub const SQLITE_INDEX_CONSTRAINT_GT: i32 = 4;
pub const SQLITE_INDEX_CONSTRAINT_LE: i32 = 8;
pub const SQLITE_INDEX_CONSTRAINT_LT: i32 = 16;
pub const SQLITE_INDEX_CONSTRAINT_GE: i32 = 32;
pub const SQLITE_INDEX_CONSTRAINT_MATCH: i32 = 64;
pub const SQLITE_INDEX_CONSTRAINT_LIKE: i32 = 65;
pub const SQLITE_INDEX_CONSTRAINT_GLOB: i32 = 66;
pub const SQLITE_INDEX_CONSTRAINT_REGEXP: i32 = 67;
pub const SQLITE_INDEX_CONSTRAINT_NE: i32 = 68;
pub const SQLITE_INDEX_CONSTRAINT_ISNOT: i32 = 69;
pub const SQLITE_INDEX_CONSTRAINT_ISNOTNULL: i32 = 70;
pub const SQLITE_INDEX_CONSTRAINT_ISNULL: i32 = 71;
pub const SQLITE_INDEX_CONSTRAINT_IS: i32 = 72;
pub const SQLITE_INDEX_CONSTRAINT_LIMIT: i32 = 73;
pub const SQLITE_INDEX_CONSTRAINT_OFFSET: i32 = 74;
pub const SQLITE_INDEX_CONSTRAINT_FUNCTION: i32 = 150;
pub const SQLITE_MUTEX_FAST: i32 = 0;
pub const SQLITE_MUTEX_RECURSIVE: i32 = 1;
pub const SQLITE_MUTEX_STATIC_MAIN: i32 = 2;
pub const SQLITE_MUTEX_STATIC_MEM: i32 = 3;
pub const SQLITE_MUTEX_STATIC_MEM2: i32 = 4;
pub const SQLITE_MUTEX_STATIC_OPEN: i32 = 4;
pub const SQLITE_MUTEX_STATIC_PRNG: i32 = 5;
pub const SQLITE_MUTEX_STATIC_LRU: i32 = 6;
pub const SQLITE_MUTEX_STATIC_LRU2: i32 = 7;
pub const SQLITE_MUTEX_STATIC_PMEM: i32 = 7;
pub const SQLITE_MUTEX_STATIC_APP1: i32 = 8;
pub const SQLITE_MUTEX_STATIC_APP2: i32 = 9;
pub const SQLITE_MUTEX_STATIC_APP3: i32 = 10;
pub const SQLITE_MUTEX_STATIC_VFS1: i32 = 11;
pub const SQLITE_MUTEX_STATIC_VFS2: i32 = 12;
pub const SQLITE_MUTEX_STATIC_VFS3: i32 = 13;
pub const SQLITE_MUTEX_STATIC_MASTER: i32 = 2;
pub const SQLITE_TESTCTRL_FIRST: i32 = 5;
pub const SQLITE_TESTCTRL_PRNG_SAVE: i32 = 5;
pub const SQLITE_TESTCTRL_PRNG_RESTORE: i32 = 6;
pub const SQLITE_TESTCTRL_PRNG_RESET: i32 = 7;
pub const SQLITE_TESTCTRL_FK_NO_ACTION: i32 = 7;
pub const SQLITE_TESTCTRL_BITVEC_TEST: i32 = 8;
pub const SQLITE_TESTCTRL_FAULT_INSTALL: i32 = 9;
pub const SQLITE_TESTCTRL_BENIGN_MALLOC_HOOKS: i32 = 10;
pub const SQLITE_TESTCTRL_PENDING_BYTE: i32 = 11;
pub const SQLITE_TESTCTRL_ASSERT: i32 = 12;
pub const SQLITE_TESTCTRL_ALWAYS: i32 = 13;
pub const SQLITE_TESTCTRL_RESERVE: i32 = 14;
pub const SQLITE_TESTCTRL_JSON_SELFCHECK: i32 = 14;
pub const SQLITE_TESTCTRL_OPTIMIZATIONS: i32 = 15;
pub const SQLITE_TESTCTRL_ISKEYWORD: i32 = 16;
pub const SQLITE_TESTCTRL_SCRATCHMALLOC: i32 = 17;
pub const SQLITE_TESTCTRL_INTERNAL_FUNCTIONS: i32 = 17;
pub const SQLITE_TESTCTRL_LOCALTIME_FAULT: i32 = 18;
pub const SQLITE_TESTCTRL_EXPLAIN_STMT: i32 = 19;
pub const SQLITE_TESTCTRL_ONCE_RESET_THRESHOLD: i32 = 19;
pub const SQLITE_TESTCTRL_NEVER_CORRUPT: i32 = 20;
pub const SQLITE_TESTCTRL_VDBE_COVERAGE: i32 = 21;
pub const SQLITE_TESTCTRL_BYTEORDER: i32 = 22;
pub const SQLITE_TESTCTRL_ISINIT: i32 = 23;
pub const SQLITE_TESTCTRL_SORTER_MMAP: i32 = 24;
pub const SQLITE_TESTCTRL_IMPOSTER: i32 = 25;
pub const SQLITE_TESTCTRL_PARSER_COVERAGE: i32 = 26;
pub const SQLITE_TESTCTRL_RESULT_INTREAL: i32 = 27;
pub const SQLITE_TESTCTRL_PRNG_SEED: i32 = 28;
pub const SQLITE_TESTCTRL_EXTRA_SCHEMA_CHECKS: i32 = 29;
pub const SQLITE_TESTCTRL_SEEK_COUNT: i32 = 30;
pub const SQLITE_TESTCTRL_TRACEFLAGS: i32 = 31;
pub const SQLITE_TESTCTRL_TUNE: i32 = 32;
pub const SQLITE_TESTCTRL_LOGEST: i32 = 33;
pub const SQLITE_TESTCTRL_USELONGDOUBLE: i32 = 34;
pub const SQLITE_TESTCTRL_LAST: i32 = 34;
pub const SQLITE_STATUS_MEMORY_USED: i32 = 0;
pub const SQLITE_STATUS_PAGECACHE_USED: i32 = 1;
pub const SQLITE_STATUS_PAGECACHE_OVERFLOW: i32 = 2;
pub const SQLITE_STATUS_SCRATCH_USED: i32 = 3;
pub const SQLITE_STATUS_SCRATCH_OVERFLOW: i32 = 4;
pub const SQLITE_STATUS_MALLOC_SIZE: i32 = 5;
pub const SQLITE_STATUS_PARSER_STACK: i32 = 6;
pub const SQLITE_STATUS_PAGECACHE_SIZE: i32 = 7;
pub const SQLITE_STATUS_SCRATCH_SIZE: i32 = 8;
pub const SQLITE_STATUS_MALLOC_COUNT: i32 = 9;
pub const SQLITE_DBSTATUS_LOOKASIDE_USED: i32 = 0;
pub const SQLITE_DBSTATUS_CACHE_USED: i32 = 1;
pub const SQLITE_DBSTATUS_SCHEMA_USED: i32 = 2;
pub const SQLITE_DBSTATUS_STMT_USED: i32 = 3;
pub const SQLITE_DBSTATUS_LOOKASIDE_HIT: i32 = 4;
pub const SQLITE_DBSTATUS_LOOKASIDE_MISS_SIZE: i32 = 5;
pub const SQLITE_DBSTATUS_LOOKASIDE_MISS_FULL: i32 = 6;
pub const SQLITE_DBSTATUS_CACHE_HIT: i32 = 7;
pub const SQLITE_DBSTATUS_CACHE_MISS: i32 = 8;
pub const SQLITE_DBSTATUS_CACHE_WRITE: i32 = 9;
pub const SQLITE_DBSTATUS_DEFERRED_FKS: i32 = 10;
pub const SQLITE_DBSTATUS_CACHE_USED_SHARED: i32 = 11;
pub const SQLITE_DBSTATUS_CACHE_SPILL: i32 = 12;
pub const SQLITE_DBSTATUS_MAX: i32 = 12;
pub const SQLITE_STMTSTATUS_FULLSCAN_STEP: i32 = 1;
pub const SQLITE_STMTSTATUS_SORT: i32 = 2;
pub const SQLITE_STMTSTATUS_AUTOINDEX: i32 = 3;
pub const SQLITE_STMTSTATUS_VM_STEP: i32 = 4;
pub const SQLITE_STMTSTATUS_REPREPARE: i32 = 5;
pub const SQLITE_STMTSTATUS_RUN: i32 = 6;
pub const SQLITE_STMTSTATUS_FILTER_MISS: i32 = 7;
pub const SQLITE_STMTSTATUS_FILTER_HIT: i32 = 8;
pub const SQLITE_STMTSTATUS_MEMUSED: i32 = 99;
pub const LIBSQL_STMTSTATUS_BASE: i32 = 1024;
pub const LIBSQL_STMTSTATUS_ROWS_READ: i32 = 1025;
pub const LIBSQL_STMTSTATUS_ROWS_WRITTEN: i32 = 1026;
pub const SQLITE_CHECKPOINT_PASSIVE: i32 = 0;
pub const SQLITE_CHECKPOINT_FULL: i32 = 1;
pub const SQLITE_CHECKPOINT_RESTART: i32 = 2;
pub const SQLITE_CHECKPOINT_TRUNCATE: i32 = 3;
pub const SQLITE_VTAB_CONSTRAINT_SUPPORT: i32 = 1;
pub const SQLITE_VTAB_INNOCUOUS: i32 = 2;
pub const SQLITE_VTAB_DIRECTONLY: i32 = 3;
pub const SQLITE_VTAB_USES_ALL_SCHEMAS: i32 = 4;
pub const SQLITE_ROLLBACK: i32 = 1;
pub const SQLITE_FAIL: i32 = 3;
pub const SQLITE_REPLACE: i32 = 5;
pub const SQLITE_SCANSTAT_NLOOP: i32 = 0;
pub const SQLITE_SCANSTAT_NVISIT: i32 = 1;
pub const SQLITE_SCANSTAT_EST: i32 = 2;
pub const SQLITE_SCANSTAT_NAME: i32 = 3;
pub const SQLITE_SCANSTAT_EXPLAIN: i32 = 4;
pub const SQLITE_SCANSTAT_SELECTID: i32 = 5;
pub const SQLITE_SCANSTAT_PARENTID: i32 = 6;
pub const SQLITE_SCANSTAT_NCYCLE: i32 = 7;
pub const SQLITE_SCANSTAT_COMPLEX: i32 = 1;
pub const SQLITE_SERIALIZE_NOCOPY: i32 = 1;
pub const SQLITE_DESERIALIZE_FREEONCLOSE: i32 = 1;
pub const SQLITE_DESERIALIZE_RESIZEABLE: i32 = 2;
pub const SQLITE_DESERIALIZE_READONLY: i32 = 4;
pub const NOT_WITHIN: i32 = 0;
pub const PARTLY_WITHIN: i32 = 1;
pub const FULLY_WITHIN: i32 = 2;
pub const FTS5_TOKENIZE_QUERY: i32 = 1;
pub const FTS5_TOKENIZE_PREFIX: i32 = 2;
pub const FTS5_TOKENIZE_DOCUMENT: i32 = 4;
pub const FTS5_TOKENIZE_AUX: i32 = 8;
pub const FTS5_TOKEN_COLOCATED: i32 = 1;
pub const WAL_SAVEPOINT_NDATA: i32 = 4;
pub type va_list = __builtin_va_list;
pub type __gnuc_va_list = __builtin_va_list;
extern "C" {
    pub static sqlite3_version: [::std::os::raw::c_char; 0usize];
}
extern "C" {
    pub fn sqlite3_libversion() -> *const ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_sourceid() -> *const ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_libversion_number() -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn libsql_libversion() -> *const ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_compileoption_used(
        zOptName: *const ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_compileoption_get(N: ::std::os::raw::c_int) -> *const ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_threadsafe() -> ::std::os::raw::c_int;
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3 {
    _unused: [u8; 0],
}
pub type sqlite_int64 = ::std::os::raw::c_longlong;
pub type sqlite_uint64 = ::std::os::raw::c_ulonglong;
pub type sqlite3_int64 = sqlite_int64;
pub type sqlite3_uint64 = sqlite_uint64;
extern "C" {
    pub fn sqlite3_close(arg1: *mut sqlite3) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_close_v2(arg1: *mut sqlite3) -> ::std::os::raw::c_int;
}
pub type sqlite3_callback = ::std::option::Option<
    unsafe extern "C" fn(
        arg1: *mut ::std::os::raw::c_void,
        arg2: ::std::os::raw::c_int,
        arg3: *mut *mut ::std::os::raw::c_char,
        arg4: *mut *mut ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int,
>;
extern "C" {
    pub fn sqlite3_exec(
        arg1: *mut sqlite3,
        sql: *const ::std::os::raw::c_char,
        callback: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut ::std::os::raw::c_void,
                arg2: ::std::os::raw::c_int,
                arg3: *mut *mut ::std::os::raw::c_char,
                arg4: *mut *mut ::std::os::raw::c_char,
            ) -> ::std::os::raw::c_int,
        >,
        arg2: *mut ::std::os::raw::c_void,
        errmsg: *mut *mut ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int;
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_file {
    pub pMethods: *const sqlite3_io_methods,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_io_methods {
    pub iVersion: ::std::os::raw::c_int,
    pub xClose: ::std::option::Option<
        unsafe extern "C" fn(arg1: *mut sqlite3_file) -> ::std::os::raw::c_int,
    >,
    pub xRead: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_file,
            arg2: *mut ::std::os::raw::c_void,
            iAmt: ::std::os::raw::c_int,
            iOfst: sqlite3_int64,
        ) -> ::std::os::raw::c_int,
    >,
    pub xWrite: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_file,
            arg2: *const ::std::os::raw::c_void,
            iAmt: ::std::os::raw::c_int,
            iOfst: sqlite3_int64,
        ) -> ::std::os::raw::c_int,
    >,
    pub xTruncate: ::std::option::Option<
        unsafe extern "C" fn(arg1: *mut sqlite3_file, size: sqlite3_int64) -> ::std::os::raw::c_int,
    >,
    pub xSync: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_file,
            flags: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub xFileSize: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_file,
            pSize: *mut sqlite3_int64,
        ) -> ::std::os::raw::c_int,
    >,
    pub xLock: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_file,
            arg2: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub xUnlock: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_file,
            arg2: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub xCheckReservedLock: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_file,
            pResOut: *mut ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub xFileControl: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_file,
            op: ::std::os::raw::c_int,
            pArg: *mut ::std::os::raw::c_void,
        ) -> ::std::os::raw::c_int,
    >,
    pub xSectorSize: ::std::option::Option<
        unsafe extern "C" fn(arg1: *mut sqlite3_file) -> ::std::os::raw::c_int,
    >,
    pub xDeviceCharacteristics: ::std::option::Option<
        unsafe extern "C" fn(arg1: *mut sqlite3_file) -> ::std::os::raw::c_int,
    >,
    pub xShmMap: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_file,
            iPg: ::std::os::raw::c_int,
            pgsz: ::std::os::raw::c_int,
            arg2: ::std::os::raw::c_int,
            arg3: *mut *mut ::std::os::raw::c_void,
        ) -> ::std::os::raw::c_int,
    >,
    pub xShmLock: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_file,
            offset: ::std::os::raw::c_int,
            n: ::std::os::raw::c_int,
            flags: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub xShmBarrier: ::std::option::Option<unsafe extern "C" fn(arg1: *mut sqlite3_file)>,
    pub xShmUnmap: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_file,
            deleteFlag: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub xFetch: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_file,
            iOfst: sqlite3_int64,
            iAmt: ::std::os::raw::c_int,
            pp: *mut *mut ::std::os::raw::c_void,
        ) -> ::std::os::raw::c_int,
    >,
    pub xUnfetch: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_file,
            iOfst: sqlite3_int64,
            p: *mut ::std::os::raw::c_void,
        ) -> ::std::os::raw::c_int,
    >,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_mutex {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_api_routines {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct libsql_api_routines {
    _unused: [u8; 0],
}
pub type sqlite3_filename = *const ::std::os::raw::c_char;
pub type sqlite3_syscall_ptr = ::std::option::Option<unsafe extern "C" fn()>;
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_vfs {
    pub iVersion: ::std::os::raw::c_int,
    pub szOsFile: ::std::os::raw::c_int,
    pub mxPathname: ::std::os::raw::c_int,
    pub pNext: *mut sqlite3_vfs,
    pub zName: *const ::std::os::raw::c_char,
    pub pAppData: *mut ::std::os::raw::c_void,
    pub xOpen: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_vfs,
            zName: sqlite3_filename,
            arg2: *mut sqlite3_file,
            flags: ::std::os::raw::c_int,
            pOutFlags: *mut ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub xDelete: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_vfs,
            zName: *const ::std::os::raw::c_char,
            syncDir: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub xAccess: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_vfs,
            zName: *const ::std::os::raw::c_char,
            flags: ::std::os::raw::c_int,
            pResOut: *mut ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub xFullPathname: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_vfs,
            zName: *const ::std::os::raw::c_char,
            nOut: ::std::os::raw::c_int,
            zOut: *mut ::std::os::raw::c_char,
        ) -> ::std::os::raw::c_int,
    >,
    pub xDlOpen: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_vfs,
            zFilename: *const ::std::os::raw::c_char,
        ) -> *mut ::std::os::raw::c_void,
    >,
    pub xDlError: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_vfs,
            nByte: ::std::os::raw::c_int,
            zErrMsg: *mut ::std::os::raw::c_char,
        ),
    >,
    pub xDlSym: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_vfs,
            arg2: *mut ::std::os::raw::c_void,
            zSymbol: *const ::std::os::raw::c_char,
        ) -> ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut sqlite3_vfs,
                arg2: *mut ::std::os::raw::c_void,
                zSymbol: *const ::std::os::raw::c_char,
            ),
        >,
    >,
    pub xDlClose: ::std::option::Option<
        unsafe extern "C" fn(arg1: *mut sqlite3_vfs, arg2: *mut ::std::os::raw::c_void),
    >,
    pub xRandomness: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_vfs,
            nByte: ::std::os::raw::c_int,
            zOut: *mut ::std::os::raw::c_char,
        ) -> ::std::os::raw::c_int,
    >,
    pub xSleep: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_vfs,
            microseconds: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub xCurrentTime: ::std::option::Option<
        unsafe extern "C" fn(arg1: *mut sqlite3_vfs, arg2: *mut f64) -> ::std::os::raw::c_int,
    >,
    pub xGetLastError: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_vfs,
            arg2: ::std::os::raw::c_int,
            arg3: *mut ::std::os::raw::c_char,
        ) -> ::std::os::raw::c_int,
    >,
    pub xCurrentTimeInt64: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_vfs,
            arg2: *mut sqlite3_int64,
        ) -> ::std::os::raw::c_int,
    >,
    pub xSetSystemCall: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_vfs,
            zName: *const ::std::os::raw::c_char,
            arg2: sqlite3_syscall_ptr,
        ) -> ::std::os::raw::c_int,
    >,
    pub xGetSystemCall: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_vfs,
            zName: *const ::std::os::raw::c_char,
        ) -> sqlite3_syscall_ptr,
    >,
    pub xNextSystemCall: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_vfs,
            zName: *const ::std::os::raw::c_char,
        ) -> *const ::std::os::raw::c_char,
    >,
}
extern "C" {
    pub fn sqlite3_initialize() -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_shutdown() -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_os_init() -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_os_end() -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_config(arg1: ::std::os::raw::c_int, ...) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_db_config(
        arg1: *mut sqlite3,
        op: ::std::os::raw::c_int,
        ...
    ) -> ::std::os::raw::c_int;
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_mem_methods {
    pub xMalloc: ::std::option::Option<
        unsafe extern "C" fn(arg1: ::std::os::raw::c_int) -> *mut ::std::os::raw::c_void,
    >,
    pub xFree: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
    pub xRealloc: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut ::std::os::raw::c_void,
            arg2: ::std::os::raw::c_int,
        ) -> *mut ::std::os::raw::c_void,
    >,
    pub xSize: ::std::option::Option<
        unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void) -> ::std::os::raw::c_int,
    >,
    pub xRoundup: ::std::option::Option<
        unsafe extern "C" fn(arg1: ::std::os::raw::c_int) -> ::std::os::raw::c_int,
    >,
    pub xInit: ::std::option::Option<
        unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void) -> ::std::os::raw::c_int,
    >,
    pub xShutdown: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
    pub pAppData: *mut ::std::os::raw::c_void,
}
extern "C" {
    pub fn sqlite3_extended_result_codes(
        arg1: *mut sqlite3,
        onoff: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_last_insert_rowid(arg1: *mut sqlite3) -> sqlite3_int64;
}
extern "C" {
    pub fn sqlite3_set_last_insert_rowid(arg1: *mut sqlite3, arg2: sqlite3_int64);
}
extern "C" {
    pub fn sqlite3_changes(arg1: *mut sqlite3) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_changes64(arg1: *mut sqlite3) -> sqlite3_int64;
}
extern "C" {
    pub fn sqlite3_total_changes(arg1: *mut sqlite3) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_total_changes64(arg1: *mut sqlite3) -> sqlite3_int64;
}
extern "C" {
    pub fn sqlite3_interrupt(arg1: *mut sqlite3);
}
extern "C" {
    pub fn sqlite3_is_interrupted(arg1: *mut sqlite3) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_complete(sql: *const ::std::os::raw::c_char) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_complete16(sql: *const ::std::os::raw::c_void) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_busy_handler(
        arg1: *mut sqlite3,
        arg2: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut ::std::os::raw::c_void,
                arg2: ::std::os::raw::c_int,
            ) -> ::std::os::raw::c_int,
        >,
        arg3: *mut ::std::os::raw::c_void,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_busy_timeout(
        arg1: *mut sqlite3,
        ms: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_get_table(
        db: *mut sqlite3,
        zSql: *const ::std::os::raw::c_char,
        pazResult: *mut *mut *mut ::std::os::raw::c_char,
        pnRow: *mut ::std::os::raw::c_int,
        pnColumn: *mut ::std::os::raw::c_int,
        pzErrmsg: *mut *mut ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_free_table(result: *mut *mut ::std::os::raw::c_char);
}
extern "C" {
    pub fn sqlite3_mprintf(arg1: *const ::std::os::raw::c_char, ...)
        -> *mut ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_vmprintf(
        arg1: *const ::std::os::raw::c_char,
        arg2: *mut __va_list_tag,
    ) -> *mut ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_snprintf(
        arg1: ::std::os::raw::c_int,
        arg2: *mut ::std::os::raw::c_char,
        arg3: *const ::std::os::raw::c_char,
        ...
    ) -> *mut ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_vsnprintf(
        arg1: ::std::os::raw::c_int,
        arg2: *mut ::std::os::raw::c_char,
        arg3: *const ::std::os::raw::c_char,
        arg4: *mut __va_list_tag,
    ) -> *mut ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_malloc(arg1: ::std::os::raw::c_int) -> *mut ::std::os::raw::c_void;
}
extern "C" {
    pub fn sqlite3_malloc64(arg1: sqlite3_uint64) -> *mut ::std::os::raw::c_void;
}
extern "C" {
    pub fn sqlite3_realloc(
        arg1: *mut ::std::os::raw::c_void,
        arg2: ::std::os::raw::c_int,
    ) -> *mut ::std::os::raw::c_void;
}
extern "C" {
    pub fn sqlite3_realloc64(
        arg1: *mut ::std::os::raw::c_void,
        arg2: sqlite3_uint64,
    ) -> *mut ::std::os::raw::c_void;
}
extern "C" {
    pub fn sqlite3_free(arg1: *mut ::std::os::raw::c_void);
}
extern "C" {
    pub fn sqlite3_msize(arg1: *mut ::std::os::raw::c_void) -> sqlite3_uint64;
}
extern "C" {
    pub fn sqlite3_memory_used() -> sqlite3_int64;
}
extern "C" {
    pub fn sqlite3_memory_highwater(resetFlag: ::std::os::raw::c_int) -> sqlite3_int64;
}
extern "C" {
    pub fn sqlite3_randomness(N: ::std::os::raw::c_int, P: *mut ::std::os::raw::c_void);
}
extern "C" {
    pub fn sqlite3_set_authorizer(
        arg1: *mut sqlite3,
        xAuth: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut ::std::os::raw::c_void,
                arg2: ::std::os::raw::c_int,
                arg3: *const ::std::os::raw::c_char,
                arg4: *const ::std::os::raw::c_char,
                arg5: *const ::std::os::raw::c_char,
                arg6: *const ::std::os::raw::c_char,
            ) -> ::std::os::raw::c_int,
        >,
        pUserData: *mut ::std::os::raw::c_void,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_trace(
        arg1: *mut sqlite3,
        xTrace: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut ::std::os::raw::c_void,
                arg2: *const ::std::os::raw::c_char,
            ),
        >,
        arg2: *mut ::std::os::raw::c_void,
    ) -> *mut ::std::os::raw::c_void;
}
extern "C" {
    pub fn sqlite3_profile(
        arg1: *mut sqlite3,
        xProfile: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut ::std::os::raw::c_void,
                arg2: *const ::std::os::raw::c_char,
                arg3: sqlite3_uint64,
            ),
        >,
        arg2: *mut ::std::os::raw::c_void,
    ) -> *mut ::std::os::raw::c_void;
}
extern "C" {
    pub fn sqlite3_trace_v2(
        arg1: *mut sqlite3,
        uMask: ::std::os::raw::c_uint,
        xCallback: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: ::std::os::raw::c_uint,
                arg2: *mut ::std::os::raw::c_void,
                arg3: *mut ::std::os::raw::c_void,
                arg4: *mut ::std::os::raw::c_void,
            ) -> ::std::os::raw::c_int,
        >,
        pCtx: *mut ::std::os::raw::c_void,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_progress_handler(
        arg1: *mut sqlite3,
        arg2: ::std::os::raw::c_int,
        arg3: ::std::option::Option<
            unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void) -> ::std::os::raw::c_int,
        >,
        arg4: *mut ::std::os::raw::c_void,
    );
}
extern "C" {
    pub fn sqlite3_open(
        filename: *const ::std::os::raw::c_char,
        ppDb: *mut *mut sqlite3,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_open16(
        filename: *const ::std::os::raw::c_void,
        ppDb: *mut *mut sqlite3,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_open_v2(
        filename: *const ::std::os::raw::c_char,
        ppDb: *mut *mut sqlite3,
        flags: ::std::os::raw::c_int,
        zVfs: *const ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn libsql_open(
        filename: *const ::std::os::raw::c_char,
        ppDb: *mut *mut sqlite3,
        flags: ::std::os::raw::c_int,
        zVfs: *const ::std::os::raw::c_char,
        zWal: *const ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn libsql_open_v2(
        filename: *const ::std::os::raw::c_char,
        ppDb: *mut *mut sqlite3,
        flags: ::std::os::raw::c_int,
        zVfs: *const ::std::os::raw::c_char,
        zWal: *const ::std::os::raw::c_char,
        pWalMethodsData: *mut ::std::os::raw::c_void,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn libsql_open_v3(
        filename: *const ::std::os::raw::c_char,
        ppDb: *mut *mut sqlite3,
        flags: ::std::os::raw::c_int,
        zVfs: *const ::std::os::raw::c_char,
        wal_manager: libsql_wal_manager,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_wal_backfilled(pWal: *mut sqlite3_wal) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_wal_frame_page_no(
        pWal: *mut sqlite3_wal,
        iFrame: ::std::os::raw::c_uint,
    ) -> ::std::os::raw::c_uint;
}
extern "C" {
    pub fn libsql_try_initialize_wasm_func_table(db: *mut sqlite3) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_uri_parameter(
        z: sqlite3_filename,
        zParam: *const ::std::os::raw::c_char,
    ) -> *const ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_uri_boolean(
        z: sqlite3_filename,
        zParam: *const ::std::os::raw::c_char,
        bDefault: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_uri_int64(
        arg1: sqlite3_filename,
        arg2: *const ::std::os::raw::c_char,
        arg3: sqlite3_int64,
    ) -> sqlite3_int64;
}
extern "C" {
    pub fn sqlite3_uri_key(
        z: sqlite3_filename,
        N: ::std::os::raw::c_int,
    ) -> *const ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_filename_database(arg1: sqlite3_filename) -> *const ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_filename_journal(arg1: sqlite3_filename) -> *const ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_filename_wal(arg1: sqlite3_filename) -> *const ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_database_file_object(arg1: *const ::std::os::raw::c_char) -> *mut sqlite3_file;
}
extern "C" {
    pub fn sqlite3_create_filename(
        zDatabase: *const ::std::os::raw::c_char,
        zJournal: *const ::std::os::raw::c_char,
        zWal: *const ::std::os::raw::c_char,
        nParam: ::std::os::raw::c_int,
        azParam: *mut *const ::std::os::raw::c_char,
    ) -> sqlite3_filename;
}
extern "C" {
    pub fn sqlite3_free_filename(arg1: sqlite3_filename);
}
extern "C" {
    pub fn sqlite3_errcode(db: *mut sqlite3) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_extended_errcode(db: *mut sqlite3) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_errmsg(arg1: *mut sqlite3) -> *const ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_errmsg16(arg1: *mut sqlite3) -> *const ::std::os::raw::c_void;
}
extern "C" {
    pub fn sqlite3_errstr(arg1: ::std::os::raw::c_int) -> *const ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_error_offset(db: *mut sqlite3) -> ::std::os::raw::c_int;
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_stmt {
    _unused: [u8; 0],
}
extern "C" {
    pub fn sqlite3_limit(
        arg1: *mut sqlite3,
        id: ::std::os::raw::c_int,
        newVal: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_prepare(
        db: *mut sqlite3,
        zSql: *const ::std::os::raw::c_char,
        nByte: ::std::os::raw::c_int,
        ppStmt: *mut *mut sqlite3_stmt,
        pzTail: *mut *const ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_prepare_v2(
        db: *mut sqlite3,
        zSql: *const ::std::os::raw::c_char,
        nByte: ::std::os::raw::c_int,
        ppStmt: *mut *mut sqlite3_stmt,
        pzTail: *mut *const ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_prepare_v3(
        db: *mut sqlite3,
        zSql: *const ::std::os::raw::c_char,
        nByte: ::std::os::raw::c_int,
        prepFlags: ::std::os::raw::c_uint,
        ppStmt: *mut *mut sqlite3_stmt,
        pzTail: *mut *const ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_prepare16(
        db: *mut sqlite3,
        zSql: *const ::std::os::raw::c_void,
        nByte: ::std::os::raw::c_int,
        ppStmt: *mut *mut sqlite3_stmt,
        pzTail: *mut *const ::std::os::raw::c_void,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_prepare16_v2(
        db: *mut sqlite3,
        zSql: *const ::std::os::raw::c_void,
        nByte: ::std::os::raw::c_int,
        ppStmt: *mut *mut sqlite3_stmt,
        pzTail: *mut *const ::std::os::raw::c_void,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_prepare16_v3(
        db: *mut sqlite3,
        zSql: *const ::std::os::raw::c_void,
        nByte: ::std::os::raw::c_int,
        prepFlags: ::std::os::raw::c_uint,
        ppStmt: *mut *mut sqlite3_stmt,
        pzTail: *mut *const ::std::os::raw::c_void,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_sql(pStmt: *mut sqlite3_stmt) -> *const ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_expanded_sql(pStmt: *mut sqlite3_stmt) -> *mut ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_stmt_readonly(pStmt: *mut sqlite3_stmt) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_stmt_isexplain(pStmt: *mut sqlite3_stmt) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_stmt_explain(
        pStmt: *mut sqlite3_stmt,
        eMode: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_stmt_busy(arg1: *mut sqlite3_stmt) -> ::std::os::raw::c_int;
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_value {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_context {
    _unused: [u8; 0],
}
extern "C" {
    pub fn sqlite3_bind_blob(
        arg1: *mut sqlite3_stmt,
        arg2: ::std::os::raw::c_int,
        arg3: *const ::std::os::raw::c_void,
        n: ::std::os::raw::c_int,
        arg4: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_bind_blob64(
        arg1: *mut sqlite3_stmt,
        arg2: ::std::os::raw::c_int,
        arg3: *const ::std::os::raw::c_void,
        arg4: sqlite3_uint64,
        arg5: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_bind_double(
        arg1: *mut sqlite3_stmt,
        arg2: ::std::os::raw::c_int,
        arg3: f64,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_bind_int(
        arg1: *mut sqlite3_stmt,
        arg2: ::std::os::raw::c_int,
        arg3: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_bind_int64(
        arg1: *mut sqlite3_stmt,
        arg2: ::std::os::raw::c_int,
        arg3: sqlite3_int64,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_bind_null(
        arg1: *mut sqlite3_stmt,
        arg2: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_bind_text(
        arg1: *mut sqlite3_stmt,
        arg2: ::std::os::raw::c_int,
        arg3: *const ::std::os::raw::c_char,
        arg4: ::std::os::raw::c_int,
        arg5: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_bind_text16(
        arg1: *mut sqlite3_stmt,
        arg2: ::std::os::raw::c_int,
        arg3: *const ::std::os::raw::c_void,
        arg4: ::std::os::raw::c_int,
        arg5: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_bind_text64(
        arg1: *mut sqlite3_stmt,
        arg2: ::std::os::raw::c_int,
        arg3: *const ::std::os::raw::c_char,
        arg4: sqlite3_uint64,
        arg5: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
        encoding: ::std::os::raw::c_uchar,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_bind_value(
        arg1: *mut sqlite3_stmt,
        arg2: ::std::os::raw::c_int,
        arg3: *const sqlite3_value,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_bind_pointer(
        arg1: *mut sqlite3_stmt,
        arg2: ::std::os::raw::c_int,
        arg3: *mut ::std::os::raw::c_void,
        arg4: *const ::std::os::raw::c_char,
        arg5: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_bind_zeroblob(
        arg1: *mut sqlite3_stmt,
        arg2: ::std::os::raw::c_int,
        n: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_bind_zeroblob64(
        arg1: *mut sqlite3_stmt,
        arg2: ::std::os::raw::c_int,
        arg3: sqlite3_uint64,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_bind_parameter_count(arg1: *mut sqlite3_stmt) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_bind_parameter_name(
        arg1: *mut sqlite3_stmt,
        arg2: ::std::os::raw::c_int,
    ) -> *const ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_bind_parameter_index(
        arg1: *mut sqlite3_stmt,
        zName: *const ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_clear_bindings(arg1: *mut sqlite3_stmt) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_column_count(pStmt: *mut sqlite3_stmt) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_column_name(
        arg1: *mut sqlite3_stmt,
        N: ::std::os::raw::c_int,
    ) -> *const ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_column_name16(
        arg1: *mut sqlite3_stmt,
        N: ::std::os::raw::c_int,
    ) -> *const ::std::os::raw::c_void;
}
extern "C" {
    pub fn sqlite3_column_database_name(
        arg1: *mut sqlite3_stmt,
        arg2: ::std::os::raw::c_int,
    ) -> *const ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_column_database_name16(
        arg1: *mut sqlite3_stmt,
        arg2: ::std::os::raw::c_int,
    ) -> *const ::std::os::raw::c_void;
}
extern "C" {
    pub fn sqlite3_column_table_name(
        arg1: *mut sqlite3_stmt,
        arg2: ::std::os::raw::c_int,
    ) -> *const ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_column_table_name16(
        arg1: *mut sqlite3_stmt,
        arg2: ::std::os::raw::c_int,
    ) -> *const ::std::os::raw::c_void;
}
extern "C" {
    pub fn sqlite3_column_origin_name(
        arg1: *mut sqlite3_stmt,
        arg2: ::std::os::raw::c_int,
    ) -> *const ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_column_origin_name16(
        arg1: *mut sqlite3_stmt,
        arg2: ::std::os::raw::c_int,
    ) -> *const ::std::os::raw::c_void;
}
extern "C" {
    pub fn sqlite3_column_decltype(
        arg1: *mut sqlite3_stmt,
        arg2: ::std::os::raw::c_int,
    ) -> *const ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_column_decltype16(
        arg1: *mut sqlite3_stmt,
        arg2: ::std::os::raw::c_int,
    ) -> *const ::std::os::raw::c_void;
}
extern "C" {
    pub fn sqlite3_step(arg1: *mut sqlite3_stmt) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_data_count(pStmt: *mut sqlite3_stmt) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_column_blob(
        arg1: *mut sqlite3_stmt,
        iCol: ::std::os::raw::c_int,
    ) -> *const ::std::os::raw::c_void;
}
extern "C" {
    pub fn sqlite3_column_double(arg1: *mut sqlite3_stmt, iCol: ::std::os::raw::c_int) -> f64;
}
extern "C" {
    pub fn sqlite3_column_int(
        arg1: *mut sqlite3_stmt,
        iCol: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_column_int64(
        arg1: *mut sqlite3_stmt,
        iCol: ::std::os::raw::c_int,
    ) -> sqlite3_int64;
}
extern "C" {
    pub fn sqlite3_column_text(
        arg1: *mut sqlite3_stmt,
        iCol: ::std::os::raw::c_int,
    ) -> *const ::std::os::raw::c_uchar;
}
extern "C" {
    pub fn sqlite3_column_text16(
        arg1: *mut sqlite3_stmt,
        iCol: ::std::os::raw::c_int,
    ) -> *const ::std::os::raw::c_void;
}
extern "C" {
    pub fn sqlite3_column_value(
        arg1: *mut sqlite3_stmt,
        iCol: ::std::os::raw::c_int,
    ) -> *mut sqlite3_value;
}
extern "C" {
    pub fn sqlite3_column_bytes(
        arg1: *mut sqlite3_stmt,
        iCol: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_column_bytes16(
        arg1: *mut sqlite3_stmt,
        iCol: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_column_type(
        arg1: *mut sqlite3_stmt,
        iCol: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_finalize(pStmt: *mut sqlite3_stmt) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_reset(pStmt: *mut sqlite3_stmt) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn libsql_stmt_interrupt(stmt: *mut sqlite3_stmt);
}
extern "C" {
    pub fn sqlite3_create_function(
        db: *mut sqlite3,
        zFunctionName: *const ::std::os::raw::c_char,
        nArg: ::std::os::raw::c_int,
        eTextRep: ::std::os::raw::c_int,
        pApp: *mut ::std::os::raw::c_void,
        xFunc: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut sqlite3_context,
                arg2: ::std::os::raw::c_int,
                arg3: *mut *mut sqlite3_value,
            ),
        >,
        xStep: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut sqlite3_context,
                arg2: ::std::os::raw::c_int,
                arg3: *mut *mut sqlite3_value,
            ),
        >,
        xFinal: ::std::option::Option<unsafe extern "C" fn(arg1: *mut sqlite3_context)>,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_create_function16(
        db: *mut sqlite3,
        zFunctionName: *const ::std::os::raw::c_void,
        nArg: ::std::os::raw::c_int,
        eTextRep: ::std::os::raw::c_int,
        pApp: *mut ::std::os::raw::c_void,
        xFunc: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut sqlite3_context,
                arg2: ::std::os::raw::c_int,
                arg3: *mut *mut sqlite3_value,
            ),
        >,
        xStep: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut sqlite3_context,
                arg2: ::std::os::raw::c_int,
                arg3: *mut *mut sqlite3_value,
            ),
        >,
        xFinal: ::std::option::Option<unsafe extern "C" fn(arg1: *mut sqlite3_context)>,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_create_function_v2(
        db: *mut sqlite3,
        zFunctionName: *const ::std::os::raw::c_char,
        nArg: ::std::os::raw::c_int,
        eTextRep: ::std::os::raw::c_int,
        pApp: *mut ::std::os::raw::c_void,
        xFunc: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut sqlite3_context,
                arg2: ::std::os::raw::c_int,
                arg3: *mut *mut sqlite3_value,
            ),
        >,
        xStep: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut sqlite3_context,
                arg2: ::std::os::raw::c_int,
                arg3: *mut *mut sqlite3_value,
            ),
        >,
        xFinal: ::std::option::Option<unsafe extern "C" fn(arg1: *mut sqlite3_context)>,
        xDestroy: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_create_window_function(
        db: *mut sqlite3,
        zFunctionName: *const ::std::os::raw::c_char,
        nArg: ::std::os::raw::c_int,
        eTextRep: ::std::os::raw::c_int,
        pApp: *mut ::std::os::raw::c_void,
        xStep: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut sqlite3_context,
                arg2: ::std::os::raw::c_int,
                arg3: *mut *mut sqlite3_value,
            ),
        >,
        xFinal: ::std::option::Option<unsafe extern "C" fn(arg1: *mut sqlite3_context)>,
        xValue: ::std::option::Option<unsafe extern "C" fn(arg1: *mut sqlite3_context)>,
        xInverse: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut sqlite3_context,
                arg2: ::std::os::raw::c_int,
                arg3: *mut *mut sqlite3_value,
            ),
        >,
        xDestroy: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_aggregate_count(arg1: *mut sqlite3_context) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_expired(arg1: *mut sqlite3_stmt) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_transfer_bindings(
        arg1: *mut sqlite3_stmt,
        arg2: *mut sqlite3_stmt,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_global_recover() -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_thread_cleanup();
}
extern "C" {
    pub fn sqlite3_memory_alarm(
        arg1: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut ::std::os::raw::c_void,
                arg2: sqlite3_int64,
                arg3: ::std::os::raw::c_int,
            ),
        >,
        arg2: *mut ::std::os::raw::c_void,
        arg3: sqlite3_int64,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_value_blob(arg1: *mut sqlite3_value) -> *const ::std::os::raw::c_void;
}
extern "C" {
    pub fn sqlite3_value_double(arg1: *mut sqlite3_value) -> f64;
}
extern "C" {
    pub fn sqlite3_value_int(arg1: *mut sqlite3_value) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_value_int64(arg1: *mut sqlite3_value) -> sqlite3_int64;
}
extern "C" {
    pub fn sqlite3_value_pointer(
        arg1: *mut sqlite3_value,
        arg2: *const ::std::os::raw::c_char,
    ) -> *mut ::std::os::raw::c_void;
}
extern "C" {
    pub fn sqlite3_value_text(arg1: *mut sqlite3_value) -> *const ::std::os::raw::c_uchar;
}
extern "C" {
    pub fn sqlite3_value_text16(arg1: *mut sqlite3_value) -> *const ::std::os::raw::c_void;
}
extern "C" {
    pub fn sqlite3_value_text16le(arg1: *mut sqlite3_value) -> *const ::std::os::raw::c_void;
}
extern "C" {
    pub fn sqlite3_value_text16be(arg1: *mut sqlite3_value) -> *const ::std::os::raw::c_void;
}
extern "C" {
    pub fn sqlite3_value_bytes(arg1: *mut sqlite3_value) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_value_bytes16(arg1: *mut sqlite3_value) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_value_type(arg1: *mut sqlite3_value) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_value_numeric_type(arg1: *mut sqlite3_value) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_value_nochange(arg1: *mut sqlite3_value) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_value_frombind(arg1: *mut sqlite3_value) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_value_encoding(arg1: *mut sqlite3_value) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_value_subtype(arg1: *mut sqlite3_value) -> ::std::os::raw::c_uint;
}
extern "C" {
    pub fn sqlite3_value_dup(arg1: *const sqlite3_value) -> *mut sqlite3_value;
}
extern "C" {
    pub fn sqlite3_value_free(arg1: *mut sqlite3_value);
}
extern "C" {
    pub fn sqlite3_aggregate_context(
        arg1: *mut sqlite3_context,
        nBytes: ::std::os::raw::c_int,
    ) -> *mut ::std::os::raw::c_void;
}
extern "C" {
    pub fn sqlite3_user_data(arg1: *mut sqlite3_context) -> *mut ::std::os::raw::c_void;
}
extern "C" {
    pub fn sqlite3_context_db_handle(arg1: *mut sqlite3_context) -> *mut sqlite3;
}
extern "C" {
    pub fn sqlite3_get_auxdata(
        arg1: *mut sqlite3_context,
        N: ::std::os::raw::c_int,
    ) -> *mut ::std::os::raw::c_void;
}
extern "C" {
    pub fn sqlite3_set_auxdata(
        arg1: *mut sqlite3_context,
        N: ::std::os::raw::c_int,
        arg2: *mut ::std::os::raw::c_void,
        arg3: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
    );
}
extern "C" {
    pub fn sqlite3_get_clientdata(
        arg1: *mut sqlite3,
        arg2: *const ::std::os::raw::c_char,
    ) -> *mut ::std::os::raw::c_void;
}
extern "C" {
    pub fn sqlite3_set_clientdata(
        arg1: *mut sqlite3,
        arg2: *const ::std::os::raw::c_char,
        arg3: *mut ::std::os::raw::c_void,
        arg4: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
    ) -> ::std::os::raw::c_int;
}
pub type sqlite3_destructor_type =
    ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>;
extern "C" {
    pub fn sqlite3_result_blob(
        arg1: *mut sqlite3_context,
        arg2: *const ::std::os::raw::c_void,
        arg3: ::std::os::raw::c_int,
        arg4: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
    );
}
extern "C" {
    pub fn sqlite3_result_blob64(
        arg1: *mut sqlite3_context,
        arg2: *const ::std::os::raw::c_void,
        arg3: sqlite3_uint64,
        arg4: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
    );
}
extern "C" {
    pub fn sqlite3_result_double(arg1: *mut sqlite3_context, arg2: f64);
}
extern "C" {
    pub fn sqlite3_result_error(
        arg1: *mut sqlite3_context,
        arg2: *const ::std::os::raw::c_char,
        arg3: ::std::os::raw::c_int,
    );
}
extern "C" {
    pub fn sqlite3_result_error16(
        arg1: *mut sqlite3_context,
        arg2: *const ::std::os::raw::c_void,
        arg3: ::std::os::raw::c_int,
    );
}
extern "C" {
    pub fn sqlite3_result_error_toobig(arg1: *mut sqlite3_context);
}
extern "C" {
    pub fn sqlite3_result_error_nomem(arg1: *mut sqlite3_context);
}
extern "C" {
    pub fn sqlite3_result_error_code(arg1: *mut sqlite3_context, arg2: ::std::os::raw::c_int);
}
extern "C" {
    pub fn sqlite3_result_int(arg1: *mut sqlite3_context, arg2: ::std::os::raw::c_int);
}
extern "C" {
    pub fn sqlite3_result_int64(arg1: *mut sqlite3_context, arg2: sqlite3_int64);
}
extern "C" {
    pub fn sqlite3_result_null(arg1: *mut sqlite3_context);
}
extern "C" {
    pub fn sqlite3_result_text(
        arg1: *mut sqlite3_context,
        arg2: *const ::std::os::raw::c_char,
        arg3: ::std::os::raw::c_int,
        arg4: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
    );
}
extern "C" {
    pub fn sqlite3_result_text64(
        arg1: *mut sqlite3_context,
        arg2: *const ::std::os::raw::c_char,
        arg3: sqlite3_uint64,
        arg4: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
        encoding: ::std::os::raw::c_uchar,
    );
}
extern "C" {
    pub fn sqlite3_result_text16(
        arg1: *mut sqlite3_context,
        arg2: *const ::std::os::raw::c_void,
        arg3: ::std::os::raw::c_int,
        arg4: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
    );
}
extern "C" {
    pub fn sqlite3_result_text16le(
        arg1: *mut sqlite3_context,
        arg2: *const ::std::os::raw::c_void,
        arg3: ::std::os::raw::c_int,
        arg4: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
    );
}
extern "C" {
    pub fn sqlite3_result_text16be(
        arg1: *mut sqlite3_context,
        arg2: *const ::std::os::raw::c_void,
        arg3: ::std::os::raw::c_int,
        arg4: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
    );
}
extern "C" {
    pub fn sqlite3_result_value(arg1: *mut sqlite3_context, arg2: *mut sqlite3_value);
}
extern "C" {
    pub fn sqlite3_result_pointer(
        arg1: *mut sqlite3_context,
        arg2: *mut ::std::os::raw::c_void,
        arg3: *const ::std::os::raw::c_char,
        arg4: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
    );
}
extern "C" {
    pub fn sqlite3_result_zeroblob(arg1: *mut sqlite3_context, n: ::std::os::raw::c_int);
}
extern "C" {
    pub fn sqlite3_result_zeroblob64(
        arg1: *mut sqlite3_context,
        n: sqlite3_uint64,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_result_subtype(arg1: *mut sqlite3_context, arg2: ::std::os::raw::c_uint);
}
extern "C" {
    pub fn sqlite3_create_collation(
        arg1: *mut sqlite3,
        zName: *const ::std::os::raw::c_char,
        eTextRep: ::std::os::raw::c_int,
        pArg: *mut ::std::os::raw::c_void,
        xCompare: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut ::std::os::raw::c_void,
                arg2: ::std::os::raw::c_int,
                arg3: *const ::std::os::raw::c_void,
                arg4: ::std::os::raw::c_int,
                arg5: *const ::std::os::raw::c_void,
            ) -> ::std::os::raw::c_int,
        >,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_create_collation_v2(
        arg1: *mut sqlite3,
        zName: *const ::std::os::raw::c_char,
        eTextRep: ::std::os::raw::c_int,
        pArg: *mut ::std::os::raw::c_void,
        xCompare: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut ::std::os::raw::c_void,
                arg2: ::std::os::raw::c_int,
                arg3: *const ::std::os::raw::c_void,
                arg4: ::std::os::raw::c_int,
                arg5: *const ::std::os::raw::c_void,
            ) -> ::std::os::raw::c_int,
        >,
        xDestroy: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_create_collation16(
        arg1: *mut sqlite3,
        zName: *const ::std::os::raw::c_void,
        eTextRep: ::std::os::raw::c_int,
        pArg: *mut ::std::os::raw::c_void,
        xCompare: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut ::std::os::raw::c_void,
                arg2: ::std::os::raw::c_int,
                arg3: *const ::std::os::raw::c_void,
                arg4: ::std::os::raw::c_int,
                arg5: *const ::std::os::raw::c_void,
            ) -> ::std::os::raw::c_int,
        >,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_collation_needed(
        arg1: *mut sqlite3,
        arg2: *mut ::std::os::raw::c_void,
        arg3: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut ::std::os::raw::c_void,
                arg2: *mut sqlite3,
                eTextRep: ::std::os::raw::c_int,
                arg3: *const ::std::os::raw::c_char,
            ),
        >,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_collation_needed16(
        arg1: *mut sqlite3,
        arg2: *mut ::std::os::raw::c_void,
        arg3: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut ::std::os::raw::c_void,
                arg2: *mut sqlite3,
                eTextRep: ::std::os::raw::c_int,
                arg3: *const ::std::os::raw::c_void,
            ),
        >,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_sleep(arg1: ::std::os::raw::c_int) -> ::std::os::raw::c_int;
}
extern "C" {
    pub static mut sqlite3_temp_directory: *mut ::std::os::raw::c_char;
}
extern "C" {
    pub static mut sqlite3_data_directory: *mut ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_win32_set_directory(
        type_: ::std::os::raw::c_ulong,
        zValue: *mut ::std::os::raw::c_void,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_win32_set_directory8(
        type_: ::std::os::raw::c_ulong,
        zValue: *const ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_win32_set_directory16(
        type_: ::std::os::raw::c_ulong,
        zValue: *const ::std::os::raw::c_void,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_get_autocommit(arg1: *mut sqlite3) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_db_handle(arg1: *mut sqlite3_stmt) -> *mut sqlite3;
}
extern "C" {
    pub fn sqlite3_db_name(
        db: *mut sqlite3,
        N: ::std::os::raw::c_int,
    ) -> *const ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_db_filename(
        db: *mut sqlite3,
        zDbName: *const ::std::os::raw::c_char,
    ) -> sqlite3_filename;
}
extern "C" {
    pub fn sqlite3_db_readonly(
        db: *mut sqlite3,
        zDbName: *const ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_txn_state(
        arg1: *mut sqlite3,
        zSchema: *const ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_next_stmt(pDb: *mut sqlite3, pStmt: *mut sqlite3_stmt) -> *mut sqlite3_stmt;
}
extern "C" {
    pub fn sqlite3_commit_hook(
        arg1: *mut sqlite3,
        arg2: ::std::option::Option<
            unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void) -> ::std::os::raw::c_int,
        >,
        arg3: *mut ::std::os::raw::c_void,
    ) -> *mut ::std::os::raw::c_void;
}
extern "C" {
    pub fn sqlite3_rollback_hook(
        arg1: *mut sqlite3,
        arg2: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
        arg3: *mut ::std::os::raw::c_void,
    ) -> *mut ::std::os::raw::c_void;
}
extern "C" {
    pub fn sqlite3_autovacuum_pages(
        db: *mut sqlite3,
        arg1: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut ::std::os::raw::c_void,
                arg2: *const ::std::os::raw::c_char,
                arg3: ::std::os::raw::c_uint,
                arg4: ::std::os::raw::c_uint,
                arg5: ::std::os::raw::c_uint,
            ) -> ::std::os::raw::c_uint,
        >,
        arg2: *mut ::std::os::raw::c_void,
        arg3: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_update_hook(
        arg1: *mut sqlite3,
        arg2: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut ::std::os::raw::c_void,
                arg2: ::std::os::raw::c_int,
                arg3: *const ::std::os::raw::c_char,
                arg4: *const ::std::os::raw::c_char,
                arg5: sqlite3_int64,
            ),
        >,
        arg3: *mut ::std::os::raw::c_void,
    ) -> *mut ::std::os::raw::c_void;
}
extern "C" {
    pub fn sqlite3_enable_shared_cache(arg1: ::std::os::raw::c_int) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_release_memory(arg1: ::std::os::raw::c_int) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_db_release_memory(arg1: *mut sqlite3) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_soft_heap_limit64(N: sqlite3_int64) -> sqlite3_int64;
}
extern "C" {
    pub fn sqlite3_hard_heap_limit64(N: sqlite3_int64) -> sqlite3_int64;
}
extern "C" {
    pub fn sqlite3_soft_heap_limit(N: ::std::os::raw::c_int);
}
extern "C" {
    pub fn sqlite3_table_column_metadata(
        db: *mut sqlite3,
        zDbName: *const ::std::os::raw::c_char,
        zTableName: *const ::std::os::raw::c_char,
        zColumnName: *const ::std::os::raw::c_char,
        pzDataType: *mut *const ::std::os::raw::c_char,
        pzCollSeq: *mut *const ::std::os::raw::c_char,
        pNotNull: *mut ::std::os::raw::c_int,
        pPrimaryKey: *mut ::std::os::raw::c_int,
        pAutoinc: *mut ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_load_extension(
        db: *mut sqlite3,
        zFile: *const ::std::os::raw::c_char,
        zProc: *const ::std::os::raw::c_char,
        pzErrMsg: *mut *mut ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_enable_load_extension(
        db: *mut sqlite3,
        onoff: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_reset_auto_extension();
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_module {
    pub iVersion: ::std::os::raw::c_int,
    pub xCreate: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3,
            pAux: *mut ::std::os::raw::c_void,
            argc: ::std::os::raw::c_int,
            argv: *const *const ::std::os::raw::c_char,
            ppVTab: *mut *mut sqlite3_vtab,
            arg2: *mut *mut ::std::os::raw::c_char,
        ) -> ::std::os::raw::c_int,
    >,
    pub xConnect: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3,
            pAux: *mut ::std::os::raw::c_void,
            argc: ::std::os::raw::c_int,
            argv: *const *const ::std::os::raw::c_char,
            ppVTab: *mut *mut sqlite3_vtab,
            arg2: *mut *mut ::std::os::raw::c_char,
        ) -> ::std::os::raw::c_int,
    >,
    pub xBestIndex: ::std::option::Option<
        unsafe extern "C" fn(
            pVTab: *mut sqlite3_vtab,
            arg1: *mut sqlite3_index_info,
        ) -> ::std::os::raw::c_int,
    >,
    pub xDisconnect: ::std::option::Option<
        unsafe extern "C" fn(pVTab: *mut sqlite3_vtab) -> ::std::os::raw::c_int,
    >,
    pub xDestroy: ::std::option::Option<
        unsafe extern "C" fn(pVTab: *mut sqlite3_vtab) -> ::std::os::raw::c_int,
    >,
    pub xOpen: ::std::option::Option<
        unsafe extern "C" fn(
            pVTab: *mut sqlite3_vtab,
            ppCursor: *mut *mut sqlite3_vtab_cursor,
        ) -> ::std::os::raw::c_int,
    >,
    pub xClose: ::std::option::Option<
        unsafe extern "C" fn(arg1: *mut sqlite3_vtab_cursor) -> ::std::os::raw::c_int,
    >,
    pub xFilter: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_vtab_cursor,
            idxNum: ::std::os::raw::c_int,
            idxStr: *const ::std::os::raw::c_char,
            argc: ::std::os::raw::c_int,
            argv: *mut *mut sqlite3_value,
        ) -> ::std::os::raw::c_int,
    >,
    pub xNext: ::std::option::Option<
        unsafe extern "C" fn(arg1: *mut sqlite3_vtab_cursor) -> ::std::os::raw::c_int,
    >,
    pub xEof: ::std::option::Option<
        unsafe extern "C" fn(arg1: *mut sqlite3_vtab_cursor) -> ::std::os::raw::c_int,
    >,
    pub xColumn: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_vtab_cursor,
            arg2: *mut sqlite3_context,
            arg3: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub xRowid: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_vtab_cursor,
            pRowid: *mut sqlite3_int64,
        ) -> ::std::os::raw::c_int,
    >,
    pub xUpdate: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_vtab,
            arg2: ::std::os::raw::c_int,
            arg3: *mut *mut sqlite3_value,
            arg4: *mut sqlite3_int64,
        ) -> ::std::os::raw::c_int,
    >,
    pub xBegin: ::std::option::Option<
        unsafe extern "C" fn(pVTab: *mut sqlite3_vtab) -> ::std::os::raw::c_int,
    >,
    pub xSync: ::std::option::Option<
        unsafe extern "C" fn(pVTab: *mut sqlite3_vtab) -> ::std::os::raw::c_int,
    >,
    pub xCommit: ::std::option::Option<
        unsafe extern "C" fn(pVTab: *mut sqlite3_vtab) -> ::std::os::raw::c_int,
    >,
    pub xRollback: ::std::option::Option<
        unsafe extern "C" fn(pVTab: *mut sqlite3_vtab) -> ::std::os::raw::c_int,
    >,
    pub xFindFunction: ::std::option::Option<
        unsafe extern "C" fn(
            pVtab: *mut sqlite3_vtab,
            nArg: ::std::os::raw::c_int,
            zName: *const ::std::os::raw::c_char,
            pxFunc: *mut ::std::option::Option<
                unsafe extern "C" fn(
                    arg1: *mut sqlite3_context,
                    arg2: ::std::os::raw::c_int,
                    arg3: *mut *mut sqlite3_value,
                ),
            >,
            ppArg: *mut *mut ::std::os::raw::c_void,
        ) -> ::std::os::raw::c_int,
    >,
    pub xRename: ::std::option::Option<
        unsafe extern "C" fn(
            pVtab: *mut sqlite3_vtab,
            zNew: *const ::std::os::raw::c_char,
        ) -> ::std::os::raw::c_int,
    >,
    pub xSavepoint: ::std::option::Option<
        unsafe extern "C" fn(
            pVTab: *mut sqlite3_vtab,
            arg1: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub xRelease: ::std::option::Option<
        unsafe extern "C" fn(
            pVTab: *mut sqlite3_vtab,
            arg1: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub xRollbackTo: ::std::option::Option<
        unsafe extern "C" fn(
            pVTab: *mut sqlite3_vtab,
            arg1: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub xShadowName: ::std::option::Option<
        unsafe extern "C" fn(arg1: *const ::std::os::raw::c_char) -> ::std::os::raw::c_int,
    >,
    pub xIntegrity: ::std::option::Option<
        unsafe extern "C" fn(
            pVTab: *mut sqlite3_vtab,
            zSchema: *const ::std::os::raw::c_char,
            zTabName: *const ::std::os::raw::c_char,
            mFlags: ::std::os::raw::c_int,
            pzErr: *mut *mut ::std::os::raw::c_char,
        ) -> ::std::os::raw::c_int,
    >,
    pub reserved: [::std::option::Option<unsafe extern "C" fn()>; 5usize],
    pub xPreparedSql: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_vtab_cursor,
            arg2: *const ::std::os::raw::c_char,
        ) -> ::std::os::raw::c_int,
    >,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_index_info {
    pub nConstraint: ::std::os::raw::c_int,
    pub aConstraint: *mut sqlite3_index_constraint,
    pub nOrderBy: ::std::os::raw::c_int,
    pub aOrderBy: *mut sqlite3_index_orderby,
    pub aConstraintUsage: *mut sqlite3_index_constraint_usage,
    pub idxNum: ::std::os::raw::c_int,
    pub idxStr: *mut ::std::os::raw::c_char,
    pub needToFreeIdxStr: ::std::os::raw::c_int,
    pub orderByConsumed: ::std::os::raw::c_int,
    pub estimatedCost: f64,
    pub estimatedRows: sqlite3_int64,
    pub idxFlags: ::std::os::raw::c_int,
    pub colUsed: sqlite3_uint64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_index_constraint {
    pub iColumn: ::std::os::raw::c_int,
    pub op: ::std::os::raw::c_uchar,
    pub usable: ::std::os::raw::c_uchar,
    pub iTermOffset: ::std::os::raw::c_int,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_index_orderby {
    pub iColumn: ::std::os::raw::c_int,
    pub desc: ::std::os::raw::c_uchar,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_index_constraint_usage {
    pub argvIndex: ::std::os::raw::c_int,
    pub omit: ::std::os::raw::c_uchar,
}
extern "C" {
    pub fn sqlite3_create_module(
        db: *mut sqlite3,
        zName: *const ::std::os::raw::c_char,
        p: *const sqlite3_module,
        pClientData: *mut ::std::os::raw::c_void,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_create_module_v2(
        db: *mut sqlite3,
        zName: *const ::std::os::raw::c_char,
        p: *const sqlite3_module,
        pClientData: *mut ::std::os::raw::c_void,
        xDestroy: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_drop_modules(
        db: *mut sqlite3,
        azKeep: *mut *const ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int;
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_vtab {
    pub pModule: *const sqlite3_module,
    pub nRef: ::std::os::raw::c_int,
    pub zErrMsg: *mut ::std::os::raw::c_char,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_vtab_cursor {
    pub pVtab: *mut sqlite3_vtab,
}
extern "C" {
    pub fn sqlite3_declare_vtab(
        arg1: *mut sqlite3,
        zSQL: *const ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_overload_function(
        arg1: *mut sqlite3,
        zFuncName: *const ::std::os::raw::c_char,
        nArg: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_blob {
    _unused: [u8; 0],
}
extern "C" {
    pub fn sqlite3_blob_open(
        arg1: *mut sqlite3,
        zDb: *const ::std::os::raw::c_char,
        zTable: *const ::std::os::raw::c_char,
        zColumn: *const ::std::os::raw::c_char,
        iRow: sqlite3_int64,
        flags: ::std::os::raw::c_int,
        ppBlob: *mut *mut sqlite3_blob,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_blob_reopen(
        arg1: *mut sqlite3_blob,
        arg2: sqlite3_int64,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_blob_close(arg1: *mut sqlite3_blob) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_blob_bytes(arg1: *mut sqlite3_blob) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_blob_read(
        arg1: *mut sqlite3_blob,
        Z: *mut ::std::os::raw::c_void,
        N: ::std::os::raw::c_int,
        iOffset: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_blob_write(
        arg1: *mut sqlite3_blob,
        z: *const ::std::os::raw::c_void,
        n: ::std::os::raw::c_int,
        iOffset: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_vfs_find(zVfsName: *const ::std::os::raw::c_char) -> *mut sqlite3_vfs;
}
extern "C" {
    pub fn sqlite3_vfs_register(
        arg1: *mut sqlite3_vfs,
        makeDflt: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_vfs_unregister(arg1: *mut sqlite3_vfs) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_mutex_alloc(arg1: ::std::os::raw::c_int) -> *mut sqlite3_mutex;
}
extern "C" {
    pub fn sqlite3_mutex_free(arg1: *mut sqlite3_mutex);
}
extern "C" {
    pub fn sqlite3_mutex_enter(arg1: *mut sqlite3_mutex);
}
extern "C" {
    pub fn sqlite3_mutex_try(arg1: *mut sqlite3_mutex) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_mutex_leave(arg1: *mut sqlite3_mutex);
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_mutex_methods {
    pub xMutexInit: ::std::option::Option<unsafe extern "C" fn() -> ::std::os::raw::c_int>,
    pub xMutexEnd: ::std::option::Option<unsafe extern "C" fn() -> ::std::os::raw::c_int>,
    pub xMutexAlloc: ::std::option::Option<
        unsafe extern "C" fn(arg1: ::std::os::raw::c_int) -> *mut sqlite3_mutex,
    >,
    pub xMutexFree: ::std::option::Option<unsafe extern "C" fn(arg1: *mut sqlite3_mutex)>,
    pub xMutexEnter: ::std::option::Option<unsafe extern "C" fn(arg1: *mut sqlite3_mutex)>,
    pub xMutexTry: ::std::option::Option<
        unsafe extern "C" fn(arg1: *mut sqlite3_mutex) -> ::std::os::raw::c_int,
    >,
    pub xMutexLeave: ::std::option::Option<unsafe extern "C" fn(arg1: *mut sqlite3_mutex)>,
    pub xMutexHeld: ::std::option::Option<
        unsafe extern "C" fn(arg1: *mut sqlite3_mutex) -> ::std::os::raw::c_int,
    >,
    pub xMutexNotheld: ::std::option::Option<
        unsafe extern "C" fn(arg1: *mut sqlite3_mutex) -> ::std::os::raw::c_int,
    >,
}
extern "C" {
    pub fn sqlite3_mutex_held(arg1: *mut sqlite3_mutex) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_mutex_notheld(arg1: *mut sqlite3_mutex) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_db_mutex(arg1: *mut sqlite3) -> *mut sqlite3_mutex;
}
extern "C" {
    pub fn sqlite3_file_control(
        arg1: *mut sqlite3,
        zDbName: *const ::std::os::raw::c_char,
        op: ::std::os::raw::c_int,
        arg2: *mut ::std::os::raw::c_void,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_test_control(op: ::std::os::raw::c_int, ...) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_keyword_count() -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_keyword_name(
        arg1: ::std::os::raw::c_int,
        arg2: *mut *const ::std::os::raw::c_char,
        arg3: *mut ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_keyword_check(
        arg1: *const ::std::os::raw::c_char,
        arg2: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_str {
    _unused: [u8; 0],
}
extern "C" {
    pub fn sqlite3_str_new(arg1: *mut sqlite3) -> *mut sqlite3_str;
}
extern "C" {
    pub fn sqlite3_str_finish(arg1: *mut sqlite3_str) -> *mut ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_str_appendf(arg1: *mut sqlite3_str, zFormat: *const ::std::os::raw::c_char, ...);
}
extern "C" {
    pub fn sqlite3_str_vappendf(
        arg1: *mut sqlite3_str,
        zFormat: *const ::std::os::raw::c_char,
        arg2: *mut __va_list_tag,
    );
}
extern "C" {
    pub fn sqlite3_str_append(
        arg1: *mut sqlite3_str,
        zIn: *const ::std::os::raw::c_char,
        N: ::std::os::raw::c_int,
    );
}
extern "C" {
    pub fn sqlite3_str_appendall(arg1: *mut sqlite3_str, zIn: *const ::std::os::raw::c_char);
}
extern "C" {
    pub fn sqlite3_str_appendchar(
        arg1: *mut sqlite3_str,
        N: ::std::os::raw::c_int,
        C: ::std::os::raw::c_char,
    );
}
extern "C" {
    pub fn sqlite3_str_reset(arg1: *mut sqlite3_str);
}
extern "C" {
    pub fn sqlite3_str_errcode(arg1: *mut sqlite3_str) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_str_length(arg1: *mut sqlite3_str) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_str_value(arg1: *mut sqlite3_str) -> *mut ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_status(
        op: ::std::os::raw::c_int,
        pCurrent: *mut ::std::os::raw::c_int,
        pHighwater: *mut ::std::os::raw::c_int,
        resetFlag: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_status64(
        op: ::std::os::raw::c_int,
        pCurrent: *mut sqlite3_int64,
        pHighwater: *mut sqlite3_int64,
        resetFlag: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_db_status(
        arg1: *mut sqlite3,
        op: ::std::os::raw::c_int,
        pCur: *mut ::std::os::raw::c_int,
        pHiwtr: *mut ::std::os::raw::c_int,
        resetFlg: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_stmt_status(
        arg1: *mut sqlite3_stmt,
        op: ::std::os::raw::c_int,
        resetFlg: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_pcache {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_pcache_page {
    pub pBuf: *mut ::std::os::raw::c_void,
    pub pExtra: *mut ::std::os::raw::c_void,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_pcache_methods2 {
    pub iVersion: ::std::os::raw::c_int,
    pub pArg: *mut ::std::os::raw::c_void,
    pub xInit: ::std::option::Option<
        unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void) -> ::std::os::raw::c_int,
    >,
    pub xShutdown: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
    pub xCreate: ::std::option::Option<
        unsafe extern "C" fn(
            szPage: ::std::os::raw::c_int,
            szExtra: ::std::os::raw::c_int,
            bPurgeable: ::std::os::raw::c_int,
        ) -> *mut sqlite3_pcache,
    >,
    pub xCachesize: ::std::option::Option<
        unsafe extern "C" fn(arg1: *mut sqlite3_pcache, nCachesize: ::std::os::raw::c_int),
    >,
    pub xPagecount: ::std::option::Option<
        unsafe extern "C" fn(arg1: *mut sqlite3_pcache) -> ::std::os::raw::c_int,
    >,
    pub xFetch: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_pcache,
            key: ::std::os::raw::c_uint,
            createFlag: ::std::os::raw::c_int,
        ) -> *mut sqlite3_pcache_page,
    >,
    pub xUnpin: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_pcache,
            arg2: *mut sqlite3_pcache_page,
            discard: ::std::os::raw::c_int,
        ),
    >,
    pub xRekey: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_pcache,
            arg2: *mut sqlite3_pcache_page,
            oldKey: ::std::os::raw::c_uint,
            newKey: ::std::os::raw::c_uint,
        ),
    >,
    pub xTruncate: ::std::option::Option<
        unsafe extern "C" fn(arg1: *mut sqlite3_pcache, iLimit: ::std::os::raw::c_uint),
    >,
    pub xDestroy: ::std::option::Option<unsafe extern "C" fn(arg1: *mut sqlite3_pcache)>,
    pub xShrink: ::std::option::Option<unsafe extern "C" fn(arg1: *mut sqlite3_pcache)>,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_pcache_methods {
    pub pArg: *mut ::std::os::raw::c_void,
    pub xInit: ::std::option::Option<
        unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void) -> ::std::os::raw::c_int,
    >,
    pub xShutdown: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
    pub xCreate: ::std::option::Option<
        unsafe extern "C" fn(
            szPage: ::std::os::raw::c_int,
            bPurgeable: ::std::os::raw::c_int,
        ) -> *mut sqlite3_pcache,
    >,
    pub xCachesize: ::std::option::Option<
        unsafe extern "C" fn(arg1: *mut sqlite3_pcache, nCachesize: ::std::os::raw::c_int),
    >,
    pub xPagecount: ::std::option::Option<
        unsafe extern "C" fn(arg1: *mut sqlite3_pcache) -> ::std::os::raw::c_int,
    >,
    pub xFetch: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_pcache,
            key: ::std::os::raw::c_uint,
            createFlag: ::std::os::raw::c_int,
        ) -> *mut ::std::os::raw::c_void,
    >,
    pub xUnpin: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_pcache,
            arg2: *mut ::std::os::raw::c_void,
            discard: ::std::os::raw::c_int,
        ),
    >,
    pub xRekey: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut sqlite3_pcache,
            arg2: *mut ::std::os::raw::c_void,
            oldKey: ::std::os::raw::c_uint,
            newKey: ::std::os::raw::c_uint,
        ),
    >,
    pub xTruncate: ::std::option::Option<
        unsafe extern "C" fn(arg1: *mut sqlite3_pcache, iLimit: ::std::os::raw::c_uint),
    >,
    pub xDestroy: ::std::option::Option<unsafe extern "C" fn(arg1: *mut sqlite3_pcache)>,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_backup {
    _unused: [u8; 0],
}
extern "C" {
    pub fn sqlite3_backup_init(
        pDest: *mut sqlite3,
        zDestName: *const ::std::os::raw::c_char,
        pSource: *mut sqlite3,
        zSourceName: *const ::std::os::raw::c_char,
    ) -> *mut sqlite3_backup;
}
extern "C" {
    pub fn sqlite3_backup_step(
        p: *mut sqlite3_backup,
        nPage: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_backup_finish(p: *mut sqlite3_backup) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_backup_remaining(p: *mut sqlite3_backup) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_backup_pagecount(p: *mut sqlite3_backup) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_unlock_notify(
        pBlocked: *mut sqlite3,
        xNotify: ::std::option::Option<
            unsafe extern "C" fn(
                apArg: *mut *mut ::std::os::raw::c_void,
                nArg: ::std::os::raw::c_int,
            ),
        >,
        pNotifyArg: *mut ::std::os::raw::c_void,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_stricmp(
        arg1: *const ::std::os::raw::c_char,
        arg2: *const ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_strnicmp(
        arg1: *const ::std::os::raw::c_char,
        arg2: *const ::std::os::raw::c_char,
        arg3: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_strglob(
        zGlob: *const ::std::os::raw::c_char,
        zStr: *const ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_strlike(
        zGlob: *const ::std::os::raw::c_char,
        zStr: *const ::std::os::raw::c_char,
        cEsc: ::std::os::raw::c_uint,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_log(
        iErrCode: ::std::os::raw::c_int,
        zFormat: *const ::std::os::raw::c_char,
        ...
    );
}
extern "C" {
    pub fn sqlite3_wal_hook(
        arg1: *mut sqlite3,
        arg2: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut ::std::os::raw::c_void,
                arg2: *mut sqlite3,
                arg3: *const ::std::os::raw::c_char,
                arg4: ::std::os::raw::c_int,
            ) -> ::std::os::raw::c_int,
        >,
        arg3: *mut ::std::os::raw::c_void,
    ) -> *mut ::std::os::raw::c_void;
}
extern "C" {
    pub fn sqlite3_wal_autocheckpoint(
        db: *mut sqlite3,
        N: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_wal_checkpoint(
        db: *mut sqlite3,
        zDb: *const ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_wal_checkpoint_v2(
        db: *mut sqlite3,
        zDb: *const ::std::os::raw::c_char,
        eMode: ::std::os::raw::c_int,
        pnLog: *mut ::std::os::raw::c_int,
        pnCkpt: *mut ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_vtab_config(
        arg1: *mut sqlite3,
        op: ::std::os::raw::c_int,
        ...
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_vtab_on_conflict(arg1: *mut sqlite3) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_vtab_nochange(arg1: *mut sqlite3_context) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_vtab_collation(
        arg1: *mut sqlite3_index_info,
        arg2: ::std::os::raw::c_int,
    ) -> *const ::std::os::raw::c_char;
}
extern "C" {
    pub fn sqlite3_vtab_distinct(arg1: *mut sqlite3_index_info) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_vtab_in(
        arg1: *mut sqlite3_index_info,
        iCons: ::std::os::raw::c_int,
        bHandle: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_vtab_in_first(
        pVal: *mut sqlite3_value,
        ppOut: *mut *mut sqlite3_value,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_vtab_in_next(
        pVal: *mut sqlite3_value,
        ppOut: *mut *mut sqlite3_value,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_vtab_rhs_value(
        arg1: *mut sqlite3_index_info,
        arg2: ::std::os::raw::c_int,
        ppVal: *mut *mut sqlite3_value,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_stmt_scanstatus(
        pStmt: *mut sqlite3_stmt,
        idx: ::std::os::raw::c_int,
        iScanStatusOp: ::std::os::raw::c_int,
        pOut: *mut ::std::os::raw::c_void,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_stmt_scanstatus_v2(
        pStmt: *mut sqlite3_stmt,
        idx: ::std::os::raw::c_int,
        iScanStatusOp: ::std::os::raw::c_int,
        flags: ::std::os::raw::c_int,
        pOut: *mut ::std::os::raw::c_void,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_stmt_scanstatus_reset(arg1: *mut sqlite3_stmt);
}
extern "C" {
    pub fn sqlite3_db_cacheflush(arg1: *mut sqlite3) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn libsql_close_hook(
        db: *mut sqlite3,
        xClose: ::std::option::Option<
            unsafe extern "C" fn(pCtx: *mut ::std::os::raw::c_void, db: *mut sqlite3),
        >,
        arg: *mut ::std::os::raw::c_void,
    ) -> *mut ::std::os::raw::c_void;
}
extern "C" {
    pub fn libsql_wal_disable_checkpoint(db: *mut sqlite3) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn libsql_wal_frame_count(
        arg1: *mut sqlite3,
        arg2: *mut ::std::os::raw::c_uint,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn libsql_wal_get_frame(
        arg1: *mut sqlite3,
        arg2: ::std::os::raw::c_uint,
        arg3: *mut ::std::os::raw::c_void,
        arg4: ::std::os::raw::c_uint,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn libsql_wal_insert_begin(arg1: *mut sqlite3) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn libsql_wal_insert_end(arg1: *mut sqlite3) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn libsql_wal_insert_frame(
        arg1: *mut sqlite3,
        arg2: ::std::os::raw::c_uint,
        arg3: *mut ::std::os::raw::c_void,
        arg4: ::std::os::raw::c_uint,
        arg5: *mut ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_system_errno(arg1: *mut sqlite3) -> ::std::os::raw::c_int;
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_snapshot {
    pub hidden: [::std::os::raw::c_uchar; 48usize],
}
extern "C" {
    pub fn sqlite3_snapshot_get(
        db: *mut sqlite3,
        zSchema: *const ::std::os::raw::c_char,
        ppSnapshot: *mut *mut sqlite3_snapshot,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_snapshot_open(
        db: *mut sqlite3,
        zSchema: *const ::std::os::raw::c_char,
        pSnapshot: *mut sqlite3_snapshot,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_snapshot_free(arg1: *mut sqlite3_snapshot);
}
extern "C" {
    pub fn sqlite3_snapshot_cmp(
        p1: *mut sqlite3_snapshot,
        p2: *mut sqlite3_snapshot,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_snapshot_recover(
        db: *mut sqlite3,
        zDb: *const ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn sqlite3_serialize(
        db: *mut sqlite3,
        zSchema: *const ::std::os::raw::c_char,
        piSize: *mut sqlite3_int64,
        mFlags: ::std::os::raw::c_uint,
    ) -> *mut ::std::os::raw::c_uchar;
}
extern "C" {
    pub fn sqlite3_deserialize(
        db: *mut sqlite3,
        zSchema: *const ::std::os::raw::c_char,
        pData: *mut ::std::os::raw::c_uchar,
        szDb: sqlite3_int64,
        szBuf: sqlite3_int64,
        mFlags: ::std::os::raw::c_uint,
    ) -> ::std::os::raw::c_int;
}
pub type sqlite3_rtree_dbl = f64;
extern "C" {
    pub fn sqlite3_rtree_geometry_callback(
        db: *mut sqlite3,
        zGeom: *const ::std::os::raw::c_char,
        xGeom: ::std::option::Option<
            unsafe extern "C" fn(
                arg1: *mut sqlite3_rtree_geometry,
                arg2: ::std::os::raw::c_int,
                arg3: *mut sqlite3_rtree_dbl,
                arg4: *mut ::std::os::raw::c_int,
            ) -> ::std::os::raw::c_int,
        >,
        pContext: *mut ::std::os::raw::c_void,
    ) -> ::std::os::raw::c_int;
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_rtree_geometry {
    pub pContext: *mut ::std::os::raw::c_void,
    pub nParam: ::std::os::raw::c_int,
    pub aParam: *mut sqlite3_rtree_dbl,
    pub pUser: *mut ::std::os::raw::c_void,
    pub xDelUser: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
}
extern "C" {
    pub fn sqlite3_rtree_query_callback(
        db: *mut sqlite3,
        zQueryFunc: *const ::std::os::raw::c_char,
        xQueryFunc: ::std::option::Option<
            unsafe extern "C" fn(arg1: *mut sqlite3_rtree_query_info) -> ::std::os::raw::c_int,
        >,
        pContext: *mut ::std::os::raw::c_void,
        xDestructor: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
    ) -> ::std::os::raw::c_int;
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_rtree_query_info {
    pub pContext: *mut ::std::os::raw::c_void,
    pub nParam: ::std::os::raw::c_int,
    pub aParam: *mut sqlite3_rtree_dbl,
    pub pUser: *mut ::std::os::raw::c_void,
    pub xDelUser: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
    pub aCoord: *mut sqlite3_rtree_dbl,
    pub anQueue: *mut ::std::os::raw::c_uint,
    pub nCoord: ::std::os::raw::c_int,
    pub iLevel: ::std::os::raw::c_int,
    pub mxLevel: ::std::os::raw::c_int,
    pub iRowid: sqlite3_int64,
    pub rParentScore: sqlite3_rtree_dbl,
    pub eParentWithin: ::std::os::raw::c_int,
    pub eWithin: ::std::os::raw::c_int,
    pub rScore: sqlite3_rtree_dbl,
    pub apSqlParam: *mut *mut sqlite3_value,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct Fts5Context {
    _unused: [u8; 0],
}
pub type fts5_extension_function = ::std::option::Option<
    unsafe extern "C" fn(
        pApi: *const Fts5ExtensionApi,
        pFts: *mut Fts5Context,
        pCtx: *mut sqlite3_context,
        nVal: ::std::os::raw::c_int,
        apVal: *mut *mut sqlite3_value,
    ),
>;
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct Fts5PhraseIter {
    pub a: *const ::std::os::raw::c_uchar,
    pub b: *const ::std::os::raw::c_uchar,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct Fts5ExtensionApi {
    pub iVersion: ::std::os::raw::c_int,
    pub xUserData: ::std::option::Option<
        unsafe extern "C" fn(arg1: *mut Fts5Context) -> *mut ::std::os::raw::c_void,
    >,
    pub xColumnCount: ::std::option::Option<
        unsafe extern "C" fn(arg1: *mut Fts5Context) -> ::std::os::raw::c_int,
    >,
    pub xRowCount: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut Fts5Context,
            pnRow: *mut sqlite3_int64,
        ) -> ::std::os::raw::c_int,
    >,
    pub xColumnTotalSize: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut Fts5Context,
            iCol: ::std::os::raw::c_int,
            pnToken: *mut sqlite3_int64,
        ) -> ::std::os::raw::c_int,
    >,
    pub xTokenize: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut Fts5Context,
            pText: *const ::std::os::raw::c_char,
            nText: ::std::os::raw::c_int,
            pCtx: *mut ::std::os::raw::c_void,
            xToken: ::std::option::Option<
                unsafe extern "C" fn(
                    arg1: *mut ::std::os::raw::c_void,
                    arg2: ::std::os::raw::c_int,
                    arg3: *const ::std::os::raw::c_char,
                    arg4: ::std::os::raw::c_int,
                    arg5: ::std::os::raw::c_int,
                    arg6: ::std::os::raw::c_int,
                ) -> ::std::os::raw::c_int,
            >,
        ) -> ::std::os::raw::c_int,
    >,
    pub xPhraseCount: ::std::option::Option<
        unsafe extern "C" fn(arg1: *mut Fts5Context) -> ::std::os::raw::c_int,
    >,
    pub xPhraseSize: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut Fts5Context,
            iPhrase: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub xInstCount: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut Fts5Context,
            pnInst: *mut ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub xInst: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut Fts5Context,
            iIdx: ::std::os::raw::c_int,
            piPhrase: *mut ::std::os::raw::c_int,
            piCol: *mut ::std::os::raw::c_int,
            piOff: *mut ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub xRowid:
        ::std::option::Option<unsafe extern "C" fn(arg1: *mut Fts5Context) -> sqlite3_int64>,
    pub xColumnText: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut Fts5Context,
            iCol: ::std::os::raw::c_int,
            pz: *mut *const ::std::os::raw::c_char,
            pn: *mut ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub xColumnSize: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut Fts5Context,
            iCol: ::std::os::raw::c_int,
            pnToken: *mut ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub xQueryPhrase: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut Fts5Context,
            iPhrase: ::std::os::raw::c_int,
            pUserData: *mut ::std::os::raw::c_void,
            arg2: ::std::option::Option<
                unsafe extern "C" fn(
                    arg1: *const Fts5ExtensionApi,
                    arg2: *mut Fts5Context,
                    arg3: *mut ::std::os::raw::c_void,
                ) -> ::std::os::raw::c_int,
            >,
        ) -> ::std::os::raw::c_int,
    >,
    pub xSetAuxdata: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut Fts5Context,
            pAux: *mut ::std::os::raw::c_void,
            xDelete: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
        ) -> ::std::os::raw::c_int,
    >,
    pub xGetAuxdata: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut Fts5Context,
            bClear: ::std::os::raw::c_int,
        ) -> *mut ::std::os::raw::c_void,
    >,
    pub xPhraseFirst: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut Fts5Context,
            iPhrase: ::std::os::raw::c_int,
            arg2: *mut Fts5PhraseIter,
            arg3: *mut ::std::os::raw::c_int,
            arg4: *mut ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub xPhraseNext: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut Fts5Context,
            arg2: *mut Fts5PhraseIter,
            piCol: *mut ::std::os::raw::c_int,
            piOff: *mut ::std::os::raw::c_int,
        ),
    >,
    pub xPhraseFirstColumn: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut Fts5Context,
            iPhrase: ::std::os::raw::c_int,
            arg2: *mut Fts5PhraseIter,
            arg3: *mut ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub xPhraseNextColumn: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut Fts5Context,
            arg2: *mut Fts5PhraseIter,
            piCol: *mut ::std::os::raw::c_int,
        ),
    >,
    pub xQueryToken: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut Fts5Context,
            iPhrase: ::std::os::raw::c_int,
            iToken: ::std::os::raw::c_int,
            ppToken: *mut *const ::std::os::raw::c_char,
            pnToken: *mut ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub xInstToken: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut Fts5Context,
            iIdx: ::std::os::raw::c_int,
            iToken: ::std::os::raw::c_int,
            arg2: *mut *const ::std::os::raw::c_char,
            arg3: *mut ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct Fts5Tokenizer {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct fts5_tokenizer {
    pub xCreate: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut ::std::os::raw::c_void,
            azArg: *mut *const ::std::os::raw::c_char,
            nArg: ::std::os::raw::c_int,
            ppOut: *mut *mut Fts5Tokenizer,
        ) -> ::std::os::raw::c_int,
    >,
    pub xDelete: ::std::option::Option<unsafe extern "C" fn(arg1: *mut Fts5Tokenizer)>,
    pub xTokenize: ::std::option::Option<
        unsafe extern "C" fn(
            arg1: *mut Fts5Tokenizer,
            pCtx: *mut ::std::os::raw::c_void,
            flags: ::std::os::raw::c_int,
            pText: *const ::std::os::raw::c_char,
            nText: ::std::os::raw::c_int,
            xToken: ::std::option::Option<
                unsafe extern "C" fn(
                    pCtx: *mut ::std::os::raw::c_void,
                    tflags: ::std::os::raw::c_int,
                    pToken: *const ::std::os::raw::c_char,
                    nToken: ::std::os::raw::c_int,
                    iStart: ::std::os::raw::c_int,
                    iEnd: ::std::os::raw::c_int,
                ) -> ::std::os::raw::c_int,
            >,
        ) -> ::std::os::raw::c_int,
    >,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct fts5_api {
    pub iVersion: ::std::os::raw::c_int,
    pub xCreateTokenizer: ::std::option::Option<
        unsafe extern "C" fn(
            pApi: *mut fts5_api,
            zName: *const ::std::os::raw::c_char,
            pUserData: *mut ::std::os::raw::c_void,
            pTokenizer: *mut fts5_tokenizer,
            xDestroy: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
        ) -> ::std::os::raw::c_int,
    >,
    pub xFindTokenizer: ::std::option::Option<
        unsafe extern "C" fn(
            pApi: *mut fts5_api,
            zName: *const ::std::os::raw::c_char,
            ppUserData: *mut *mut ::std::os::raw::c_void,
            pTokenizer: *mut fts5_tokenizer,
        ) -> ::std::os::raw::c_int,
    >,
    pub xCreateFunction: ::std::option::Option<
        unsafe extern "C" fn(
            pApi: *mut fts5_api,
            zName: *const ::std::os::raw::c_char,
            pUserData: *mut ::std::os::raw::c_void,
            xFunction: fts5_extension_function,
            xDestroy: ::std::option::Option<unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void)>,
        ) -> ::std::os::raw::c_int,
    >,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct Pager {
    _unused: [u8; 0],
}
pub type PgHdr = libsql_pghdr;
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct PCache {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct libsql_pghdr {
    pub pPage: *mut sqlite3_pcache_page,
    pub pData: *mut ::std::os::raw::c_void,
    pub pExtra: *mut ::std::os::raw::c_void,
    pub pCache: *mut PCache,
    pub pDirty: *mut PgHdr,
    pub pPager: *mut Pager,
    pub pgno: ::std::os::raw::c_uint,
    pub pageHash: ::std::os::raw::c_uint,
    pub flags: ::std::os::raw::c_ushort,
    #[doc = " Elements above, except pCache, are public.  All that follow are\n private to pcache.c and should not be accessed by other modules.\n pCache is grouped with the public elements for efficiency."]
    pub nRef: ::std::os::raw::c_ulonglong,
    pub pDirtyNext: *mut PgHdr,
    pub pDirtyPrev: *mut PgHdr,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct wal_impl {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct wal_manager_impl {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct libsql_wal_methods {
    pub iVersion: ::std::os::raw::c_int,
    pub xLimit: ::std::option::Option<
        unsafe extern "C" fn(pWal: *mut wal_impl, limit: ::std::os::raw::c_longlong),
    >,
    pub xBeginReadTransaction: ::std::option::Option<
        unsafe extern "C" fn(
            pWal: *mut wal_impl,
            arg1: *mut ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub xEndReadTransaction: ::std::option::Option<unsafe extern "C" fn(arg1: *mut wal_impl)>,
    pub xFindFrame: ::std::option::Option<
        unsafe extern "C" fn(
            pWal: *mut wal_impl,
            arg1: ::std::os::raw::c_uint,
            arg2: *mut ::std::os::raw::c_uint,
        ) -> ::std::os::raw::c_int,
    >,
    pub xReadFrame: ::std::option::Option<
        unsafe extern "C" fn(
            pWal: *mut wal_impl,
            arg1: ::std::os::raw::c_uint,
            arg2: ::std::os::raw::c_int,
            arg3: *mut ::std::os::raw::c_uchar,
        ) -> ::std::os::raw::c_int,
    >,
    pub xReadFrameRaw: ::std::option::Option<
        unsafe extern "C" fn(
            pWal: *mut wal_impl,
            arg1: ::std::os::raw::c_uint,
            arg2: ::std::os::raw::c_int,
            arg3: *mut ::std::os::raw::c_uchar,
        ) -> ::std::os::raw::c_int,
    >,
    pub xDbsize:
        ::std::option::Option<unsafe extern "C" fn(pWal: *mut wal_impl) -> ::std::os::raw::c_uint>,
    pub xBeginWriteTransaction:
        ::std::option::Option<unsafe extern "C" fn(pWal: *mut wal_impl) -> ::std::os::raw::c_int>,
    pub xEndWriteTransaction:
        ::std::option::Option<unsafe extern "C" fn(pWal: *mut wal_impl) -> ::std::os::raw::c_int>,
    pub xUndo: ::std::option::Option<
        unsafe extern "C" fn(
            pWal: *mut wal_impl,
            xUndo: ::std::option::Option<
                unsafe extern "C" fn(
                    arg1: *mut ::std::os::raw::c_void,
                    arg2: ::std::os::raw::c_uint,
                ) -> ::std::os::raw::c_int,
            >,
            pUndoCtx: *mut ::std::os::raw::c_void,
        ) -> ::std::os::raw::c_int,
    >,
    pub xSavepoint: ::std::option::Option<
        unsafe extern "C" fn(pWal: *mut wal_impl, aWalData: *mut ::std::os::raw::c_uint),
    >,
    pub xSavepointUndo: ::std::option::Option<
        unsafe extern "C" fn(
            pWal: *mut wal_impl,
            aWalData: *mut ::std::os::raw::c_uint,
        ) -> ::std::os::raw::c_int,
    >,
    pub xFrameCount: ::std::option::Option<
        unsafe extern "C" fn(
            pWal: *mut wal_impl,
            arg1: ::std::os::raw::c_int,
            arg2: *mut ::std::os::raw::c_uint,
        ) -> ::std::os::raw::c_int,
    >,
    pub xFrames: ::std::option::Option<
        unsafe extern "C" fn(
            pWal: *mut wal_impl,
            arg1: ::std::os::raw::c_int,
            arg2: *mut libsql_pghdr,
            arg3: ::std::os::raw::c_uint,
            arg4: ::std::os::raw::c_int,
            arg5: ::std::os::raw::c_int,
            arg6: *mut ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub xCheckpoint: ::std::option::Option<
        unsafe extern "C" fn(
            pWal: *mut wal_impl,
            db: *mut sqlite3,
            eMode: ::std::os::raw::c_int,
            xBusy: ::std::option::Option<
                unsafe extern "C" fn(arg1: *mut ::std::os::raw::c_void) -> ::std::os::raw::c_int,
            >,
            pBusyArg: *mut ::std::os::raw::c_void,
            sync_flags: ::std::os::raw::c_int,
            nBuf: ::std::os::raw::c_int,
            zBuf: *mut ::std::os::raw::c_uchar,
            pnLog: *mut ::std::os::raw::c_int,
            pnCkpt: *mut ::std::os::raw::c_int,
            xCb: ::std::option::Option<
                unsafe extern "C" fn(
                    pCbData: *mut ::std::os::raw::c_void,
                    mxSafeFrame: ::std::os::raw::c_int,
                    pPage: *const ::std::os::raw::c_uchar,
                    nPage: ::std::os::raw::c_int,
                    page_no: ::std::os::raw::c_int,
                    frame_no: ::std::os::raw::c_int,
                ) -> ::std::os::raw::c_int,
            >,
            pCbData: *mut ::std::os::raw::c_void,
        ) -> ::std::os::raw::c_int,
    >,
    pub xCallback:
        ::std::option::Option<unsafe extern "C" fn(pWal: *mut wal_impl) -> ::std::os::raw::c_int>,
    pub xExclusiveMode: ::std::option::Option<
        unsafe extern "C" fn(
            pWal: *mut wal_impl,
            op: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub xHeapMemory:
        ::std::option::Option<unsafe extern "C" fn(pWal: *mut wal_impl) -> ::std::os::raw::c_int>,
    pub xSnapshotGet: ::std::option::Option<
        unsafe extern "C" fn(
            pWal: *mut wal_impl,
            ppSnapshot: *mut *mut sqlite3_snapshot,
        ) -> ::std::os::raw::c_int,
    >,
    pub xSnapshotOpen: ::std::option::Option<
        unsafe extern "C" fn(pWal: *mut wal_impl, pSnapshot: *mut sqlite3_snapshot),
    >,
    pub xSnapshotRecover:
        ::std::option::Option<unsafe extern "C" fn(pWal: *mut wal_impl) -> ::std::os::raw::c_int>,
    pub xSnapshotCheck: ::std::option::Option<
        unsafe extern "C" fn(
            pWal: *mut ::std::os::raw::c_void,
            pSnapshot: *mut sqlite3_snapshot,
        ) -> ::std::os::raw::c_int,
    >,
    pub xSnapshotUnlock: ::std::option::Option<unsafe extern "C" fn(pWal: *mut wal_impl)>,
    pub xFramesize:
        ::std::option::Option<unsafe extern "C" fn(pWal: *mut wal_impl) -> ::std::os::raw::c_int>,
    pub xFile:
        ::std::option::Option<unsafe extern "C" fn(pWal: *mut wal_impl) -> *mut sqlite3_file>,
    pub xWriteLock: ::std::option::Option<
        unsafe extern "C" fn(
            pWal: *mut wal_impl,
            bLock: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub xDb: ::std::option::Option<unsafe extern "C" fn(pWal: *mut wal_impl, db: *mut sqlite3)>,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct WalIterator {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct WalCkptInfo {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct WalIndexHdr {
    pub iVersion: ::std::os::raw::c_uint,
    pub unused: ::std::os::raw::c_uint,
    pub iChange: ::std::os::raw::c_uint,
    pub isInit: ::std::os::raw::c_uchar,
    pub bigEndCksum: ::std::os::raw::c_uchar,
    pub szPage: ::std::os::raw::c_ushort,
    pub mxFrame: ::std::os::raw::c_uint,
    pub nPage: ::std::os::raw::c_uint,
    pub aFrameCksum: [::std::os::raw::c_uint; 2usize],
    pub aSalt: [::std::os::raw::c_uint; 2usize],
    pub aCksum: [::std::os::raw::c_uint; 2usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct libsql_wal_manager {
    pub bUsesShm: ::std::os::raw::c_int,
    pub xOpen: ::std::option::Option<
        unsafe extern "C" fn(
            pData: *mut wal_manager_impl,
            arg1: *mut sqlite3_vfs,
            arg2: *mut sqlite3_file,
            no_shm_mode: ::std::os::raw::c_int,
            max_size: ::std::os::raw::c_longlong,
            zMainDbFileName: *const ::std::os::raw::c_char,
            out_wal: *mut libsql_wal,
        ) -> ::std::os::raw::c_int,
    >,
    pub xClose: ::std::option::Option<
        unsafe extern "C" fn(
            pData: *mut wal_manager_impl,
            pWal: *mut wal_impl,
            db: *mut sqlite3,
            sync_flags: ::std::os::raw::c_int,
            nBuf: ::std::os::raw::c_int,
            zBuf: *mut ::std::os::raw::c_uchar,
        ) -> ::std::os::raw::c_int,
    >,
    pub xLogDestroy: ::std::option::Option<
        unsafe extern "C" fn(
            pData: *mut wal_manager_impl,
            vfs: *mut sqlite3_vfs,
            zMainDbFileName: *const ::std::os::raw::c_char,
        ) -> ::std::os::raw::c_int,
    >,
    pub xLogExists: ::std::option::Option<
        unsafe extern "C" fn(
            pData: *mut wal_manager_impl,
            vfs: *mut sqlite3_vfs,
            zMainDbFileName: *const ::std::os::raw::c_char,
            exist: *mut ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub xDestroy: ::std::option::Option<unsafe extern "C" fn(pData: *mut wal_manager_impl)>,
    pub pData: *mut wal_manager_impl,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sqlite3_wal {
    pub pVfs: *mut sqlite3_vfs,
    pub pDbFd: *mut sqlite3_file,
    pub pWalFd: *mut sqlite3_file,
    pub iCallback: ::std::os::raw::c_uint,
    pub mxWalSize: ::std::os::raw::c_longlong,
    pub nWiData: ::std::os::raw::c_int,
    pub szFirstBlock: ::std::os::raw::c_int,
    pub apWiData: *mut *mut ::std::os::raw::c_uint,
    pub szPage: ::std::os::raw::c_uint,
    pub readLock: ::std::os::raw::c_short,
    pub syncFlags: ::std::os::raw::c_uchar,
    pub exclusiveMode: ::std::os::raw::c_uchar,
    pub writeLock: ::std::os::raw::c_uchar,
    pub ckptLock: ::std::os::raw::c_uchar,
    pub readOnly: ::std::os::raw::c_uchar,
    pub truncateOnCommit: ::std::os::raw::c_uchar,
    pub syncHeader: ::std::os::raw::c_uchar,
    pub padToSectorBoundary: ::std::os::raw::c_uchar,
    pub bShmUnreliable: ::std::os::raw::c_uchar,
    pub hdr: WalIndexHdr,
    pub minFrame: ::std::os::raw::c_uint,
    pub iReCksum: ::std::os::raw::c_uint,
    pub zWalName: *const ::std::os::raw::c_char,
    pub nCkpt: ::std::os::raw::c_uint,
    pub lockError: ::std::os::raw::c_uchar,
    pub pSnapshot: *mut WalIndexHdr,
    pub db: *mut sqlite3,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct libsql_wal {
    pub methods: libsql_wal_methods,
    pub pData: *mut wal_impl,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct RefCountedWalManager {
    pub n: ::std::os::raw::c_int,
    pub ref_: libsql_wal_manager,
    pub is_static: ::std::os::raw::c_int,
}
extern "C" {
    pub fn make_ref_counted_wal_manager(
        wal_manager: libsql_wal_manager,
        out: *mut *mut RefCountedWalManager,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn destroy_wal_manager(p: *mut RefCountedWalManager);
}
extern "C" {
    pub fn clone_wal_manager(p: *mut RefCountedWalManager) -> *mut RefCountedWalManager;
}
extern "C" {
    pub fn make_sqlite3_wal_manager_rc() -> *mut RefCountedWalManager;
}
extern "C" {
    pub static sqlite3_wal_manager: libsql_wal_manager;
}
pub type __builtin_va_list = [__va_list_tag; 1usize];
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct __va_list_tag {
    pub gp_offset: ::std::os::raw::c_uint,
    pub fp_offset: ::std::os::raw::c_uint,
    pub overflow_arg_area: *mut ::std::os::raw::c_void,
    pub reg_save_area: *mut ::std::os::raw::c_void,
}
