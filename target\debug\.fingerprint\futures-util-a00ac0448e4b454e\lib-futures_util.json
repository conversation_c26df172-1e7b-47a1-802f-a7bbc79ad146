{"rustc": 3062648155896360161, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 13318305459243126790, "path": 790360863344208173, "deps": [[5103565458935487, "futures_io", false, 4854603049200602500], [1615478164327904835, "pin_utils", false, 10722408344375330206], [1811549171721445101, "futures_channel", false, 15061169904035865039], [1906322745568073236, "pin_project_lite", false, 6270632195488232726], [7013762810557009322, "futures_sink", false, 1207570865710432512], [7620660491849607393, "futures_core", false, 17862481366668668731], [10565019901765856648, "futures_macro", false, 17293502026761764300], [14767213526276824509, "slab", false, 16262876883517085424], [15932120279885307830, "memchr", false, 9127042864190067382], [16240732885093539806, "futures_task", false, 11016313434737999709]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-a00ac0448e4b454e\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}