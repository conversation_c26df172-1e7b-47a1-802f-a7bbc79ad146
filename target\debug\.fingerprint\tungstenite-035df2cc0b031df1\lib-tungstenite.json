{"rustc": 3062648155896360161, "features": "[\"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"sha1\"]", "declared_features": "[\"__rustls-tls\", \"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"sha1\", \"url\", \"webpki-roots\"]", "target": 5395530797274129873, "profile": 15657897354478470176, "path": 15399248213591249420, "deps": [[99287295355353247, "data_encoding", false, 15628873110510137915], [4359956005902820838, "utf8", false, 797263378476382774], [4537297827336760846, "thiserror", false, 2723179682894236067], [6163892036024256188, "httparse", false, 10257830529449848117], [9010263965687315507, "http", false, 5534069023319847022], [10724389056617919257, "sha1", false, 7959825829923364613], [11916940916964035392, "rand", false, 2140904830931925307], [13066042571740262168, "log", false, 13344303879308748865], [16066129441945555748, "bytes", false, 8148076815200774068]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tungstenite-035df2cc0b031df1\\dep-lib-tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}