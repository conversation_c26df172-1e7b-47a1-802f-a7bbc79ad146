{"rustc": 3062648155896360161, "features": "[\"lib_has_std\"]", "declared_features": "[\"alloc\", \"clippy\", \"lib_has_std\"]", "target": 15229808779680689443, "profile": 2225463790103693989, "path": 14366782794919834012, "deps": [[4003231138667150418, "derive_builder_core", false, 2710006666784067169], [17332570067994900305, "syn", false, 3020629385477819389]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\derive_builder_macro-37ca4d649117ff15\\dep-lib-derive_builder_macro", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}