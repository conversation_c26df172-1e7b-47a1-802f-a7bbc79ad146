{"rustc": 3062648155896360161, "features": "[]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 3165595516910038244, "profile": 15657897354478470176, "path": 1432488132545093637, "deps": [[784494742817713399, "tower_service", false, 15712931872650505409], [4405182208873388884, "http", false, 10314525203348988149], [7712452662827335977, "tower_layer", false, 525772972084167676], [8915503303801890683, "http_body", false, 3165026523775083827], [9293824762099617471, "build_script_build", false, 6590307493434126404], [10229185211513642314, "mime", false, 17643779385371687159], [10629569228670356391, "futures_util", false, 6524679691479467602], [16066129441945555748, "bytes", false, 8148076815200774068], [16611674984963787466, "async_trait", false, 11388465789347159649]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\axum-core-c8e49d428d1dd0af\\dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}