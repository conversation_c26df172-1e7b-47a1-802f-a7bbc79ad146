{"rustc": 3062648155896360161, "features": "[\"default\", \"logging\", \"prettyplease\", \"runtime\", \"which-rustfmt\"]", "declared_features": "[\"__cli\", \"__testing_only_extra_assertions\", \"__testing_only_libclang_5\", \"__testing_only_libclang_9\", \"default\", \"experimental\", \"logging\", \"prettyplease\", \"runtime\", \"static\", \"which-rustfmt\"]", "target": 15460903241111225995, "profile": 2225463790103693989, "path": 12763429654918952640, "deps": [[373107762698212489, "proc_macro2", false, 16965594641470688371], [503635761244294217, "regex", false, 17651452073308353053], [950716570147248582, "cexpr", false, 14030438573192587104], [2004958070545769120, "lazycell", false, 859737179613418584], [4885725550624711673, "clang_sys", false, 13704636113361465132], [5359720273228040123, "build_script_build", false, 18142283047845928001], [6243494903393190189, "which", false, 13580566748985692943], [8410525223747752176, "shlex", false, 16404260931847984269], [9423015880379144908, "prettyplease", false, 13119819533696014718], [11863159202453368486, "peeking_take_while", false, 10440246636167849492], [12848154260885479101, "bitflags", false, 2266399611250280020], [13066042571740262168, "log", false, 13344303879308748865], [16055916053474393816, "rustc_hash", false, 6285228602091759681], [17332570067994900305, "syn", false, 3020629385477819389], [17917672826516349275, "lazy_static", false, 17223233478446441159], [17990358020177143287, "quote", false, 4770973289583101107]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\bindgen-7b1c72e38691799a\\dep-lib-bindgen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}